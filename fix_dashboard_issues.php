<?php
/**
 * إصلاح مشاكل لوحة تحكم المدرب
 * Fix instructor dashboard issues
 */

require_once 'config/database.php';

echo "<!DOCTYPE html>";
echo "<html dir='rtl' lang='ar'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>إصلاح مشاكل لوحة تحكم المدرب</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "</head>";
echo "<body>";
echo "<div class='container mt-4'>";
echo "<h2>🔧 إصلاح مشاكل لوحة تحكم المدرب</h2>";

try {
    // 1. فحص وإصلاح جدول sessions
    echo "<h4>📅 فحص جدول الجلسات</h4>";
    
    // التحقق من وجود الأعمدة المطلوبة
    $stmt = $conn->query("SHOW COLUMNS FROM sessions");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $required_columns = ['session_date', 'start_time', 'end_time', 'duration'];
    $missing_columns = [];
    
    foreach ($required_columns as $col) {
        if (!in_array($col, $columns)) {
            $missing_columns[] = $col;
        }
    }
    
    if (!empty($missing_columns)) {
        echo "<div class='alert alert-warning'>⚠️ أعمدة مفقودة: " . implode(', ', $missing_columns) . "</div>";
        
        // إضافة الأعمدة المفقودة
        foreach ($missing_columns as $col) {
            switch ($col) {
                case 'session_date':
                    $conn->exec("ALTER TABLE sessions ADD COLUMN session_date DATE DEFAULT NULL AFTER course_id");
                    echo "<div class='alert alert-success'>✅ تم إضافة عمود session_date</div>";
                    break;
                case 'start_time':
                    $conn->exec("ALTER TABLE sessions ADD COLUMN start_time TIME DEFAULT NULL AFTER session_date");
                    echo "<div class='alert alert-success'>✅ تم إضافة عمود start_time</div>";
                    break;
                case 'end_time':
                    $conn->exec("ALTER TABLE sessions ADD COLUMN end_time TIME DEFAULT NULL AFTER start_time");
                    echo "<div class='alert alert-success'>✅ تم إضافة عمود end_time</div>";
                    break;
                case 'duration':
                    $conn->exec("ALTER TABLE sessions ADD COLUMN duration INT DEFAULT 60 AFTER end_time");
                    echo "<div class='alert alert-success'>✅ تم إضافة عمود duration</div>";
                    break;
            }
        }
    } else {
        echo "<div class='alert alert-info'>ℹ️ جميع الأعمدة المطلوبة موجودة</div>";
    }

    // 2. تحديث الجلسات الموجودة بتواريخ وأوقات صحيحة
    echo "<h4>⏰ تحديث بيانات الجلسات</h4>";
    
    $stmt = $conn->query("SELECT id, title FROM sessions WHERE session_date IS NULL OR start_time IS NULL");
    $sessions_to_update = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($sessions_to_update)) {
        $updated_count = 0;
        
        foreach ($sessions_to_update as $session) {
            // إنشاء تاريخ ووقت عشوائي للجلسة
            $future_date = date('Y-m-d', strtotime('+' . rand(1, 30) . ' days'));
            $start_hour = rand(9, 18); // من 9 صباحاً إلى 6 مساءً
            $start_minute = rand(0, 3) * 15; // 0, 15, 30, 45
            $start_time = sprintf('%02d:%02d:00', $start_hour, $start_minute);
            
            $duration = [60, 90, 120][rand(0, 2)]; // 60, 90, أو 120 دقيقة
            $end_time = date('H:i:s', strtotime($start_time . ' +' . $duration . ' minutes'));
            
            $stmt = $conn->prepare("
                UPDATE sessions 
                SET session_date = ?, start_time = ?, end_time = ?, duration = ?
                WHERE id = ?
            ");
            $stmt->execute([$future_date, $start_time, $end_time, $duration, $session['id']]);
            
            $updated_count++;
            echo "<div class='alert alert-success'>✅ تم تحديث الجلسة: " . htmlspecialchars($session['title']) . " - $future_date $start_time</div>";
        }
        
        echo "<div class='alert alert-info'>ℹ️ تم تحديث $updated_count جلسة</div>";
    } else {
        echo "<div class='alert alert-info'>ℹ️ جميع الجلسات محدثة</div>";
    }

    // 3. إنشاء جلسات تجريبية إضافية
    echo "<h4>📚 إنشاء جلسات تجريبية</h4>";
    
    $stmt = $conn->query("SELECT id, title FROM courses WHERE status = 'active' LIMIT 3");
    $courses = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $sessions_created = 0;
    
    foreach ($courses as $course) {
        // إنشاء 3 جلسات لكل كورس
        $session_templates = [
            ['المحاضرة الأولى - مقدمة', 'مقدمة شاملة عن الكورس والأهداف'],
            ['المحاضرة الثانية - الأساسيات', 'شرح المفاهيم الأساسية والمبادئ'],
            ['المحاضرة الثالثة - التطبيق العملي', 'تطبيق عملي على ما تم تعلمه']
        ];
        
        foreach ($session_templates as $index => $template) {
            // التحقق من عدم وجود جلسة بنفس العنوان
            $stmt = $conn->prepare("SELECT id FROM sessions WHERE course_id = ? AND title = ?");
            $stmt->execute([$course['id'], $template[0]]);
            
            if (!$stmt->fetch()) {
                $future_date = date('Y-m-d', strtotime('+' . ($index + 1) . ' weeks'));
                $start_hour = rand(10, 16);
                $start_minute = rand(0, 3) * 15;
                $start_time = sprintf('%02d:%02d:00', $start_hour, $start_minute);
                
                $duration = [60, 90, 120][rand(0, 2)];
                $end_time = date('H:i:s', strtotime($start_time . ' +' . $duration . ' minutes'));
                
                $meeting_link = "https://zoom.us/j/" . rand(1000000000, 9999999999) . "?pwd=" . bin2hex(random_bytes(8));
                
                $stmt = $conn->prepare("
                    INSERT INTO sessions (course_id, title, description, session_date, start_time, end_time, duration, meeting_link, status, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'scheduled', NOW())
                ");
                $stmt->execute([
                    $course['id'],
                    $template[0],
                    $template[1],
                    $future_date,
                    $start_time,
                    $end_time,
                    $duration,
                    $meeting_link
                ]);
                
                $sessions_created++;
                echo "<div class='alert alert-success'>✅ تم إنشاء جلسة: " . htmlspecialchars($template[0]) . " للكورس: " . htmlspecialchars($course['title']) . "</div>";
            }
        }
    }
    
    if ($sessions_created == 0) {
        echo "<div class='alert alert-info'>ℹ️ الجلسات التجريبية موجودة مسبقاً</div>";
    }

    // 4. فحص جدول session_attendance
    echo "<h4>👥 فحص جدول حضور الجلسات</h4>";
    
    $stmt = $conn->query("SHOW TABLES LIKE 'session_attendance'");
    if ($stmt->rowCount() == 0) {
        $conn->exec("
            CREATE TABLE session_attendance (
                id INT AUTO_INCREMENT PRIMARY KEY,
                session_id INT NOT NULL,
                student_id INT NOT NULL,
                attendance_status ENUM('present', 'absent', 'late') DEFAULT 'present',
                join_time TIMESTAMP NULL,
                leave_time TIMESTAMP NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                
                INDEX idx_session_id (session_id),
                INDEX idx_student_id (student_id),
                
                FOREIGN KEY (session_id) REFERENCES sessions(id) ON DELETE CASCADE,
                FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
                
                UNIQUE KEY unique_session_student (session_id, student_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        echo "<div class='alert alert-success'>✅ تم إنشاء جدول حضور الجلسات</div>";
    } else {
        echo "<div class='alert alert-info'>ℹ️ جدول حضور الجلسات موجود</div>";
    }

    // 5. إضافة بيانات حضور تجريبية
    echo "<h4>📊 إضافة بيانات حضور تجريبية</h4>";
    
    $stmt = $conn->query("
        SELECT s.id as session_id, ce.student_id
        FROM sessions s
        JOIN courses c ON s.course_id = c.id
        JOIN course_enrollments ce ON c.id = ce.course_id
        WHERE ce.status = 'active'
        LIMIT 20
    ");
    $attendance_data = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $attendance_added = 0;
    
    foreach ($attendance_data as $data) {
        $stmt = $conn->prepare("SELECT id FROM session_attendance WHERE session_id = ? AND student_id = ?");
        $stmt->execute([$data['session_id'], $data['student_id']]);
        
        if (!$stmt->fetch()) {
            $attendance_status = ['present', 'present', 'present', 'late', 'absent'][rand(0, 4)]; // 60% حضور
            
            $stmt = $conn->prepare("
                INSERT INTO session_attendance (session_id, student_id, attendance_status, join_time, created_at)
                VALUES (?, ?, ?, NOW(), NOW())
            ");
            $stmt->execute([$data['session_id'], $data['student_id'], $attendance_status]);
            $attendance_added++;
        }
    }
    
    if ($attendance_added > 0) {
        echo "<div class='alert alert-success'>✅ تم إضافة $attendance_added سجل حضور</div>";
    } else {
        echo "<div class='alert alert-info'>ℹ️ بيانات الحضور موجودة مسبقاً</div>";
    }

    // 6. إحصائيات النظام المحدثة
    echo "<h4>📈 إحصائيات النظام المحدثة</h4>";
    
    $stmt = $conn->query("SELECT COUNT(*) FROM sessions WHERE session_date >= CURDATE()");
    $upcoming_sessions = $stmt->fetchColumn();
    
    $stmt = $conn->query("SELECT COUNT(*) FROM sessions WHERE session_date < CURDATE()");
    $past_sessions = $stmt->fetchColumn();
    
    $stmt = $conn->query("SELECT COUNT(*) FROM session_attendance");
    $total_attendance = $stmt->fetchColumn();
    
    $stmt = $conn->query("SELECT COUNT(DISTINCT student_id) FROM course_enrollments WHERE status = 'active'");
    $active_students = $stmt->fetchColumn();
    
    echo "<div class='row'>";
    
    echo "<div class='col-md-3'>";
    echo "<div class='card bg-primary text-white'>";
    echo "<div class='card-body text-center'>";
    echo "<h3>$upcoming_sessions</h3>";
    echo "<p class='mb-0'>جلسات قادمة</p>";
    echo "</div></div></div>";
    
    echo "<div class='col-md-3'>";
    echo "<div class='card bg-success text-white'>";
    echo "<div class='card-body text-center'>";
    echo "<h3>$past_sessions</h3>";
    echo "<p class='mb-0'>جلسات مكتملة</p>";
    echo "</div></div></div>";
    
    echo "<div class='col-md-3'>";
    echo "<div class='card bg-info text-white'>";
    echo "<div class='card-body text-center'>";
    echo "<h3>$total_attendance</h3>";
    echo "<p class='mb-0'>سجلات حضور</p>";
    echo "</div></div></div>";
    
    echo "<div class='col-md-3'>";
    echo "<div class='card bg-warning text-white'>";
    echo "<div class='card-body text-center'>";
    echo "<h3>$active_students</h3>";
    echo "<p class='mb-0'>طلاب نشطين</p>";
    echo "</div></div></div>";
    
    echo "</div>";

    echo "<div class='alert alert-success mt-4'>";
    echo "<h5>🎉 تم إصلاح جميع المشاكل بنجاح!</h5>";
    echo "<ul class='mb-0'>";
    echo "<li>✅ تم إصلاح جدول الجلسات</li>";
    echo "<li>✅ تم تحديث بيانات الجلسات</li>";
    echo "<li>✅ تم إنشاء جلسات تجريبية</li>";
    echo "<li>✅ تم إنشاء جدول الحضور</li>";
    echo "<li>✅ تم إضافة بيانات حضور تجريبية</li>";
    echo "</ul>";
    echo "</div>";

} catch (Exception $e) {
    echo "<div class='alert alert-danger'>❌ خطأ: " . htmlspecialchars($e->getMessage()) . "</div>";
}

echo "<div class='text-center mt-4'>";
echo "<a href='instructor/dashboard.php' class='btn btn-primary btn-lg me-2'>🏠 لوحة تحكم المدرب</a>";
echo "<a href='login.php' class='btn btn-success btn-lg me-2'>🔐 تسجيل الدخول</a>";
echo "<a href='instructor/course-details.php?id=18' class='btn btn-info btn-lg'>📚 تفاصيل الكورس</a>";
echo "</div>";

echo "<div class='mt-4'>";
echo "<div class='alert alert-info'>";
echo "<h6>💡 تم إصلاح المشاكل التالية:</h6>";
echo "<ul>";
echo "<li>خطأ 'Undefined array key duration' في لوحة تحكم المدرب</li>";
echo "<li>إضافة الأعمدة المفقودة في جدول sessions</li>";
echo "<li>تحديث بيانات الجلسات بتواريخ وأوقات صحيحة</li>";
echo "<li>إنشاء جدول حضور الجلسات</li>";
echo "<li>إضافة بيانات تجريبية للاختبار</li>";
echo "</ul>";
echo "</div>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>
