<?php
require_once 'config/database.php';

echo "<h2>إنشاء طلاب تجريبيين</h2>";

try {
    // التحقق من وجود طلاب
    $stmt = $conn->query("SELECT COUNT(*) FROM users WHERE role = 'student'");
    $student_count = $stmt->fetchColumn();
    
    if ($student_count > 0) {
        echo "<p style='color: blue;'>يوجد بالفعل $student_count طالب في النظام</p>";
    } else {
        echo "<p>إنشاء طلاب تجريبيين...</p>";
        
        $sample_students = [
            [
                'username' => 'ahmed_student',
                'email' => '<EMAIL>',
                'password' => password_hash('123456', PASSWORD_DEFAULT),
                'full_name' => 'أحمد محمد علي',
                'phone' => '01234567890',
                'role' => 'student'
            ],
            [
                'username' => 'fatima_student',
                'email' => '<EMAIL>',
                'password' => password_hash('123456', PASSWORD_DEFAULT),
                'full_name' => 'فاطمة أحمد حسن',
                'phone' => '01234567891',
                'role' => 'student'
            ],
            [
                'username' => 'omar_student',
                'email' => '<EMAIL>',
                'password' => password_hash('123456', PASSWORD_DEFAULT),
                'full_name' => 'عمر خالد محمود',
                'phone' => '01234567892',
                'role' => 'student'
            ],
            [
                'username' => 'sara_student',
                'email' => '<EMAIL>',
                'password' => password_hash('123456', PASSWORD_DEFAULT),
                'full_name' => 'سارة عبدالله يوسف',
                'phone' => '01234567893',
                'role' => 'student'
            ],
            [
                'username' => 'hassan_student',
                'email' => '<EMAIL>',
                'password' => password_hash('123456', PASSWORD_DEFAULT),
                'full_name' => 'حسن عبدالرحمن طه',
                'phone' => '01234567894',
                'role' => 'student'
            ]
        ];
        
        foreach ($sample_students as $student) {
            $stmt = $conn->prepare("
                INSERT INTO users (username, email, password, full_name, phone, role, status) 
                VALUES (?, ?, ?, ?, ?, ?, 'active')
            ");
            $stmt->execute([
                $student['username'],
                $student['email'],
                $student['password'],
                $student['full_name'],
                $student['phone'],
                $student['role']
            ]);
            
            echo "<p>✅ تم إنشاء الطالب: " . $student['full_name'] . " (اسم المستخدم: " . $student['username'] . ")</p>";
        }
    }
    
    // إنشاء تسجيلات في الكورسات
    echo "<h3>إنشاء تسجيلات في الكورسات:</h3>";
    
    // التحقق من وجود جدول course_enrollments
    $stmt = $conn->query("SHOW TABLES LIKE 'course_enrollments'");
    if ($stmt->rowCount() == 0) {
        echo "<p>إنشاء جدول course_enrollments...</p>";
        $conn->exec("CREATE TABLE course_enrollments (
            id INT AUTO_INCREMENT PRIMARY KEY,
            student_id INT NOT NULL,
            course_id INT NOT NULL,
            enrolled_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            status ENUM('active', 'completed', 'suspended') DEFAULT 'active',
            progress DECIMAL(5,2) DEFAULT 0,
            grade DECIMAL(5,2) DEFAULT NULL,
            INDEX idx_student_id (student_id),
            INDEX idx_course_id (course_id),
            UNIQUE KEY unique_enrollment (student_id, course_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
        echo "<p>✅ تم إنشاء جدول course_enrollments</p>";
    }
    
    // جلب الطلاب والكورسات
    $stmt = $conn->query("SELECT id FROM users WHERE role = 'student' LIMIT 5");
    $students = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $stmt = $conn->query("SELECT id FROM courses LIMIT 3");
    $courses = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    if (!empty($students) && !empty($courses)) {
        foreach ($students as $student_id) {
            foreach ($courses as $course_id) {
                // تسجيل عشوائي (ليس كل طالب في كل كورس)
                if (rand(1, 100) <= 60) { // 60% احتمال التسجيل
                    try {
                        $progress = rand(0, 100);
                        $stmt = $conn->prepare("
                            INSERT IGNORE INTO course_enrollments (student_id, course_id, progress) 
                            VALUES (?, ?, ?)
                        ");
                        $stmt->execute([$student_id, $course_id, $progress]);
                        
                        if ($stmt->rowCount() > 0) {
                            echo "<p>✅ تم تسجيل الطالب $student_id في الكورس $course_id (التقدم: $progress%)</p>";
                        }
                    } catch (PDOException $e) {
                        // تجاهل الأخطاء المكررة
                    }
                }
            }
        }
    }
    
    // إنشاء طلبات انضمام معلقة
    echo "<h3>إنشاء طلبات انضمام معلقة:</h3>";
    
    // التحقق من وجود جدول join_requests
    $stmt = $conn->query("SHOW TABLES LIKE 'join_requests'");
    if ($stmt->rowCount() == 0) {
        echo "<p>إنشاء جدول join_requests...</p>";
        $conn->exec("CREATE TABLE join_requests (
            id INT AUTO_INCREMENT PRIMARY KEY,
            student_id INT NOT NULL,
            course_id INT NOT NULL,
            message TEXT,
            status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            processed_at TIMESTAMP NULL,
            processed_by INT NULL,
            INDEX idx_student_id (student_id),
            INDEX idx_course_id (course_id),
            INDEX idx_status (status)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
        echo "<p>✅ تم إنشاء جدول join_requests</p>";
    }
    
    // إنشاء بعض طلبات الانضمام المعلقة
    if (!empty($students) && !empty($courses)) {
        $messages = [
            'أرغب في الانضمام لهذا الكورس لتطوير مهاراتي',
            'أحتاج هذا الكورس لإكمال دراستي',
            'مهتم جداً بموضوع هذا الكورس',
            'أريد تعلم المزيد في هذا المجال',
            'أعتقد أن هذا الكورس سيفيدني كثيراً'
        ];
        
        for ($i = 0; $i < 3; $i++) {
            $student_id = $students[array_rand($students)];
            $course_id = $courses[array_rand($courses)];
            $message = $messages[array_rand($messages)];
            
            try {
                $stmt = $conn->prepare("
                    INSERT IGNORE INTO join_requests (student_id, course_id, message) 
                    VALUES (?, ?, ?)
                ");
                $stmt->execute([$student_id, $course_id, $message]);
                
                if ($stmt->rowCount() > 0) {
                    echo "<p>✅ تم إنشاء طلب انضمام للطالب $student_id في الكورس $course_id</p>";
                }
            } catch (PDOException $e) {
                // تجاهل الأخطاء المكررة
            }
        }
    }
    
    echo "<h3 style='color: green;'>✅ تم إنشاء البيانات التجريبية بنجاح!</h3>";
    
    // عرض معلومات تسجيل الدخول
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>🔑 معلومات تسجيل الدخول للطلاب:</h4>";
    echo "<p><strong>كلمة المرور لجميع الطلاب:</strong> 123456</p>";
    echo "<p><strong>أسماء المستخدمين:</strong></p>";
    echo "<ul>";
    echo "<li>ahmed_student</li>";
    echo "<li>fatima_student</li>";
    echo "<li>omar_student</li>";
    echo "<li>sara_student</li>";
    echo "<li>hassan_student</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<p><a href='check_students.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>عرض الطلاب</a></p>";
    echo "<p><a href='login.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>تسجيل الدخول</a></p>";
    
} catch (PDOException $e) {
    echo "<h3 style='color: red;'>❌ خطأ: " . $e->getMessage() . "</h3>";
}
?>
