<?php
require_once 'config/database.php';

echo "<h2>إنشاء جدول الفصول</h2>";

try {
    // التحقق من وجود جدول course_chapters
    $stmt = $conn->query("SHOW TABLES LIKE 'course_chapters'");
    if ($stmt->rowCount() == 0) {
        echo "<p>إنشاء جدول course_chapters...</p>";
        
        $conn->exec("CREATE TABLE course_chapters (
            id INT AUTO_INCREMENT PRIMARY KEY,
            course_id INT NOT NULL,
            title VARCHAR(255) NOT NULL,
            description TEXT,
            order_number INT DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_course_id (course_id),
            INDEX idx_order (order_number)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
        
        echo "<p style='color: green;'>✅ تم إنشاء جدول course_chapters</p>";
        
        // إضافة بعض الفصول التجريبية
        $stmt = $conn->query("SELECT id, title FROM courses LIMIT 3");
        $courses = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $sample_chapters = [
            'الفصل الأول: المقدمة والأساسيات',
            'الفصل الثاني: المفاهيم المتقدمة',
            'الفصل الثالث: التطبيق العملي',
            'الفصل الرابع: المشاريع والتمارين'
        ];
        
        foreach ($courses as $course) {
            for ($i = 0; $i < 3; $i++) {
                if (isset($sample_chapters[$i])) {
                    $conn->exec("INSERT INTO course_chapters 
                                (course_id, title, description, order_number) 
                                VALUES 
                                ({$course['id']}, '{$sample_chapters[$i]}', 'وصف {$sample_chapters[$i]}', " . ($i + 1) . ")");
                    
                    echo "<p>✅ تم إدراج فصل: " . $sample_chapters[$i] . " للكورس: " . $course['title'] . "</p>";
                }
            }
        }
    } else {
        echo "<p style='color: blue;'>ℹ️ جدول course_chapters موجود بالفعل</p>";
        
        // التحقق من وجود عمود order_number
        $stmt = $conn->query("DESCRIBE course_chapters");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        if (!in_array('order_number', $columns)) {
            echo "<p>إضافة عمود order_number...</p>";
            $conn->exec("ALTER TABLE course_chapters ADD COLUMN order_number INT DEFAULT 1");
            echo "<p style='color: green;'>✅ تم إضافة عمود order_number</p>";
        }
    }
    
    // التحقق من وجود جدول video_watches
    $stmt = $conn->query("SHOW TABLES LIKE 'video_watches'");
    if ($stmt->rowCount() == 0) {
        echo "<p>إنشاء جدول video_watches...</p>";
        
        $conn->exec("CREATE TABLE video_watches (
            id INT AUTO_INCREMENT PRIMARY KEY,
            video_id INT NOT NULL,
            user_id INT NOT NULL,
            watch_percentage DECIMAL(5,2) DEFAULT 0,
            watch_time INT DEFAULT 0,
            last_position INT DEFAULT 0,
            completed TINYINT(1) DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_video_id (video_id),
            INDEX idx_user_id (user_id),
            UNIQUE KEY unique_watch (video_id, user_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
        
        echo "<p style='color: green;'>✅ تم إنشاء جدول video_watches</p>";
        
        // إضافة بعض البيانات التجريبية
        $stmt = $conn->query("SELECT id FROM course_videos LIMIT 5");
        $videos = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        $stmt = $conn->query("SELECT id FROM users WHERE role = 'student' LIMIT 3");
        $students = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        if (!empty($videos) && !empty($students)) {
            foreach ($videos as $video_id) {
                foreach ($students as $student_id) {
                    $percentage = rand(10, 100);
                    $completed = $percentage >= 80 ? 1 : 0;
                    
                    $conn->exec("INSERT INTO video_watches 
                                (video_id, user_id, watch_percentage, completed) 
                                VALUES 
                                ($video_id, $student_id, $percentage, $completed)");
                }
            }
            echo "<p>✅ تم إدراج بيانات مشاهدة تجريبية</p>";
        }
    } else {
        echo "<p style='color: blue;'>ℹ️ جدول video_watches موجود بالفعل</p>";
    }
    
    echo "<h3 style='color: green;'>✅ تم إنشاء جميع الجداول المطلوبة بنجاح!</h3>";
    echo "<p><a href='instructor/videos.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>الذهاب لصفحة الفيديوهات</a></p>";
    
} catch (PDOException $e) {
    echo "<h3 style='color: red;'>❌ خطأ: " . $e->getMessage() . "</h3>";
}
?>
