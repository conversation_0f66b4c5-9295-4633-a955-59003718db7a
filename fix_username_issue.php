<?php
/**
 * إصلاح مشكلة عمود username
 * Fix username column issue
 */

require_once 'config/database.php';

echo "<!DOCTYPE html>";
echo "<html dir='rtl' lang='ar'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>إصلاح مشكلة username</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "</head>";
echo "<body>";
echo "<div class='container mt-4'>";
echo "<h2>🔧 إصلاح مشكلة عمود username</h2>";

try {
    // 1. فحص جدول المستخدمين
    echo "<h4>🔍 فحص جدول المستخدمين</h4>";
    
    $stmt = $conn->query("DESCRIBE users");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<div class='table-responsive'>";
    echo "<table class='table table-sm'>";
    echo "<thead><tr><th>العمود</th><th>النوع</th><th>Null</th><th>Key</th><th>Default</th></tr></thead>";
    echo "<tbody>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . ($column['Default'] ?? 'NULL') . "</td>";
        echo "</tr>";
    }
    echo "</tbody></table>";
    echo "</div>";

    // 2. إزالة عمود username إذا كان موجود
    echo "<h4>🗑️ إزالة عمود username المشكل</h4>";
    
    $existing_columns = array_column($columns, 'Field');
    
    if (in_array('username', $existing_columns)) {
        try {
            $conn->exec("ALTER TABLE users DROP COLUMN username");
            echo "<div class='alert alert-success'>✅ تم حذف عمود username</div>";
        } catch (Exception $e) {
            echo "<div class='alert alert-warning'>⚠️ لم يتم حذف عمود username: " . $e->getMessage() . "</div>";
        }
    } else {
        echo "<div class='alert alert-info'>ℹ️ عمود username غير موجود</div>";
    }

    // 3. إزالة جميع القيود الفريدة المشكلة
    echo "<h4>🔓 إزالة القيود المشكلة</h4>";
    
    try {
        // إزالة الفهارس الفريدة
        $indexes_to_drop = ['username', 'users_username_unique', 'idx_username'];
        
        foreach ($indexes_to_drop as $index) {
            try {
                $conn->exec("ALTER TABLE users DROP INDEX $index");
                echo "<div class='alert alert-success'>✅ تم حذف فهرس: $index</div>";
            } catch (Exception $e) {
                // تجاهل الأخطاء
            }
        }
    } catch (Exception $e) {
        // تجاهل
    }

    // 4. حذف جميع المستخدمين الموجودين
    echo "<h4>🗑️ تنظيف جدول المستخدمين</h4>";
    
    $conn->exec("DELETE FROM users");
    echo "<div class='alert alert-success'>✅ تم حذف جميع المستخدمين القدامى</div>";

    // 5. إعادة تعيين AUTO_INCREMENT
    $conn->exec("ALTER TABLE users AUTO_INCREMENT = 1");
    echo "<div class='alert alert-success'>✅ تم إعادة تعيين AUTO_INCREMENT</div>";

    // 6. إنشاء المستخدمين الجدد
    echo "<h4>👥 إنشاء مستخدمين جدد</h4>";
    
    $users = [
        ['مدير النظام', '<EMAIL>', 'admin123', 'admin'],
        ['أحمد محمد - مدرب', '<EMAIL>', 'instructor123', 'instructor'],
        ['سارة أحمد - طالبة', '<EMAIL>', 'student123', 'student'],
        ['محمد علي - مدرب', '<EMAIL>', 'instructor123', 'instructor'],
        ['فاطمة خالد - طالبة', '<EMAIL>', 'student123', 'student']
    ];
    
    // التحقق من الأعمدة الموجودة
    $stmt = $conn->query("DESCRIBE users");
    $current_columns = array_column($stmt->fetchAll(PDO::FETCH_ASSOC), 'Field');
    
    // بناء الاستعلام حسب الأعمدة المتوفرة
    if (in_array('phone', $current_columns)) {
        $stmt = $conn->prepare("INSERT INTO users (name, email, password, role, status, phone, created_at) VALUES (?, ?, ?, ?, 'active', NULL, NOW())");
    } else {
        $stmt = $conn->prepare("INSERT INTO users (name, email, password, role, status, created_at) VALUES (?, ?, ?, ?, 'active', NOW())");
    }
    
    foreach ($users as $user) {
        $hashed_password = password_hash($user[2], PASSWORD_DEFAULT);
        
        if (in_array('phone', $current_columns)) {
            $stmt->execute([$user[0], $user[1], $hashed_password, $user[3]]);
        } else {
            $stmt->execute([$user[0], $user[1], $hashed_password, $user[3]]);
        }
        
        echo "<div class='alert alert-success'>✅ تم إنشاء: {$user[0]} - {$user[1]}</div>";
    }

    // 7. اختبار المستخدمين
    echo "<h4>🧪 اختبار تسجيل الدخول</h4>";
    
    $test_users = [
        ['<EMAIL>', 'admin123', 'مدير'],
        ['<EMAIL>', 'instructor123', 'مدرب'],
        ['<EMAIL>', 'student123', 'طالب']
    ];
    
    foreach ($test_users as $test_user) {
        $stmt = $conn->prepare("SELECT * FROM users WHERE email = ?");
        $stmt->execute([$test_user[0]]);
        $db_user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($db_user && password_verify($test_user[1], $db_user['password'])) {
            echo "<div class='alert alert-success'>";
            echo "✅ <strong>{$test_user[2]}</strong> - {$test_user[0]} | كلمة المرور: {$test_user[1]} ✓";
            echo "</div>";
        } else {
            echo "<div class='alert alert-danger'>❌ مشكلة في: {$test_user[2]}</div>";
        }
    }

    // 8. عرض بيانات تسجيل الدخول النهائية
    echo "<h4>🔑 بيانات تسجيل الدخول الجاهزة</h4>";
    
    echo "<div class='row'>";
    
    $login_data = [
        ['مدير النظام', '<EMAIL>', 'admin123', 'danger', '👨‍💼'],
        ['مدرب', '<EMAIL>', 'instructor123', 'primary', '👨‍🏫'],
        ['طالب', '<EMAIL>', 'student123', 'success', '👨‍🎓']
    ];
    
    foreach ($login_data as $data) {
        echo "<div class='col-md-4 mb-3'>";
        echo "<div class='card border-{$data[3]}'>";
        echo "<div class='card-header bg-{$data[3]} text-white text-center'>";
        echo "<h5 class='mb-0'>{$data[4]} {$data[0]}</h5>";
        echo "</div>";
        echo "<div class='card-body'>";
        echo "<div class='mb-2'>";
        echo "<label class='form-label'><strong>البريد الإلكتروني:</strong></label>";
        echo "<input type='text' class='form-control' value='{$data[1]}' readonly onclick='this.select()' style='font-family: monospace;'>";
        echo "</div>";
        echo "<div class='mb-2'>";
        echo "<label class='form-label'><strong>كلمة المرور:</strong></label>";
        echo "<input type='text' class='form-control' value='{$data[2]}' readonly onclick='this.select()' style='font-family: monospace;'>";
        echo "</div>";
        echo "<div class='text-center'>";
        echo "<span class='badge bg-success'>✅ جاهز للاستخدام</span>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
    }
    
    echo "</div>";

    // 9. إحصائيات نهائية
    echo "<h4>📊 إحصائيات النظام</h4>";
    
    $stmt = $conn->query("SELECT COUNT(*) as total FROM users");
    $total_users = $stmt->fetchColumn();
    
    $stmt = $conn->query("SELECT role, COUNT(*) as count FROM users GROUP BY role");
    $role_stats = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<div class='row'>";
    echo "<div class='col-md-3'>";
    echo "<div class='card bg-primary text-white'>";
    echo "<div class='card-body text-center'>";
    echo "<h3>$total_users</h3>";
    echo "<p class='mb-0'>إجمالي المستخدمين</p>";
    echo "</div></div></div>";
    
    foreach ($role_stats as $stat) {
        $role_name = '';
        $bg_class = '';
        switch($stat['role']) {
            case 'admin': $role_name = 'مدراء'; $bg_class = 'danger'; break;
            case 'instructor': $role_name = 'مدربين'; $bg_class = 'info'; break;
            case 'student': $role_name = 'طلاب'; $bg_class = 'success'; break;
        }
        
        echo "<div class='col-md-3'>";
        echo "<div class='card bg-$bg_class text-white'>";
        echo "<div class='card-body text-center'>";
        echo "<h3>{$stat['count']}</h3>";
        echo "<p class='mb-0'>$role_name</p>";
        echo "</div></div></div>";
    }
    echo "</div>";

    echo "<div class='alert alert-success mt-4'>";
    echo "<h5>🎉 تم الإصلاح بنجاح!</h5>";
    echo "<ul class='mb-0'>";
    echo "<li>✅ تم حذف عمود username المشكل</li>";
    echo "<li>✅ تم إزالة جميع القيود المشكلة</li>";
    echo "<li>✅ تم إنشاء $total_users مستخدم جديد</li>";
    echo "<li>✅ تم اختبار جميع الحسابات بنجاح</li>";
    echo "<li>✅ النظام جاهز للاستخدام</li>";
    echo "</ul>";
    echo "</div>";

} catch (Exception $e) {
    echo "<div class='alert alert-danger'>❌ خطأ: " . htmlspecialchars($e->getMessage()) . "</div>";
}

echo "<div class='text-center mt-4'>";
echo "<a href='login.php' class='btn btn-primary btn-lg me-2'>🔐 تسجيل الدخول الآن</a>";
echo "<a href='system_health_check.php' class='btn btn-info btn-lg'>🔍 فحص النظام</a>";
echo "</div>";

echo "<div class='mt-4'>";
echo "<div class='alert alert-info'>";
echo "<h6>📋 تعليمات تسجيل الدخول:</h6>";
echo "<ol>";
echo "<li>انقر على الحقل لتحديد النص</li>";
echo "<li>انسخ البريد الإلكتروني (Ctrl+C)</li>";
echo "<li>انسخ كلمة المرور (Ctrl+C)</li>";
echo "<li>اذهب لصفحة تسجيل الدخول</li>";
echo "<li>اختر نوع الحساب المناسب</li>";
echo "<li>الصق البيانات (Ctrl+V)</li>";
echo "</ol>";
echo "</div>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>
