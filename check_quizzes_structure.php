<?php
require_once 'config/database.php';

echo "<h2>فحص بنية جدول quizzes</h2>";

try {
    // عرض بنية الجدول
    $stmt = $conn->query("DESCRIBE quizzes");
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>اسم العمود</th><th>النوع</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    while ($row = $stmt->fetch()) {
        echo "<tr>";
        echo "<td>{$row['Field']}</td>";
        echo "<td>{$row['Type']}</td>";
        echo "<td>{$row['Null']}</td>";
        echo "<td>{$row['Key']}</td>";
        echo "<td>{$row['Default']}</td>";
        echo "<td>{$row['Extra']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // اختبار استعلام بسيط
    echo "<h3>اختبار استعلام بسيط:</h3>";
    $stmt = $conn->query("SELECT * FROM quizzes LIMIT 1");
    if ($row = $stmt->fetch()) {
        echo "<pre>";
        print_r($row);
        echo "</pre>";
    } else {
        echo "لا توجد بيانات في جدول quizzes";
    }
    
} catch (Exception $e) {
    echo "خطأ: " . $e->getMessage();
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
table { margin: 10px 0; }
th, td { padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
</style>
