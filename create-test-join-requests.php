<?php
require_once 'config/database.php';

echo "<h2>إنشاء طلبات انضمام تجريبية</h2>";

try {
    // التأكد من وجود جدول join_requests
    $stmt = $conn->query("SHOW TABLES LIKE 'join_requests'");
    if ($stmt->rowCount() == 0) {
        echo "<p>إنشاء جدول join_requests...</p>";
        $conn->exec("CREATE TABLE join_requests (
            id INT AUTO_INCREMENT PRIMARY KEY,
            student_id INT NOT NULL,
            course_id INT NOT NULL,
            message TEXT,
            status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            processed_at TIMESTAMP NULL,
            processed_by INT NULL,
            INDEX idx_student_id (student_id),
            INDEX idx_course_id (course_id),
            INDEX idx_status (status)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
        echo "<p style='color: green;'>✅ تم إنشاء جدول join_requests</p>";
    }
    
    // جلب الطلاب والكورسات
    $stmt = $conn->query("SELECT id, username, COALESCE(full_name, username) as full_name FROM users WHERE role = 'student' LIMIT 5");
    $students = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $stmt = $conn->query("SELECT id, title FROM courses WHERE status = 'active' LIMIT 3");
    $courses = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($students)) {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px;'>";
        echo "<h4>❌ لا يوجد طلاب في النظام</h4>";
        echo "<p><a href='create_sample_students.php'>انقر هنا لإنشاء طلاب تجريبيين</a></p>";
        echo "</div>";
        exit;
    }
    
    if (empty($courses)) {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px;'>";
        echo "<h4>❌ لا توجد كورسات في النظام</h4>";
        echo "<p>يجب إنشاء كورسات أولاً</p>";
        echo "</div>";
        exit;
    }
    
    echo "<h3>الطلاب المتاحون:</h3>";
    echo "<ul>";
    foreach ($students as $student) {
        echo "<li>ID: " . $student['id'] . " - " . htmlspecialchars($student['full_name']) . "</li>";
    }
    echo "</ul>";
    
    echo "<h3>الكورسات المتاحة:</h3>";
    echo "<ul>";
    foreach ($courses as $course) {
        echo "<li>ID: " . $course['id'] . " - " . htmlspecialchars($course['title']) . "</li>";
    }
    echo "</ul>";
    
    // رسائل تجريبية
    $sample_messages = [
        'أرغب في الانضمام لهذا الكورس لتطوير مهاراتي في البرمجة',
        'أحتاج هذا الكورس لإكمال دراستي الجامعية',
        'مهتم جداً بموضوع هذا الكورس وأريد تعلم المزيد',
        'أعتقد أن هذا الكورس سيساعدني في تطوير مسيرتي المهنية',
        'لدي خبرة سابقة في المجال وأريد تطوير معرفتي أكثر',
        'أريد تعلم هذا الموضوع من الصفر',
        'سمعت عن هذا الكورس من أصدقائي وأريد الانضمام',
        'أحتاج هذه المهارات في عملي الحالي'
    ];
    
    echo "<h3>إنشاء طلبات الانضمام:</h3>";
    
    $created_count = 0;
    
    // إنشاء طلبات متنوعة
    foreach ($students as $student) {
        foreach ($courses as $course) {
            // إنشاء طلب بنسبة 70%
            if (rand(1, 100) <= 70) {
                $message = $sample_messages[array_rand($sample_messages)];
                
                // التحقق من عدم وجود طلب سابق
                $stmt = $conn->prepare("SELECT id FROM join_requests WHERE student_id = ? AND course_id = ?");
                $stmt->execute([$student['id'], $course['id']]);
                
                if (!$stmt->fetch()) {
                    // إنشاء الطلب
                    $stmt = $conn->prepare("
                        INSERT INTO join_requests (student_id, course_id, message) 
                        VALUES (?, ?, ?)
                    ");
                    $stmt->execute([$student['id'], $course['id'], $message]);
                    
                    echo "<p>✅ تم إنشاء طلب انضمام للطالب " . htmlspecialchars($student['full_name'] ?? $student['username']) . 
                         " للكورس " . htmlspecialchars($course['title']) . "</p>";
                    $created_count++;
                }
            }
        }
    }
    
    // إنشاء بعض الطلبات المعالجة (مقبولة ومرفوضة)
    echo "<h3>إنشاء طلبات معالجة (للاختبار):</h3>";
    
    $stmt = $conn->query("SELECT id FROM join_requests WHERE status = 'pending' LIMIT 3");
    $pending_requests = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    if (!empty($pending_requests)) {
        // قبول طلب واحد
        $approved_id = $pending_requests[0];
        $conn->exec("UPDATE join_requests SET status = 'approved', processed_at = NOW() WHERE id = $approved_id");
        echo "<p>✅ تم قبول طلب رقم $approved_id</p>";
        
        // رفض طلب آخر
        if (count($pending_requests) > 1) {
            $rejected_id = $pending_requests[1];
            $conn->exec("UPDATE join_requests SET status = 'rejected', processed_at = NOW() WHERE id = $rejected_id");
            echo "<p>❌ تم رفض طلب رقم $rejected_id</p>";
        }
    }
    
    echo "<h3 style='color: green;'>✅ تم إنشاء $created_count طلب انضمام بنجاح!</h3>";
    
    // عرض الإحصائيات النهائية
    $stmt = $conn->query("
        SELECT 
            COUNT(*) as total,
            SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
            SUM(CASE WHEN status = 'approved' THEN 1 ELSE 0 END) as approved,
            SUM(CASE WHEN status = 'rejected' THEN 1 ELSE 0 END) as rejected
        FROM join_requests
    ");
    $stats = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>📊 إحصائيات طلبات الانضمام:</h4>";
    echo "<ul>";
    echo "<li><strong>إجمالي الطلبات:</strong> " . $stats['total'] . "</li>";
    echo "<li><strong>طلبات معلقة:</strong> " . $stats['pending'] . "</li>";
    echo "<li><strong>طلبات مقبولة:</strong> " . $stats['approved'] . "</li>";
    echo "<li><strong>طلبات مرفوضة:</strong> " . $stats['rejected'] . "</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h3>🔗 الخطوات التالية:</h3>";
    echo "<ol>";
    echo "<li><a href='debug-join-requests.php'>عرض جميع طلبات الانضمام</a></li>";
    echo "<li><a href='instructor/course-join-requests.php?course_id=1'>مراجعة طلبات الكورس الأول</a></li>";
    echo "<li><a href='test-join-course.php'>اختبار إرسال طلب جديد</a></li>";
    echo "</ol>";
    
} catch (PDOException $e) {
    echo "<h3 style='color: red;'>❌ خطأ: " . $e->getMessage() . "</h3>";
}
?>
