<?php
session_start();
require_once 'config/database.php';
require_once 'config/config.php';
require_once 'includes/functions.php';
require_once 'includes/security_enhanced.php';

// تطبيق إعدادات الأمان
applySecurityHeaders();

// معاملات الفلترة والترتيب
$category = $_GET['category'] ?? '';
$level = $_GET['level'] ?? '';
$price_type = $_GET['price_type'] ?? '';
$instructor = $_GET['instructor'] ?? '';
$sort_by = $_GET['sort_by'] ?? 'latest';
$page = max(1, (int)($_GET['page'] ?? 1));
$per_page = 12;

// بناء استعلام الكورسات
$where_conditions = ["c.status = 'active'"];
$params = [];

if (!empty($category)) {
    $where_conditions[] = "c.category_id = ?";
    $params[] = $category;
}

if (!empty($level)) {
    $where_conditions[] = "c.level = ?";
    $params[] = $level;
}

if (!empty($price_type)) {
    if ($price_type === 'free') {
        $where_conditions[] = "c.price = 0";
    } elseif ($price_type === 'paid') {
        $where_conditions[] = "c.price > 0";
    }
}

if (!empty($instructor)) {
    $where_conditions[] = "c.instructor_id = ?";
    $params[] = $instructor;
}

$where_clause = implode(' AND ', $where_conditions);

// ترتيب النتائج
$order_clause = match($sort_by) {
    'latest' => 'c.created_at DESC',
    'oldest' => 'c.created_at ASC',
    'price_low' => 'c.price ASC',
    'price_high' => 'c.price DESC',
    'rating' => 'avg_rating DESC',
    'students' => 'total_students DESC',
    'title' => 'c.title ASC',
    default => 'c.created_at DESC'
};

try {
    // استعلام الكورسات مع المعلومات الإضافية
    $offset = ($page - 1) * $per_page;

    $stmt = $conn->prepare("
        SELECT c.*,
               u.name as instructor_name,
               u.profile_picture as instructor_avatar,
               cat.name as category_name,
               cat.color as category_color,
               (SELECT COUNT(*) FROM course_enrollments WHERE course_id = c.id AND status = 'active') as total_students,
               (SELECT AVG(rating) FROM course_reviews WHERE course_id = c.id) as avg_rating,
               (SELECT COUNT(*) FROM course_reviews WHERE course_id = c.id) as total_reviews,
               (SELECT COUNT(*) FROM course_videos WHERE course_id = c.id) as total_videos,
               (SELECT SUM(duration_minutes) FROM course_videos WHERE course_id = c.id) as total_duration
        FROM courses c
        LEFT JOIN users u ON c.instructor_id = u.id
        LEFT JOIN categories cat ON c.category_id = cat.id
        WHERE $where_clause
        ORDER BY $order_clause
        LIMIT ? OFFSET ?
    ");

    $params[] = $per_page;
    $params[] = $offset;
    $stmt->execute($params);
    $courses = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // عدد الكورسات الإجمالي
    $count_params = array_slice($params, 0, -2); // إزالة LIMIT و OFFSET
    $count_stmt = $conn->prepare("
        SELECT COUNT(*)
        FROM courses c
        LEFT JOIN users u ON c.instructor_id = u.id
        LEFT JOIN categories cat ON c.category_id = cat.id
        WHERE $where_clause
    ");
    $count_stmt->execute($count_params);
    $total_courses = $count_stmt->fetchColumn();

    // الحصول على التصنيفات
    $categories_stmt = $conn->query("
        SELECT cat.*, COUNT(c.id) as courses_count
        FROM categories cat
        LEFT JOIN courses c ON cat.id = c.category_id AND c.status = 'active'
        WHERE cat.is_active = 1
        GROUP BY cat.id
        ORDER BY cat.name
    ");
    $categories = $categories_stmt->fetchAll(PDO::FETCH_ASSOC);

    // الحصول على المدربين
    $instructors_stmt = $conn->query("
        SELECT u.id, u.name, COUNT(c.id) as courses_count
        FROM users u
        LEFT JOIN courses c ON u.id = c.instructor_id AND c.status = 'active'
        WHERE u.role = 'instructor' AND u.status = 'active'
        GROUP BY u.id
        HAVING courses_count > 0
        ORDER BY u.name
        LIMIT 50
    ");
    $instructors = $instructors_stmt->fetchAll(PDO::FETCH_ASSOC);

} catch (PDOException $e) {
    error_log("Database error in courses: " . $e->getMessage());
    $courses = [];
    $total_courses = 0;
    $categories = [];
    $instructors = [];
}

$total_pages = ceil($total_courses / $per_page);

// دوال مساعدة
function formatPrice($price, $currency = 'USD') {
    if ($price == 0) {
        return '<span class="price-free">مجاني</span>';
    }

    $symbols = [
        'USD' => '$',
        'EUR' => '€',
        'SAR' => 'ر.س',
        'AED' => 'د.إ'
    ];

    $symbol = $symbols[$currency] ?? '$';
    return '<span class="price-paid">' . $symbol . number_format($price, 2) . '</span>';
}

function getStarRating($rating, $total_reviews = 0) {
    if (!$rating) {
        return '<span class="text-muted">لا توجد تقييمات</span>';
    }

    $rating = (float)$rating;
    $fullStars = floor($rating);
    $halfStar = ($rating - $fullStars) >= 0.5 ? 1 : 0;
    $emptyStars = 5 - $fullStars - $halfStar;

    $html = '<div class="rating-stars">';

    // نجوم ممتلئة
    for ($i = 0; $i < $fullStars; $i++) {
        $html .= '<i class="fas fa-star text-warning"></i>';
    }

    // نجمة نصف ممتلئة
    if ($halfStar) {
        $html .= '<i class="fas fa-star-half-alt text-warning"></i>';
    }

    // نجوم فارغة
    for ($i = 0; $i < $emptyStars; $i++) {
        $html .= '<i class="far fa-star text-warning"></i>';
    }

    $html .= '<span class="rating-text">(' . number_format($rating, 1) . ')</span>';

    if ($total_reviews > 0) {
        $html .= '<span class="reviews-count">' . number_format($total_reviews) . ' تقييم</span>';
    }

    $html .= '</div>';

    return $html;
}

function formatDuration($minutes) {
    if (!$minutes) return 'غير محدد';

    $hours = floor($minutes / 60);
    $mins = $minutes % 60;

    if ($hours > 0) {
        return $hours . 'س ' . ($mins > 0 ? $mins . 'د' : '');
    } else {
        return $mins . ' دقيقة';
    }
}

$pageTitle = "جميع الكورسات";
if (!empty($category)) {
    $cat_name = '';
    foreach ($categories as $cat) {
        if ($cat['id'] == $category) {
            $cat_name = $cat['name'];
            break;
        }
    }
    if ($cat_name) {
        $pageTitle = "كورسات " . $cat_name;
    }
}
?>

<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="تصفح جميع الكورسات المتاحة في منصة التعلم الإلكتروني مع إمكانية الفلترة والبحث">
    <meta name="keywords" content="كورسات, تعلم إلكتروني, دورات تدريبية, تعليم أونلاين">
    <title><?php echo $pageTitle; ?> - <?php echo SITE_NAME; ?></title>

    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="assets/css/main.css" rel="stylesheet">
    <link href="assets/css/responsive.css" rel="stylesheet">

    <style>
        :root {
            --primary-color: #667eea;
            --secondary-color: #764ba2;
            --success-color: #48bb78;
            --warning-color: #ed8936;
            --danger-color: #f56565;
            --info-color: #4299e1;
            --light-color: #f7fafc;
            --dark-color: #2d3748;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8fafc;
        }

        .courses-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 80px 0 40px;
            margin-top: 70px;
        }

        .filters-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            position: sticky;
            top: 90px;
            z-index: 100;
        }

        .filter-group {
            margin-bottom: 20px;
        }

        .filter-label {
            font-weight: 600;
            margin-bottom: 10px;
            color: var(--dark-color);
            display: block;
        }

        .filter-select {
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            padding: 12px 15px;
            width: 100%;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .filter-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            outline: none;
        }

        .courses-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }

        .course-card {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            position: relative;
        }

        .course-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }

        .course-image {
            position: relative;
            height: 200px;
            overflow: hidden;
        }

        .course-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .course-card:hover .course-image img {
            transform: scale(1.05);
        }

        .course-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .course-card:hover .course-overlay {
            opacity: 1;
        }

        .course-placeholder {
            height: 200px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 3rem;
        }

        .price-badge {
            position: absolute;
            top: 15px;
            right: 15px;
            padding: 8px 15px;
            border-radius: 25px;
            font-weight: 600;
            font-size: 14px;
            z-index: 2;
        }

        .price-free {
            background: var(--success-color);
            color: white;
        }

        .price-paid {
            background: var(--warning-color);
            color: white;
        }

        .course-content {
            padding: 20px;
        }

        .course-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            font-size: 12px;
        }

        .course-category {
            background: var(--light-color);
            color: var(--primary-color);
            padding: 4px 12px;
            border-radius: 20px;
            font-weight: 500;
        }

        .course-level {
            color: var(--dark-color);
            font-weight: 500;
        }

        .course-title {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 10px;
            line-height: 1.4;
        }

        .course-title a {
            color: var(--dark-color);
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .course-title a:hover {
            color: var(--primary-color);
        }

        .course-description {
            color: #718096;
            font-size: 14px;
            line-height: 1.5;
            margin-bottom: 15px;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .course-instructor {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            font-size: 14px;
            color: var(--dark-color);
        }

        .instructor-avatar {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            margin-left: 10px;
            object-fit: cover;
        }

        .course-stats {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-top: 15px;
            border-top: 1px solid #e2e8f0;
            font-size: 13px;
        }

        .rating-stars {
            display: flex;
            align-items: center;
            gap: 2px;
        }

        .rating-text {
            margin-right: 5px;
            color: var(--dark-color);
            font-weight: 500;
        }

        .reviews-count {
            color: #718096;
            margin-right: 5px;
        }

        .course-students {
            color: #718096;
        }

        .course-duration {
            color: #718096;
        }

        .results-header {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
        }

        .results-info {
            color: var(--dark-color);
            font-weight: 500;
        }

        .sort-section {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .sort-select {
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            padding: 8px 15px;
            min-width: 150px;
            font-size: 14px;
        }

        .pagination-container {
            display: flex;
            justify-content: center;
            margin-top: 40px;
        }

        .pagination .page-link {
            border: none;
            color: var(--primary-color);
            padding: 12px 16px;
            margin: 0 2px;
            border-radius: 10px;
            font-weight: 500;
        }

        .pagination .page-item.active .page-link {
            background: var(--primary-color);
            color: white;
        }

        .pagination .page-link:hover {
            background: var(--light-color);
            color: var(--primary-color);
        }

        .no-courses {
            text-align: center;
            padding: 60px 20px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .no-courses-icon {
            font-size: 4rem;
            color: #cbd5e0;
            margin-bottom: 20px;
        }

        .clear-filters-btn {
            background: var(--danger-color);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 10px;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .clear-filters-btn:hover {
            background: #e53e3e;
            transform: translateY(-2px);
        }

        .mobile-filters-toggle {
            display: none;
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 10px;
            font-weight: 500;
            margin-bottom: 20px;
            width: 100%;
        }

        @media (max-width: 768px) {
            .courses-header {
                padding: 60px 0 30px;
                margin-top: 60px;
            }

            .courses-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .filters-section {
                position: static;
                margin-bottom: 20px;
            }

            .mobile-filters-toggle {
                display: block;
            }

            .results-header {
                flex-direction: column;
                text-align: center;
            }

            .course-stats {
                flex-direction: column;
                gap: 10px;
                align-items: flex-start;
            }
        }

        @media (max-width: 576px) {
            .course-card {
                margin: 0 10px;
            }

            .courses-grid {
                grid-template-columns: 1fr;
                padding: 0 10px;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <?php include 'includes/header.php'; ?>

    <!-- Courses Header -->
    <section class="courses-header">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center">
                    <h1 class="mb-3">
                        <i class="fas fa-graduation-cap"></i>
                        <?php echo $pageTitle; ?>
                    </h1>
                    <p class="lead mb-4">
                        اكتشف مجموعة واسعة من الكورسات التعليمية عالية الجودة
                    </p>
                    <div class="courses-stats">
                        <span class="badge bg-light text-dark fs-6 me-3">
                            <i class="fas fa-book"></i>
                            <?php echo number_format($total_courses); ?> كورس متاح
                        </span>
                        <span class="badge bg-light text-dark fs-6">
                            <i class="fas fa-users"></i>
                            <?php echo number_format(count($instructors)); ?> مدرب محترف
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Main Content -->
    <div class="container my-5">
        <div class="row">
            <!-- Filters Sidebar -->
            <div class="col-lg-3">
                <!-- Mobile Filters Toggle -->
                <button class="mobile-filters-toggle" type="button" data-bs-toggle="collapse" data-bs-target="#filtersCollapse">
                    <i class="fas fa-filter"></i>
                    تصفية النتائج
                </button>

                <div class="collapse d-lg-block" id="filtersCollapse">
                    <div class="filters-section">
                        <h5 class="mb-3">
                            <i class="fas fa-filter"></i>
                            تصفية النتائج
                        </h5>

                        <form method="GET" action="courses_enhanced.php" id="filtersForm">
                            <!-- Category Filter -->
                            <div class="filter-group">
                                <label class="filter-label">التصنيف</label>
                                <select name="category" class="filter-select" onchange="document.getElementById('filtersForm').submit()">
                                    <option value="">جميع التصنيفات</option>
                                    <?php foreach ($categories as $cat): ?>
                                        <option value="<?php echo $cat['id']; ?>" <?php echo $category == $cat['id'] ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($cat['name']); ?>
                                            (<?php echo $cat['courses_count']; ?>)
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <!-- Level Filter -->
                            <div class="filter-group">
                                <label class="filter-label">المستوى</label>
                                <select name="level" class="filter-select" onchange="document.getElementById('filtersForm').submit()">
                                    <option value="">جميع المستويات</option>
                                    <option value="beginner" <?php echo $level == 'beginner' ? 'selected' : ''; ?>>مبتدئ</option>
                                    <option value="intermediate" <?php echo $level == 'intermediate' ? 'selected' : ''; ?>>متوسط</option>
                                    <option value="advanced" <?php echo $level == 'advanced' ? 'selected' : ''; ?>>متقدم</option>
                                </select>
                            </div>

                            <!-- Price Filter -->
                            <div class="filter-group">
                                <label class="filter-label">نوع السعر</label>
                                <select name="price_type" class="filter-select" onchange="document.getElementById('filtersForm').submit()">
                                    <option value="">جميع الأنواع</option>
                                    <option value="free" <?php echo $price_type == 'free' ? 'selected' : ''; ?>>مجاني</option>
                                    <option value="paid" <?php echo $price_type == 'paid' ? 'selected' : ''; ?>>مدفوع</option>
                                </select>
                            </div>

                            <!-- Instructor Filter -->
                            <div class="filter-group">
                                <label class="filter-label">المدرب</label>
                                <select name="instructor" class="filter-select" onchange="document.getElementById('filtersForm').submit()">
                                    <option value="">جميع المدربين</option>
                                    <?php foreach ($instructors as $inst): ?>
                                        <option value="<?php echo $inst['id']; ?>" <?php echo $instructor == $inst['id'] ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($inst['name']); ?>
                                            (<?php echo $inst['courses_count']; ?>)
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <!-- Clear Filters -->
                            <?php if ($category || $level || $price_type || $instructor): ?>
                                <div class="filter-group">
                                    <a href="courses_enhanced.php" class="clear-filters-btn w-100 d-block text-center text-decoration-none">
                                        <i class="fas fa-times"></i>
                                        مسح جميع الفلاتر
                                    </a>
                                </div>
                            <?php endif; ?>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Courses Content -->
            <div class="col-lg-9">
                <!-- Results Header -->
                <div class="results-header">
                    <div class="results-info">
                        <strong><?php echo number_format($total_courses); ?></strong> كورس متاح
                        <?php if ($category || $level || $price_type || $instructor): ?>
                            <span class="text-muted">(مفلتر)</span>
                        <?php endif; ?>
                    </div>
                    <div class="sort-section">
                        <label for="sortSelect" class="form-label mb-0 me-2">ترتيب حسب:</label>
                        <form method="GET" action="courses_enhanced.php" style="display: inline;">
                            <input type="hidden" name="category" value="<?php echo htmlspecialchars($category); ?>">
                            <input type="hidden" name="level" value="<?php echo htmlspecialchars($level); ?>">
                            <input type="hidden" name="price_type" value="<?php echo htmlspecialchars($price_type); ?>">
                            <input type="hidden" name="instructor" value="<?php echo htmlspecialchars($instructor); ?>">
                            <select name="sort_by" id="sortSelect" class="sort-select" onchange="this.form.submit()">
                                <option value="latest" <?php echo $sort_by == 'latest' ? 'selected' : ''; ?>>الأحدث</option>
                                <option value="oldest" <?php echo $sort_by == 'oldest' ? 'selected' : ''; ?>>الأقدم</option>
                                <option value="title" <?php echo $sort_by == 'title' ? 'selected' : ''; ?>>الاسم</option>
                                <option value="price_low" <?php echo $sort_by == 'price_low' ? 'selected' : ''; ?>>السعر (الأقل)</option>
                                <option value="price_high" <?php echo $sort_by == 'price_high' ? 'selected' : ''; ?>>السعر (الأعلى)</option>
                                <option value="rating" <?php echo $sort_by == 'rating' ? 'selected' : ''; ?>>التقييم</option>
                                <option value="students" <?php echo $sort_by == 'students' ? 'selected' : ''; ?>>عدد الطلاب</option>
                            </select>
                        </form>
                    </div>
                </div>

                <!-- Courses Grid -->
                <?php if (!empty($courses)): ?>
                    <div class="courses-grid">
                        <?php foreach ($courses as $course): ?>
                            <div class="course-card">
                                <div class="course-image">
                                    <?php if ($course['thumbnail']): ?>
                                        <img src="<?php echo htmlspecialchars($course['thumbnail']); ?>"
                                             alt="<?php echo htmlspecialchars($course['title']); ?>"
                                             loading="lazy">
                                    <?php else: ?>
                                        <div class="course-placeholder">
                                            <i class="fas fa-book"></i>
                                        </div>
                                    <?php endif; ?>

                                    <div class="course-overlay">
                                        <a href="course-details.php?id=<?php echo $course['id']; ?>"
                                           class="btn btn-light btn-lg">
                                            <i class="fas fa-eye"></i>
                                            عرض التفاصيل
                                        </a>
                                    </div>

                                    <div class="price-badge <?php echo $course['price'] > 0 ? 'price-paid' : 'price-free'; ?>">
                                        <?php echo formatPrice($course['price'], $course['currency'] ?? 'USD'); ?>
                                    </div>
                                </div>

                                <div class="course-content">
                                    <div class="course-meta">
                                        <span class="course-category" style="background-color: <?php echo $course['category_color'] ?? '#f7fafc'; ?>20;">
                                            <i class="fas fa-tag"></i>
                                            <?php echo htmlspecialchars($course['category_name'] ?? 'عام'); ?>
                                        </span>
                                        <span class="course-level">
                                            <i class="fas fa-signal"></i>
                                            <?php
                                            $levels = [
                                                'beginner' => 'مبتدئ',
                                                'intermediate' => 'متوسط',
                                                'advanced' => 'متقدم'
                                            ];
                                            echo $levels[$course['level']] ?? 'مبتدئ';
                                            ?>
                                        </span>
                                    </div>

                                    <h3 class="course-title">
                                        <a href="course-details.php?id=<?php echo $course['id']; ?>">
                                            <?php echo htmlspecialchars($course['title']); ?>
                                        </a>
                                    </h3>

                                    <p class="course-description">
                                        <?php echo htmlspecialchars($course['short_description'] ?? $course['description']); ?>
                                    </p>

                                    <div class="course-instructor">
                                        <?php if ($course['instructor_avatar']): ?>
                                            <img src="<?php echo htmlspecialchars($course['instructor_avatar']); ?>"
                                                 alt="<?php echo htmlspecialchars($course['instructor_name']); ?>"
                                                 class="instructor-avatar">
                                        <?php else: ?>
                                            <div class="instructor-avatar bg-secondary d-flex align-items-center justify-content-center">
                                                <i class="fas fa-user text-white"></i>
                                            </div>
                                        <?php endif; ?>
                                        <span><?php echo htmlspecialchars($course['instructor_name']); ?></span>
                                    </div>

                                    <div class="course-stats">
                                        <div class="rating-section">
                                            <?php echo getStarRating($course['avg_rating'], $course['total_reviews']); ?>
                                        </div>
                                        <div class="stats-info">
                                            <div class="course-students">
                                                <i class="fas fa-users"></i>
                                                <?php echo number_format($course['total_students']); ?> طالب
                                            </div>
                                            <div class="course-duration">
                                                <i class="fas fa-clock"></i>
                                                <?php echo formatDuration($course['total_duration']); ?>
                                            </div>
                                            <?php if ($course['total_videos']): ?>
                                                <div class="course-videos">
                                                    <i class="fas fa-play-circle"></i>
                                                    <?php echo $course['total_videos']; ?> فيديو
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>

                    <!-- Pagination -->
                    <?php if ($total_pages > 1): ?>
                        <div class="pagination-container">
                            <nav aria-label="صفحات الكورسات">
                                <ul class="pagination">
                                    <?php if ($page > 1): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?page=<?php echo $page - 1; ?>&category=<?php echo urlencode($category); ?>&level=<?php echo urlencode($level); ?>&price_type=<?php echo urlencode($price_type); ?>&instructor=<?php echo urlencode($instructor); ?>&sort_by=<?php echo urlencode($sort_by); ?>">
                                                <i class="fas fa-chevron-right"></i>
                                                السابق
                                            </a>
                                        </li>
                                    <?php endif; ?>

                                    <?php
                                    $start = max(1, $page - 2);
                                    $end = min($total_pages, $page + 2);

                                    for ($i = $start; $i <= $end; $i++): ?>
                                        <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                                            <a class="page-link" href="?page=<?php echo $i; ?>&category=<?php echo urlencode($category); ?>&level=<?php echo urlencode($level); ?>&price_type=<?php echo urlencode($price_type); ?>&instructor=<?php echo urlencode($instructor); ?>&sort_by=<?php echo urlencode($sort_by); ?>">
                                                <?php echo $i; ?>
                                            </a>
                                        </li>
                                    <?php endfor; ?>

                                    <?php if ($page < $total_pages): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?page=<?php echo $page + 1; ?>&category=<?php echo urlencode($category); ?>&level=<?php echo urlencode($level); ?>&price_type=<?php echo urlencode($price_type); ?>&instructor=<?php echo urlencode($instructor); ?>&sort_by=<?php echo urlencode($sort_by); ?>">
                                                التالي
                                                <i class="fas fa-chevron-left"></i>
                                            </a>
                                        </li>
                                    <?php endif; ?>
                                </ul>
                            </nav>
                        </div>
                    <?php endif; ?>

                <?php else: ?>
                    <!-- No Courses Found -->
                    <div class="no-courses">
                        <div class="no-courses-icon">
                            <i class="fas fa-search"></i>
                        </div>
                        <h3>لم يتم العثور على كورسات</h3>
                        <p class="text-muted mb-4">
                            لم نتمكن من العثور على أي كورسات تطابق معايير البحث المحددة.
                        </p>
                        <div class="suggestions">
                            <p><strong>جرب:</strong></p>
                            <ul class="list-unstyled">
                                <li>• إزالة بعض الفلاتر</li>
                                <li>• البحث في تصنيف آخر</li>
                                <li>• تغيير مستوى الصعوبة</li>
                                <li>• تصفح جميع الكورسات</li>
                            </ul>
                        </div>
                        <?php if ($category || $level || $price_type || $instructor): ?>
                            <a href="courses_enhanced.php" class="btn btn-primary mt-3">
                                <i class="fas fa-refresh"></i>
                                عرض جميع الكورسات
                            </a>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <?php include 'includes/footer.php'; ?>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
    <script src="assets/js/responsive.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // تحسين تجربة الفلاتر
            const filterSelects = document.querySelectorAll('.filter-select');

            filterSelects.forEach(select => {
                select.addEventListener('change', function() {
                    // إضافة مؤشر التحميل
                    const loadingSpinner = document.createElement('div');
                    loadingSpinner.className = 'text-center my-3';
                    loadingSpinner.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التحميل...';

                    const coursesGrid = document.querySelector('.courses-grid');
                    if (coursesGrid) {
                        coursesGrid.style.opacity = '0.5';
                        coursesGrid.parentNode.insertBefore(loadingSpinner, coursesGrid);
                    }
                });
            });

            // تحسين عرض البطاقات
            const courseCards = document.querySelectorAll('.course-card');

            courseCards.forEach(card => {
                // إضافة تأثير hover للصور
                const image = card.querySelector('.course-image img');
                if (image) {
                    image.addEventListener('load', function() {
                        this.style.opacity = '1';
                    });
                }

                // تحسين النقر على البطاقة
                card.addEventListener('click', function(e) {
                    if (!e.target.closest('a')) {
                        const link = this.querySelector('.course-title a');
                        if (link) {
                            window.location.href = link.href;
                        }
                    }
                });
            });

            // تحسين الفلاتر للجوال
            const filtersToggle = document.querySelector('.mobile-filters-toggle');
            const filtersCollapse = document.querySelector('#filtersCollapse');

            if (filtersToggle && filtersCollapse) {
                filtersToggle.addEventListener('click', function() {
                    const icon = this.querySelector('i');

                    filtersCollapse.addEventListener('shown.bs.collapse', function() {
                        icon.className = 'fas fa-times';
                    });

                    filtersCollapse.addEventListener('hidden.bs.collapse', function() {
                        icon.className = 'fas fa-filter';
                    });
                });
            }

            // Lazy loading للصور
            if ('IntersectionObserver' in window) {
                const imageObserver = new IntersectionObserver((entries, observer) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            const img = entry.target;
                            img.src = img.dataset.src;
                            img.classList.remove('lazy');
                            imageObserver.unobserve(img);
                        }
                    });
                });

                document.querySelectorAll('img[data-src]').forEach(img => {
                    imageObserver.observe(img);
                });
            }

            // تحسين الأداء - تأخير تحميل المحتوى غير المرئي
            const cards = document.querySelectorAll('.course-card');
            const cardObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('animate-in');
                    }
                });
            }, {
                threshold: 0.1,
                rootMargin: '50px'
            });

            cards.forEach(card => {
                cardObserver.observe(card);
            });
        });

        // دالة لحفظ حالة الفلاتر في localStorage
        function saveFiltersState() {
            const filters = {
                category: document.querySelector('select[name="category"]').value,
                level: document.querySelector('select[name="level"]').value,
                price_type: document.querySelector('select[name="price_type"]').value,
                instructor: document.querySelector('select[name="instructor"]').value,
                sort_by: document.querySelector('select[name="sort_by"]').value
            };

            localStorage.setItem('coursesFilters', JSON.stringify(filters));
        }

        // دالة لاستعادة حالة الفلاتر
        function restoreFiltersState() {
            const savedFilters = localStorage.getItem('coursesFilters');
            if (savedFilters) {
                const filters = JSON.parse(savedFilters);

                Object.keys(filters).forEach(key => {
                    const select = document.querySelector(`select[name="${key}"]`);
                    if (select && filters[key]) {
                        select.value = filters[key];
                    }
                });
            }
        }

        // حفظ الفلاتر عند التغيير
        document.querySelectorAll('.filter-select, .sort-select').forEach(select => {
            select.addEventListener('change', saveFiltersState);
        });
    </script>

    <!-- Schema.org Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "ItemList",
        "name": "<?php echo $pageTitle; ?>",
        "description": "قائمة الكورسات التعليمية المتاحة",
        "numberOfItems": <?php echo $total_courses; ?>,
        "itemListElement": [
            <?php foreach (array_slice($courses, 0, 10) as $index => $course): ?>
            {
                "@type": "Course",
                "position": <?php echo $index + 1; ?>,
                "name": "<?php echo htmlspecialchars($course['title']); ?>",
                "description": "<?php echo htmlspecialchars(substr($course['description'], 0, 160)); ?>",
                "provider": {
                    "@type": "Organization",
                    "name": "<?php echo SITE_NAME; ?>"
                },
                "instructor": {
                    "@type": "Person",
                    "name": "<?php echo htmlspecialchars($course['instructor_name']); ?>"
                },
                "url": "<?php echo SITE_URL; ?>/course-details.php?id=<?php echo $course['id']; ?>"
                <?php if ($course['price'] > 0): ?>
                ,
                "offers": {
                    "@type": "Offer",
                    "price": "<?php echo $course['price']; ?>",
                    "priceCurrency": "<?php echo $course['currency'] ?? 'USD'; ?>"
                }
                <?php endif; ?>
            }<?php echo $index < min(9, count($courses) - 1) ? ',' : ''; ?>
            <?php endforeach; ?>
        ]
    }
    </script>
</body>
</html>