<?php
require_once 'config/database.php';

$adminEmail = '<EMAIL>';
$adminPassword = '123456'; // كلمة المرور الأولية
$hashedPassword = password_hash($adminPassword, PASSWORD_DEFAULT);

try {
    $stmt = $conn->prepare("INSERT INTO users (name, email, phone, password, role, status) VALUES (?, ?, ?, ?, 'admin', 'approved')");
    $stmt->execute(['المدير', $adminEmail, '123456789', $hashedPassword]);
    echo "تم إنشاء حساب المدير بنجاح\n";
    echo "البريد الإلكتروني: " . $adminEmail . "\n";
    echo "كلمة المرور: " . $adminPassword;
} catch(PDOException $e) {
    echo "خطأ: " . $e->getMessage();
}
?>
