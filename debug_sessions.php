<?php
require_once 'config/database.php';

echo "<h2>تشخيص جدول الجلسات</h2>";

try {
    // التحقق من وجود جدول الجلسات
    $stmt = $conn->query("SHOW TABLES LIKE 'sessions'");
    if ($stmt->rowCount() > 0) {
        echo "<p style='color: green;'>✅ جدول sessions موجود</p>";
        
        // عرض هيكل الجدول
        echo "<h3>هيكل جدول sessions:</h3>";
        $stmt = $conn->query("DESCRIBE sessions");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>اسم العمود</th><th>النوع</th><th>NULL</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>" . $column['Field'] . "</td>";
            echo "<td>" . $column['Type'] . "</td>";
            echo "<td>" . $column['Null'] . "</td>";
            echo "<td>" . $column['Key'] . "</td>";
            echo "<td>" . $column['Default'] . "</td>";
            echo "<td>" . $column['Extra'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // التحقق من وجود حقول الفيديو
        $video_fields = ['has_video', 'video_title', 'video_description', 'video_file_path', 'video_size', 'video_uploaded_at'];
        $column_names = array_column($columns, 'Field');
        
        echo "<h3>حقول الفيديو:</h3>";
        foreach ($video_fields as $field) {
            if (in_array($field, $column_names)) {
                echo "<p style='color: green;'>✅ $field موجود</p>";
            } else {
                echo "<p style='color: red;'>❌ $field غير موجود</p>";
            }
        }
        
        // عرض عدد الجلسات
        $stmt = $conn->query("SELECT COUNT(*) as count FROM sessions");
        $count = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "<h3>عدد الجلسات: " . $count['count'] . "</h3>";
        
        // عرض الجلسات إذا كانت موجودة
        if ($count['count'] > 0) {
            echo "<h3>الجلسات الموجودة:</h3>";
            $stmt = $conn->query("SELECT * FROM sessions LIMIT 5");
            $sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            if (!empty($sessions)) {
                // عرض الهيدر
                echo "<tr>";
                foreach (array_keys($sessions[0]) as $key) {
                    echo "<th>$key</th>";
                }
                echo "</tr>";
                
                // عرض البيانات
                foreach ($sessions as $session) {
                    echo "<tr>";
                    foreach ($session as $value) {
                        echo "<td>" . htmlspecialchars($value ?? 'NULL') . "</td>";
                    }
                    echo "</tr>";
                }
            }
            echo "</table>";
        }
        
    } else {
        echo "<p style='color: red;'>❌ جدول sessions غير موجود</p>";
    }
    
    // التحقق من جدول الكورسات
    echo "<h3>التحقق من جدول الكورسات:</h3>";
    $stmt = $conn->query("SHOW TABLES LIKE 'courses'");
    if ($stmt->rowCount() > 0) {
        echo "<p style='color: green;'>✅ جدول courses موجود</p>";
        
        $stmt = $conn->query("SELECT COUNT(*) as count FROM courses");
        $count = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "<p>عدد الكورسات: " . $count['count'] . "</p>";
    } else {
        echo "<p style='color: red;'>❌ جدول courses غير موجود</p>";
    }
    
} catch (PDOException $e) {
    echo "<h3 style='color: red;'>❌ خطأ: " . $e->getMessage() . "</h3>";
}
?>

<p><a href="instructor/session-videos.php" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">العودة لصفحة فيديوهات الجلسات</a></p>
