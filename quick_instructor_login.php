<?php
/**
 * وصول سريع لحساب المدرس
 * Quick instructor login access
 */

require_once 'config/database.php';

echo "<!DOCTYPE html>";
echo "<html dir='rtl' lang='ar'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>وصول سريع - حساب المدرس</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "</head>";
echo "<body>";
echo "<div class='container mt-4'>";
echo "<h2>👨‍🏫 وصول سريع لحساب المدرس</h2>";

try {
    // 1. حذف جداول الأمان المشكلة
    echo "<h4>🔧 إصلاح سريع للأمان</h4>";
    
    $problematic_tables = ['login_attempts', 'failed_logins', 'security_logs'];
    foreach ($problematic_tables as $table) {
        try {
            $conn->exec("DROP TABLE IF EXISTS $table");
            echo "<div class='alert alert-success'>✅ تم حذف جدول: $table</div>";
        } catch (Exception $e) {
            // تجاهل الأخطاء
        }
    }

    // 2. إزالة أعمدة القفل من جدول المستخدمين
    echo "<h4>🔓 إزالة قيود الأمان</h4>";
    
    $lock_columns = ['locked_until', 'failed_login_attempts', 'last_failed_login'];
    foreach ($lock_columns as $column) {
        try {
            $conn->exec("ALTER TABLE users DROP COLUMN IF EXISTS $column");
            echo "<div class='alert alert-success'>✅ تم إزالة عمود: $column</div>";
        } catch (Exception $e) {
            // تجاهل الأخطاء
        }
    }

    // 3. تفعيل جميع المستخدمين
    $stmt = $conn->exec("UPDATE users SET status = 'active'");
    echo "<div class='alert alert-success'>✅ تم تفعيل جميع المستخدمين</div>";

    // 4. إنشاء/تحديث حساب المدرس
    echo "<h4>👨‍🏫 إعداد حساب المدرس</h4>";
    
    $instructor_password = password_hash('instructor123', PASSWORD_DEFAULT);
    
    // حذف المدرس الموجود وإنشاؤه من جديد
    $stmt = $conn->prepare("DELETE FROM users WHERE email = '<EMAIL>'");
    $stmt->execute();
    
    $stmt = $conn->prepare("INSERT INTO users (name, email, password, role, status) VALUES (?, ?, ?, ?, ?)");
    $stmt->execute(['أحمد محمد - مدرب', '<EMAIL>', $instructor_password, 'instructor', 'active']);
    
    echo "<div class='alert alert-success'>✅ تم إنشاء حساب المدرس بنجاح</div>";

    // 5. إنشاء حسابات إضافية للاختبار
    echo "<h4>👥 إنشاء حسابات إضافية</h4>";
    
    $additional_users = [
        ['مدير النظام', '<EMAIL>', password_hash('admin123', PASSWORD_DEFAULT), 'admin'],
        ['سارة أحمد - طالبة', '<EMAIL>', password_hash('student123', PASSWORD_DEFAULT), 'student']
    ];
    
    foreach ($additional_users as $user_data) {
        $stmt = $conn->prepare("DELETE FROM users WHERE email = ?");
        $stmt->execute([$user_data[1]]);
        
        $stmt = $conn->prepare("INSERT INTO users (name, email, password, role, status) VALUES (?, ?, ?, ?, 'active')");
        $stmt->execute($user_data);
        echo "<div class='alert alert-info'>✅ تم إنشاء حساب: {$user_data[0]}</div>";
    }

    // 6. اختبار الحسابات
    echo "<h4>🧪 اختبار الحسابات</h4>";
    
    $test_accounts = [
        ['<EMAIL>', 'instructor123', 'مدرب'],
        ['<EMAIL>', 'admin123', 'مدير'],
        ['<EMAIL>', 'student123', 'طالب']
    ];
    
    foreach ($test_accounts as $account) {
        $stmt = $conn->prepare("SELECT * FROM users WHERE email = ?");
        $stmt->execute([$account[0]]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($user && password_verify($account[1], $user['password']) && $user['status'] === 'active') {
            echo "<div class='alert alert-success'>✅ <strong>{$account[2]}</strong> - {$account[0]} | كلمة المرور: {$account[1]} ✓</div>";
        } else {
            echo "<div class='alert alert-danger'>❌ <strong>{$account[2]}</strong> - مشكلة في الحساب</div>";
        }
    }

    // 7. تنظيف الجلسات
    echo "<h4>🧹 تنظيف الجلسات</h4>";
    
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    session_destroy();
    echo "<div class='alert alert-success'>✅ تم تنظيف بيانات الجلسة</div>";

    // 8. عرض بيانات تسجيل الدخول
    echo "<h4>🔑 بيانات تسجيل الدخول الجاهزة</h4>";
    
    echo "<div class='row'>";
    
    // حساب المدرس
    echo "<div class='col-md-4'>";
    echo "<div class='card border-primary'>";
    echo "<div class='card-header bg-primary text-white text-center'>";
    echo "<h5 class='mb-0'>👨‍🏫 حساب المدرس</h5>";
    echo "</div>";
    echo "<div class='card-body text-center'>";
    echo "<p><strong>البريد الإلكتروني:</strong></p>";
    echo "<h6><code><EMAIL></code></h6>";
    echo "<p><strong>كلمة المرور:</strong></p>";
    echo "<h6><code>instructor123</code></h6>";
    echo "<hr>";
    echo "<p class='text-success'><strong>✅ جاهز للاستخدام</strong></p>";
    echo "<a href='login.php' class='btn btn-primary'>تسجيل الدخول</a>";
    echo "</div>";
    echo "</div>";
    echo "</div>";
    
    // حساب المدير
    echo "<div class='col-md-4'>";
    echo "<div class='card border-danger'>";
    echo "<div class='card-header bg-danger text-white text-center'>";
    echo "<h5 class='mb-0'>👨‍💼 حساب المدير</h5>";
    echo "</div>";
    echo "<div class='card-body text-center'>";
    echo "<p><strong>البريد الإلكتروني:</strong></p>";
    echo "<h6><code><EMAIL></code></h6>";
    echo "<p><strong>كلمة المرور:</strong></p>";
    echo "<h6><code>admin123</code></h6>";
    echo "<hr>";
    echo "<p class='text-success'><strong>✅ جاهز للاستخدام</strong></p>";
    echo "<a href='login.php' class='btn btn-danger'>تسجيل الدخول</a>";
    echo "</div>";
    echo "</div>";
    echo "</div>";
    
    // حساب الطالب
    echo "<div class='col-md-4'>";
    echo "<div class='card border-success'>";
    echo "<div class='card-header bg-success text-white text-center'>";
    echo "<h5 class='mb-0'>👨‍🎓 حساب الطالب</h5>";
    echo "</div>";
    echo "<div class='card-body text-center'>";
    echo "<p><strong>البريد الإلكتروني:</strong></p>";
    echo "<h6><code><EMAIL></code></h6>";
    echo "<p><strong>كلمة المرور:</strong></p>";
    echo "<h6><code>student123</code></h6>";
    echo "<hr>";
    echo "<p class='text-success'><strong>✅ جاهز للاستخدام</strong></p>";
    echo "<a href='login.php' class='btn btn-success'>تسجيل الدخول</a>";
    echo "</div>";
    echo "</div>";
    echo "</div>";
    
    echo "</div>";

    // 9. خطوات تسجيل الدخول
    echo "<h4>📋 خطوات تسجيل الدخول كمدرس</h4>";
    
    echo "<div class='alert alert-info'>";
    echo "<ol class='mb-0'>";
    echo "<li>اضغط على زر <strong>\"تسجيل الدخول\"</strong> في بطاقة المدرس أعلاه</li>";
    echo "<li>في صفحة تسجيل الدخول، اختر <strong>\"مدرب\"</strong></li>";
    echo "<li>أدخل البريد: <code><EMAIL></code></li>";
    echo "<li>أدخل كلمة المرور: <code>instructor123</code></li>";
    echo "<li>اضغط <strong>\"تسجيل الدخول\"</strong></li>";
    echo "</ol>";
    echo "</div>";

    // 10. إحصائيات النظام
    echo "<h4>📊 إحصائيات النظام</h4>";
    
    $stmt = $conn->query("SELECT COUNT(*) as total, COUNT(CASE WHEN status = 'active' THEN 1 END) as active FROM users");
    $stats = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "<div class='row'>";
    echo "<div class='col-md-6'>";
    echo "<div class='card bg-primary text-white'>";
    echo "<div class='card-body text-center'>";
    echo "<h3>{$stats['total']}</h3>";
    echo "<p class='mb-0'>إجمالي المستخدمين</p>";
    echo "</div></div></div>";
    
    echo "<div class='col-md-6'>";
    echo "<div class='card bg-success text-white'>";
    echo "<div class='card-body text-center'>";
    echo "<h3>{$stats['active']}</h3>";
    echo "<p class='mb-0'>مستخدمين نشطين</p>";
    echo "</div></div></div>";
    echo "</div>";

    echo "<div class='alert alert-success mt-4'>";
    echo "<h5>🎉 تم الإصلاح بنجاح!</h5>";
    echo "<ul class='mb-0'>";
    echo "<li>✅ تم حذف جميع جداول الأمان المشكلة</li>";
    echo "<li>✅ تم إزالة قيود القفل من المستخدمين</li>";
    echo "<li>✅ تم إنشاء حساب المدرس وتفعيله</li>";
    echo "<li>✅ تم تنظيف بيانات الجلسات</li>";
    echo "<li>✅ النظام جاهز للاستخدام بدون قيود</li>";
    echo "</ul>";
    echo "</div>";

} catch (Exception $e) {
    echo "<div class='alert alert-danger'>❌ خطأ: " . htmlspecialchars($e->getMessage()) . "</div>";
}

echo "<div class='text-center mt-4'>";
echo "<a href='login.php' class='btn btn-primary btn-lg me-2'>🔐 تسجيل الدخول الآن</a>";
echo "<a href='instructor/dashboard.php' class='btn btn-success btn-lg me-2'>📊 لوحة المدرس</a>";
echo "<a href='admin/dashboard.php' class='btn btn-danger btn-lg me-2'>🏠 لوحة المدير</a>";
echo "<a href='system_health_check.php' class='btn btn-info btn-lg'>🔍 فحص النظام</a>";
echo "</div>";

echo "<div class='mt-4'>";
echo "<div class='alert alert-warning'>";
echo "<h6>💡 ملاحظات مهمة:</h6>";
echo "<ul class='mb-0'>";
echo "<li>تم تعطيل جميع قيود الأمان مؤقتاً لحل مشكلة القفل</li>";
echo "<li>استخدم البيانات المعروضة أعلاه بالضبط</li>";
echo "<li>تأكد من اختيار نوع الحساب الصحيح عند تسجيل الدخول</li>";
echo "<li>إذا واجهت مشاكل، شغل فحص النظام</li>";
echo "</ul>";
echo "</div>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>
