<?php
require_once 'config/database.php';

echo "<h2>إصلاح جدول المستخدمين</h2>";

try {
    // التحقق من هيكل جدول users
    $stmt = $conn->query("DESCRIBE users");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<h3>الأعمدة الموجودة حالياً:</h3>";
    echo "<ul>";
    foreach ($columns as $column) {
        echo "<li>$column</li>";
    }
    echo "</ul>";
    
    // التحقق من وجود عمود full_name
    if (!in_array('full_name', $columns)) {
        echo "<p>إضافة عمود full_name...</p>";
        $conn->exec("ALTER TABLE users ADD COLUMN full_name VARCHAR(255) NULL AFTER username");
        echo "<p style='color: green;'>✅ تم إضافة عمود full_name</p>";
        
        // تحديث البيانات الموجودة
        $conn->exec("UPDATE users SET full_name = username WHERE full_name IS NULL");
        echo "<p style='color: green;'>✅ تم تحديث البيانات الموجودة</p>";
    } else {
        echo "<p style='color: blue;'>ℹ️ عمود full_name موجود بالفعل</p>";
    }
    
    // التحقق من الأعمدة الأخرى المطلوبة
    $required_columns = [
        'phone' => 'VARCHAR(20) NULL',
        'address' => 'TEXT NULL',
        'date_of_birth' => 'DATE NULL',
        'gender' => "ENUM('male', 'female') NULL",
        'profile_image' => 'VARCHAR(500) NULL',
        'bio' => 'TEXT NULL',
        'specialization' => 'VARCHAR(255) NULL',
        'experience_years' => 'INT NULL',
        'education' => 'TEXT NULL',
        'skills' => 'TEXT NULL',
        'social_links' => 'JSON NULL',
        'last_login' => 'TIMESTAMP NULL',
        'email_verified' => 'TINYINT(1) DEFAULT 0',
        'phone_verified' => 'TINYINT(1) DEFAULT 0'
    ];
    
    echo "<h3>إضافة الأعمدة المفقودة:</h3>";
    
    foreach ($required_columns as $column_name => $column_definition) {
        if (!in_array($column_name, $columns)) {
            try {
                $conn->exec("ALTER TABLE users ADD COLUMN $column_name $column_definition");
                echo "<p style='color: green;'>✅ تم إضافة العمود: $column_name</p>";
            } catch (PDOException $e) {
                echo "<p style='color: red;'>❌ خطأ في إضافة العمود $column_name: " . $e->getMessage() . "</p>";
            }
        } else {
            echo "<p style='color: blue;'>ℹ️ العمود $column_name موجود بالفعل</p>";
        }
    }
    
    // تحديث البيانات التجريبية
    echo "<h3>تحديث البيانات التجريبية:</h3>";
    
    $sample_data = [
        'ahmed_student' => [
            'full_name' => 'أحمد محمد علي',
            'phone' => '01234567890',
            'bio' => 'طالب مهتم بتعلم البرمجة وتطوير المواقع'
        ],
        'fatima_student' => [
            'full_name' => 'فاطمة أحمد حسن',
            'phone' => '01234567891',
            'bio' => 'طالبة في كلية الحاسوب، أحب تعلم التقنيات الجديدة'
        ],
        'omar_student' => [
            'full_name' => 'عمر خالد محمود',
            'phone' => '01234567892',
            'bio' => 'مطور مبتدئ يسعى لتطوير مهاراته'
        ],
        'sara_student' => [
            'full_name' => 'سارة عبدالله يوسف',
            'phone' => '01234567893',
            'bio' => 'مهتمة بتصميم المواقع والتطبيقات'
        ],
        'hassan_student' => [
            'full_name' => 'حسن عبدالرحمن طه',
            'phone' => '01234567894',
            'bio' => 'طالب هندسة برمجيات'
        ]
    ];
    
    foreach ($sample_data as $username => $data) {
        $stmt = $conn->prepare("
            UPDATE users 
            SET full_name = ?, phone = ?, bio = ? 
            WHERE username = ?
        ");
        $stmt->execute([
            $data['full_name'],
            $data['phone'],
            $data['bio'],
            $username
        ]);
        
        if ($stmt->rowCount() > 0) {
            echo "<p>✅ تم تحديث بيانات المستخدم: " . $data['full_name'] . "</p>";
        }
    }
    
    // عرض هيكل الجدول المحدث
    echo "<h3>هيكل الجدول بعد التحديث:</h3>";
    $stmt = $conn->query("DESCRIBE users");
    $updated_columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>اسم العمود</th><th>النوع</th><th>NULL</th><th>Default</th></tr>";
    foreach ($updated_columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . ($column['Default'] ?? 'NULL') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // عرض بعض المستخدمين كمثال
    echo "<h3>المستخدمون الموجودون:</h3>";
    $stmt = $conn->query("SELECT id, username, full_name, email, role, phone FROM users LIMIT 10");
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($users)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>اسم المستخدم</th><th>الاسم الكامل</th><th>البريد</th><th>الدور</th><th>الهاتف</th></tr>";
        foreach ($users as $user) {
            echo "<tr>";
            echo "<td>" . $user['id'] . "</td>";
            echo "<td>" . htmlspecialchars($user['username']) . "</td>";
            echo "<td>" . htmlspecialchars($user['full_name'] ?? 'غير محدد') . "</td>";
            echo "<td>" . htmlspecialchars($user['email']) . "</td>";
            echo "<td>" . htmlspecialchars($user['role']) . "</td>";
            echo "<td>" . htmlspecialchars($user['phone'] ?? 'غير محدد') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<h3 style='color: green;'>✅ تم إصلاح جدول المستخدمين بنجاح!</h3>";
    echo "<p><a href='create-test-join-requests.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>إنشاء طلبات انضمام تجريبية</a></p>";
    
} catch (PDOException $e) {
    echo "<h3 style='color: red;'>❌ خطأ: " . $e->getMessage() . "</h3>";
}
?>
