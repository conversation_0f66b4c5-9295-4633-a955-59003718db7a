<?php
require_once 'config/database.php';

echo "<h2>🎓 الطلاب في النظام</h2>";

try {
    // عرض جميع الطلاب
    $stmt = $conn->query("
        SELECT 
            u.id,
            u.username,
            u.email,
            u.full_name,
            u.phone,
            u.created_at,
            u.status,
            COUNT(DISTINCT ce.id) as enrolled_courses,
            COUNT(DISTINCT jr.id) as pending_requests
        FROM users u
        LEFT JOIN course_enrollments ce ON u.id = ce.student_id AND ce.status = 'active'
        LEFT JOIN join_requests jr ON u.id = jr.student_id AND jr.status = 'pending'
        WHERE u.role = 'student'
        GROUP BY u.id
        ORDER BY u.created_at DESC
    ");
    $students = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>📊 إحصائيات الطلاب:</h3>";
    echo "<p><strong>إجمالي الطلاب:</strong> " . count($students) . "</p>";
    
    if (empty($students)) {
        echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h4>⚠️ لا يوجد طلاب في النظام</h4>";
        echo "<p>يمكنك إضافة طلاب بالطرق التالية:</p>";
        echo "<ul>";
        echo "<li><a href='register.php?role=student'>تسجيل طالب جديد</a></li>";
        echo "<li><a href='admin/manage-students.php'>إدارة الطلاب (للأدمن)</a></li>";
        echo "<li><a href='instructor/student-invitations.php'>دعوة طلاب (للمدرب)</a></li>";
        echo "</ul>";
        echo "</div>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 10px;'>ID</th>";
        echo "<th style='padding: 10px;'>الاسم</th>";
        echo "<th style='padding: 10px;'>اسم المستخدم</th>";
        echo "<th style='padding: 10px;'>البريد الإلكتروني</th>";
        echo "<th style='padding: 10px;'>الهاتف</th>";
        echo "<th style='padding: 10px;'>الكورسات المسجل بها</th>";
        echo "<th style='padding: 10px;'>طلبات الانضمام المعلقة</th>";
        echo "<th style='padding: 10px;'>الحالة</th>";
        echo "<th style='padding: 10px;'>تاريخ التسجيل</th>";
        echo "</tr>";
        
        foreach ($students as $student) {
            $status_color = $student['status'] === 'active' ? 'green' : 'red';
            $status_text = $student['status'] === 'active' ? 'نشط' : 'غير نشط';
            
            echo "<tr>";
            echo "<td style='padding: 8px;'>" . $student['id'] . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($student['full_name'] ?? 'غير محدد') . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($student['username']) . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($student['email']) . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($student['phone'] ?? 'غير محدد') . "</td>";
            echo "<td style='padding: 8px; text-align: center;'>" . $student['enrolled_courses'] . "</td>";
            echo "<td style='padding: 8px; text-align: center;'>" . $student['pending_requests'] . "</td>";
            echo "<td style='padding: 8px; color: $status_color; font-weight: bold;'>$status_text</td>";
            echo "<td style='padding: 8px;'>" . date('Y-m-d H:i', strtotime($student['created_at'])) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // عرض تسجيلات الكورسات
    echo "<h3>📚 تسجيلات الكورسات:</h3>";
    $stmt = $conn->query("
        SELECT 
            u.full_name as student_name,
            u.username,
            c.title as course_title,
            ce.enrolled_at,
            ce.status,
            ce.progress
        FROM course_enrollments ce
        INNER JOIN users u ON ce.student_id = u.id
        INNER JOIN courses c ON ce.course_id = c.id
        WHERE u.role = 'student'
        ORDER BY ce.enrolled_at DESC
        LIMIT 10
    ");
    $enrollments = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($enrollments)) {
        echo "<p>لا توجد تسجيلات في الكورسات حالياً</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 10px;'>اسم الطالب</th>";
        echo "<th style='padding: 10px;'>اسم المستخدم</th>";
        echo "<th style='padding: 10px;'>الكورس</th>";
        echo "<th style='padding: 10px;'>تاريخ التسجيل</th>";
        echo "<th style='padding: 10px;'>الحالة</th>";
        echo "<th style='padding: 10px;'>التقدم</th>";
        echo "</tr>";
        
        foreach ($enrollments as $enrollment) {
            $status_color = $enrollment['status'] === 'active' ? 'green' : 'orange';
            $status_text = $enrollment['status'] === 'active' ? 'نشط' : $enrollment['status'];
            
            echo "<tr>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($enrollment['student_name'] ?? 'غير محدد') . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($enrollment['username']) . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($enrollment['course_title']) . "</td>";
            echo "<td style='padding: 8px;'>" . date('Y-m-d H:i', strtotime($enrollment['enrolled_at'])) . "</td>";
            echo "<td style='padding: 8px; color: $status_color; font-weight: bold;'>$status_text</td>";
            echo "<td style='padding: 8px;'>" . ($enrollment['progress'] ?? 0) . "%</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // عرض طلبات الانضمام المعلقة
    echo "<h3>⏳ طلبات الانضمام المعلقة:</h3>";
    $stmt = $conn->query("
        SELECT 
            u.full_name as student_name,
            u.username,
            u.email,
            c.title as course_title,
            jr.created_at,
            jr.status
        FROM join_requests jr
        INNER JOIN users u ON jr.student_id = u.id
        INNER JOIN courses c ON jr.course_id = c.id
        WHERE jr.status = 'pending'
        ORDER BY jr.created_at DESC
    ");
    $pending_requests = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($pending_requests)) {
        echo "<p>لا توجد طلبات انضمام معلقة</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
        echo "<tr style='background: #fff3cd;'>";
        echo "<th style='padding: 10px;'>اسم الطالب</th>";
        echo "<th style='padding: 10px;'>اسم المستخدم</th>";
        echo "<th style='padding: 10px;'>البريد الإلكتروني</th>";
        echo "<th style='padding: 10px;'>الكورس المطلوب</th>";
        echo "<th style='padding: 10px;'>تاريخ الطلب</th>";
        echo "</tr>";
        
        foreach ($pending_requests as $request) {
            echo "<tr>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($request['student_name'] ?? 'غير محدد') . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($request['username']) . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($request['email']) . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($request['course_title']) . "</td>";
            echo "<td style='padding: 8px;'>" . date('Y-m-d H:i', strtotime($request['created_at'])) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
} catch (PDOException $e) {
    echo "<h3 style='color: red;'>❌ خطأ: " . $e->getMessage() . "</h3>";
}

echo "<hr>";
echo "<h3>🔗 روابط مفيدة:</h3>";
echo "<ul>";
echo "<li><a href='register.php?role=student'>تسجيل طالب جديد</a></li>";
echo "<li><a href='admin/manage-students.php'>إدارة الطلاب (أدمن)</a></li>";
echo "<li><a href='instructor/enrollment-requests.php'>طلبات الانضمام (مدرب)</a></li>";
echo "<li><a href='student/dashboard.php'>لوحة تحكم الطالب</a></li>";
echo "<li><a href='courses.php'>عرض الكورسات المتاحة</a></li>";
echo "</ul>";
?>
