<?php
require_once 'config/database.php';
require_once 'config/google.php';
require_once 'includes/functions.php';

if (!isset($_GET['code'])) {
    redirect('login.php');
}

// Exchange code for access token
$ch = curl_init();

curl_setopt($ch, CURLOPT_URL, 'https://oauth2.googleapis.com/token');
curl_setopt($ch, CURLOPT_POST, 1);
curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query([
    'code'          => $_GET['code'],
    'client_id'     => GOOGLE_CLIENT_ID,
    'client_secret' => GOOGLE_CLIENT_SECRET,
    'redirect_uri'  => GOOGLE_REDIRECT_URI,
    'grant_type'    => 'authorization_code'
]));
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

$response = curl_exec($ch);
$token_data = json_decode($response);

if (!isset($token_data->access_token)) {
    redirect('login.php');
}

// Get user info
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'https://www.googleapis.com/oauth2/v2/userinfo');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, ['Authorization: Bearer ' . $token_data->access_token]);

$response = curl_exec($ch);
$user_data = json_decode($response);

try {
    // Check if user exists
    $stmt = $conn->prepare("SELECT * FROM users WHERE google_id = ? OR email = ?");
    $stmt->execute([$user_data->id, $user_data->email]);
    $user = $stmt->fetch();
    
    if ($user) {
        // Update Google ID if not set
        if (!$user['google_id']) {
            $stmt = $conn->prepare("UPDATE users SET google_id = ? WHERE id = ?");
            $stmt->execute([$user_data->id, $user['id']]);
        }
    } else {
        // Create new user
        $stmt = $conn->prepare("INSERT INTO users (name, email, google_id, status, role) VALUES (?, ?, ?, 'pending', 'student')");
        $stmt->execute([$user_data->name, $user_data->email, $user_data->id]);
        $user = [
            'id' => $conn->lastInsertId(),
            'name' => $user_data->name,
            'role' => 'student',
            'status' => 'pending'
        ];
    }
    
    if ($user['status'] === 'pending' && $user['role'] === 'student') {
        $_SESSION['error'] = 'حسابك قيد المراجعة. يرجى انتظار موافقة المدير';
        redirect('login.php');
    }
    
    $_SESSION['user_id'] = $user['id'];
    $_SESSION['name'] = $user['name'];
    $_SESSION['role'] = $user['role'];
    
    if ($user['role'] === 'admin') {
        redirect('admin/dashboard.php');
    } else {
        redirect('dashboard.php');
    }
    
} catch(PDOException $e) {
    $_SESSION['error'] = 'حدث خطأ في النظام';
    redirect('login.php');
}
?>
