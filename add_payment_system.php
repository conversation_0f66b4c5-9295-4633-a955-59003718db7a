<?php
/**
 * إضافة نظام الدفع والعمولات للكورسات
 * Add payment and commission system for courses
 */

require_once 'config/database.php';

echo "<!DOCTYPE html>";
echo "<html dir='rtl' lang='ar'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>إضافة نظام الدفع والعمولات</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "</head>";
echo "<body>";
echo "<div class='container mt-4'>";
echo "<h2>💰 إضافة نظام الدفع والعمولات للكورسات</h2>";

try {
    // 1. تحديث جدول الكورسات
    echo "<h4>📚 تحديث جدول الكورسات</h4>";
    
    // فحص الأعمدة الموجودة
    $stmt = $conn->query("DESCRIBE courses");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    $existing_columns = array_column($columns, 'Field');
    
    // إضافة عمود نوع الكورس (مجاني/مدفوع)
    if (!in_array('course_type', $existing_columns)) {
        $conn->exec("ALTER TABLE courses ADD COLUMN course_type ENUM('free', 'paid') DEFAULT 'free' AFTER description");
        echo "<div class='alert alert-success'>✅ تم إضافة عمود نوع الكورس (course_type)</div>";
    } else {
        echo "<div class='alert alert-info'>ℹ️ عمود نوع الكورس موجود</div>";
    }
    
    // إضافة عمود السعر
    if (!in_array('price', $existing_columns)) {
        $conn->exec("ALTER TABLE courses ADD COLUMN price DECIMAL(10,2) DEFAULT 0.00 AFTER course_type");
        echo "<div class='alert alert-success'>✅ تم إضافة عمود السعر (price)</div>";
    } else {
        echo "<div class='alert alert-info'>ℹ️ عمود السعر موجود</div>";
    }
    
    // إضافة عمود العملة
    if (!in_array('currency', $existing_columns)) {
        $conn->exec("ALTER TABLE courses ADD COLUMN currency VARCHAR(3) DEFAULT 'SAR' AFTER price");
        echo "<div class='alert alert-success'>✅ تم إضافة عمود العملة (currency)</div>";
    } else {
        echo "<div class='alert alert-info'>ℹ️ عمود العملة موجود</div>";
    }

    // 2. إنشاء جدول إعدادات العمولات
    echo "<h4>⚙️ إنشاء جدول إعدادات العمولات</h4>";
    
    $stmt = $conn->query("SHOW TABLES LIKE 'commission_settings'");
    if ($stmt->rowCount() == 0) {
        $conn->exec("
            CREATE TABLE commission_settings (
                id INT AUTO_INCREMENT PRIMARY KEY,
                setting_name VARCHAR(100) UNIQUE NOT NULL,
                setting_value DECIMAL(5,2) NOT NULL,
                min_value DECIMAL(5,2) DEFAULT 0,
                max_value DECIMAL(5,2) DEFAULT 100,
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        echo "<div class='alert alert-success'>✅ تم إنشاء جدول إعدادات العمولات</div>";
        
        // إضافة الإعدادات الافتراضية
        $conn->exec("
            INSERT INTO commission_settings (setting_name, setting_value, min_value, max_value, description) VALUES
            ('platform_commission', 30.00, 15.00, 50.00, 'نسبة عمولة المنصة من الكورسات المدفوعة'),
            ('instructor_commission', 70.00, 50.00, 85.00, 'نسبة المدرب من الكورسات المدفوعة')
        ");
        echo "<div class='alert alert-success'>✅ تم إضافة الإعدادات الافتراضية للعمولات</div>";
    } else {
        echo "<div class='alert alert-info'>ℹ️ جدول إعدادات العمولات موجود</div>";
    }

    // 3. إنشاء جدول المدفوعات
    echo "<h4>💳 إنشاء جدول المدفوعات</h4>";
    
    $stmt = $conn->query("SHOW TABLES LIKE 'course_payments'");
    if ($stmt->rowCount() == 0) {
        $conn->exec("
            CREATE TABLE course_payments (
                id INT AUTO_INCREMENT PRIMARY KEY,
                course_id INT NOT NULL,
                student_id INT NOT NULL,
                amount DECIMAL(10,2) NOT NULL,
                currency VARCHAR(3) DEFAULT 'SAR',
                platform_commission DECIMAL(5,2) NOT NULL,
                instructor_amount DECIMAL(10,2) NOT NULL,
                platform_amount DECIMAL(10,2) NOT NULL,
                payment_method VARCHAR(50) DEFAULT 'online',
                payment_status ENUM('pending', 'completed', 'failed', 'refunded') DEFAULT 'pending',
                transaction_id VARCHAR(255) DEFAULT NULL,
                payment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                notes TEXT DEFAULT NULL,
                
                INDEX idx_course_id (course_id),
                INDEX idx_student_id (student_id),
                INDEX idx_payment_status (payment_status),
                INDEX idx_payment_date (payment_date)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        echo "<div class='alert alert-success'>✅ تم إنشاء جدول المدفوعات</div>";
    } else {
        echo "<div class='alert alert-info'>ℹ️ جدول المدفوعات موجود</div>";
    }

    // 4. إنشاء جدول أرباح المدربين
    echo "<h4>💰 إنشاء جدول أرباح المدربين</h4>";
    
    $stmt = $conn->query("SHOW TABLES LIKE 'instructor_earnings'");
    if ($stmt->rowCount() == 0) {
        $conn->exec("
            CREATE TABLE instructor_earnings (
                id INT AUTO_INCREMENT PRIMARY KEY,
                instructor_id INT NOT NULL,
                course_id INT NOT NULL,
                payment_id INT NOT NULL,
                amount DECIMAL(10,2) NOT NULL,
                currency VARCHAR(3) DEFAULT 'SAR',
                status ENUM('pending', 'paid', 'on_hold') DEFAULT 'pending',
                earned_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                paid_date TIMESTAMP NULL,
                notes TEXT DEFAULT NULL,
                
                INDEX idx_instructor_id (instructor_id),
                INDEX idx_course_id (course_id),
                INDEX idx_status (status),
                INDEX idx_earned_date (earned_date)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        echo "<div class='alert alert-success'>✅ تم إنشاء جدول أرباح المدربين</div>";
    } else {
        echo "<div class='alert alert-info'>ℹ️ جدول أرباح المدربين موجود</div>";
    }

    // 5. تحديث الكورسات الموجودة
    echo "<h4>🔄 تحديث الكورسات الموجودة</h4>";
    
    $stmt = $conn->exec("UPDATE courses SET course_type = 'free', price = 0.00, currency = 'SAR' WHERE course_type IS NULL OR course_type = ''");
    echo "<div class='alert alert-success'>✅ تم تحديث $stmt كورس ليكون مجاني افتراضياً</div>";

    // 6. إضافة بعض الكورسات المدفوعة كمثال
    echo "<h4>💼 إضافة كورسات مدفوعة تجريبية</h4>";
    
    $paid_courses = [
        ['كورس البرمجة المتقدمة', 'تعلم البرمجة المتقدمة مع مشاريع عملية', 'paid', 299.00],
        ['كورس التصميم الاحترافي', 'تعلم التصميم الجرافيكي الاحترافي', 'paid', 199.00],
        ['كورس التسويق الرقمي', 'احترف التسويق الرقمي ووسائل التواصل', 'paid', 149.00]
    ];
    
    // البحث عن مدرب
    $stmt = $conn->query("SELECT id FROM users WHERE role = 'instructor' LIMIT 1");
    $instructor = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($instructor) {
        foreach ($paid_courses as $course_data) {
            $stmt = $conn->prepare("SELECT id FROM courses WHERE title = ?");
            $stmt->execute([$course_data[0]]);
            
            if (!$stmt->fetch()) {
                $stmt = $conn->prepare("
                    INSERT INTO courses (title, description, course_type, price, currency, instructor_id, start_date, end_date, status, created_at) 
                    VALUES (?, ?, ?, ?, 'SAR', ?, '2025-07-01', '2025-12-31', 'active', NOW())
                ");
                $stmt->execute([$course_data[0], $course_data[1], $course_data[2], $course_data[3], $instructor['id']]);
                echo "<div class='alert alert-success'>✅ تم إضافة كورس مدفوع: {$course_data[0]} - {$course_data[3]} ريال</div>";
            }
        }
    }

    // 7. عرض إعدادات العمولات الحالية
    echo "<h4>📊 إعدادات العمولات الحالية</h4>";
    
    $stmt = $conn->query("SELECT * FROM commission_settings");
    $settings = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<div class='table-responsive'>";
    echo "<table class='table table-striped'>";
    echo "<thead class='table-dark'>";
    echo "<tr><th>الإعداد</th><th>القيمة</th><th>الحد الأدنى</th><th>الحد الأقصى</th><th>الوصف</th></tr>";
    echo "</thead><tbody>";
    
    foreach ($settings as $setting) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($setting['setting_name']) . "</td>";
        echo "<td><span class='badge bg-primary'>{$setting['setting_value']}%</span></td>";
        echo "<td>{$setting['min_value']}%</td>";
        echo "<td>{$setting['max_value']}%</td>";
        echo "<td>" . htmlspecialchars($setting['description']) . "</td>";
        echo "</tr>";
    }
    
    echo "</tbody></table>";
    echo "</div>";

    // 8. عرض الكورسات مع الأسعار
    echo "<h4>💰 الكورسات مع الأسعار</h4>";
    
    $stmt = $conn->query("
        SELECT c.*, u.name as instructor_name 
        FROM courses c 
        JOIN users u ON c.instructor_id = u.id 
        ORDER BY c.course_type DESC, c.price DESC
    ");
    $courses = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($courses)) {
        echo "<div class='table-responsive'>";
        echo "<table class='table table-hover'>";
        echo "<thead class='table-light'>";
        echo "<tr><th>الكورس</th><th>المدرب</th><th>النوع</th><th>السعر</th><th>العملة</th><th>الحالة</th></tr>";
        echo "</thead><tbody>";
        
        foreach ($courses as $course) {
            $type_class = $course['course_type'] === 'paid' ? 'warning' : 'success';
            $type_text = $course['course_type'] === 'paid' ? 'مدفوع' : 'مجاني';
            
            echo "<tr>";
            echo "<td>" . htmlspecialchars($course['title']) . "</td>";
            echo "<td>" . htmlspecialchars($course['instructor_name']) . "</td>";
            echo "<td><span class='badge bg-$type_class'>$type_text</span></td>";
            echo "<td>";
            if ($course['course_type'] === 'paid') {
                echo "<strong>" . number_format($course['price'], 2) . "</strong>";
            } else {
                echo "<span class='text-muted'>مجاني</span>";
            }
            echo "</td>";
            echo "<td>{$course['currency']}</td>";
            echo "<td><span class='badge bg-info'>{$course['status']}</span></td>";
            echo "</tr>";
        }
        
        echo "</tbody></table>";
        echo "</div>";
    }

    // 9. إحصائيات النظام
    echo "<h4>📈 إحصائيات النظام</h4>";
    
    $stmt = $conn->query("SELECT COUNT(*) FROM courses WHERE course_type = 'free'");
    $free_courses = $stmt->fetchColumn();
    
    $stmt = $conn->query("SELECT COUNT(*) FROM courses WHERE course_type = 'paid'");
    $paid_courses_count = $stmt->fetchColumn();
    
    $stmt = $conn->query("SELECT AVG(price) FROM courses WHERE course_type = 'paid'");
    $avg_price = $stmt->fetchColumn();
    
    $stmt = $conn->query("SELECT SUM(price) FROM courses WHERE course_type = 'paid'");
    $total_value = $stmt->fetchColumn();
    
    echo "<div class='row'>";
    
    echo "<div class='col-md-3'>";
    echo "<div class='card bg-success text-white'>";
    echo "<div class='card-body text-center'>";
    echo "<h3>$free_courses</h3>";
    echo "<p class='mb-0'>كورسات مجانية</p>";
    echo "</div></div></div>";
    
    echo "<div class='col-md-3'>";
    echo "<div class='card bg-warning text-white'>";
    echo "<div class='card-body text-center'>";
    echo "<h3>$paid_courses_count</h3>";
    echo "<p class='mb-0'>كورسات مدفوعة</p>";
    echo "</div></div></div>";
    
    echo "<div class='col-md-3'>";
    echo "<div class='card bg-info text-white'>";
    echo "<div class='card-body text-center'>";
    echo "<h3>" . number_format($avg_price, 0) . "</h3>";
    echo "<p class='mb-0'>متوسط السعر (ريال)</p>";
    echo "</div></div></div>";
    
    echo "<div class='col-md-3'>";
    echo "<div class='card bg-primary text-white'>";
    echo "<div class='card-body text-center'>";
    echo "<h3>" . number_format($total_value, 0) . "</h3>";
    echo "<p class='mb-0'>إجمالي القيمة (ريال)</p>";
    echo "</div></div></div>";
    
    echo "</div>";

    echo "<div class='alert alert-success mt-4'>";
    echo "<h5>🎉 تم إضافة نظام الدفع والعمولات بنجاح!</h5>";
    echo "<ul class='mb-0'>";
    echo "<li>✅ تم إضافة حقول السعر ونوع الكورس</li>";
    echo "<li>✅ تم إنشاء جداول العمولات والمدفوعات</li>";
    echo "<li>✅ تم تعيين العمولة الافتراضية 30% للمنصة</li>";
    echo "<li>✅ تم إضافة كورسات مدفوعة تجريبية</li>";
    echo "<li>✅ النظام جاهز لإدارة الكورسات المدفوعة</li>";
    echo "</ul>";
    echo "</div>";

} catch (Exception $e) {
    echo "<div class='alert alert-danger'>❌ خطأ: " . htmlspecialchars($e->getMessage()) . "</div>";
}

echo "<div class='text-center mt-4'>";
echo "<a href='instructor/add-course.php' class='btn btn-success btn-lg me-2'>➕ إضافة كورس</a>";
echo "<a href='admin/commission-settings.php' class='btn btn-warning btn-lg me-2'>⚙️ إعدادات العمولات</a>";
echo "<a href='admin/courses-revenue.php' class='btn btn-info btn-lg me-2'>💰 إيرادات الكورسات</a>";
echo "<a href='admin/dashboard.php' class='btn btn-primary btn-lg'>🏠 لوحة التحكم</a>";
echo "</div>";

echo "<div class='mt-4'>";
echo "<div class='alert alert-info'>";
echo "<h6>💡 ملاحظات مهمة:</h6>";
echo "<ul class='mb-0'>";
echo "<li>العمولة الافتراضية للمنصة: 30%</li>";
echo "<li>يمكن للمدير تعديل العمولة بين 15% و 50%</li>";
echo "<li>المدرب يحصل على 70% من سعر الكورس افتراضياً</li>";
echo "<li>الكورسات المجانية لا تخضع للعمولات</li>";
echo "</ul>";
echo "</div>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>
