<?php
require_once 'config/database.php';

try {
    // إنشاء جدول العلاقة بين المدرسين والطلاب
    $conn->exec("CREATE TABLE IF NOT EXISTS instructor_students (
        id INT AUTO_INCREMENT PRIMARY KEY,
        instructor_id INT NOT NULL,
        student_id INT NOT NULL,
        status ENUM('pending', 'active', 'inactive') DEFAULT 'pending',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (instructor_id) REFERENCES users(id),
        FOREIGN KEY (student_id) REFERENCES users(id),
        UNIQUE KEY unique_instructor_student (instructor_id, student_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");

    echo "تم إنشاء جدول العلاقة بين المدرسين والطلاب بنجاح!";
} catch (PDOException $e) {
    echo "خطأ في قاعدة البيانات: " . $e->getMessage();
}
?>
