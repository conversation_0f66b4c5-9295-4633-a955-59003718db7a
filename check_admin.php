<?php
require_once 'config/database.php';

try {
    // Get admin user
    $stmt = $conn->prepare("SELECT * FROM users WHERE email = '<EMAIL>'");
    $stmt->execute();
    $admin = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($admin) {
        echo "معلومات المدير الحالية:\n";
        echo "الاسم: " . $admin['name'] . "\n";
        echo "البريد: " . $admin['email'] . "\n";
        echo "الحالة: " . $admin['status'] . "\n";
        echo "الدور: " . $admin['role'] . "\n\n";
        
        // Create new admin if needed
        $newPassword = 'admin123';
        $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
        
        $stmt = $conn->prepare("DELETE FROM users WHERE email = '<EMAIL>'");
        $stmt->execute();
        
        $stmt = $conn->prepare("INSERT INTO users (name, email, phone, password, role, status) VALUES (?, ?, ?, ?, 'admin', 'approved')");
        $stmt->execute(['المدير', '<EMAIL>', '123456789', $hashedPassword]);
        
        echo "✅ تم إعادة إنشاء حساب المدير بنجاح\n";
        echo "البريد الإلكتروني: <EMAIL>\n";
        echo "كلمة المرور الجديدة: " . $newPassword . "\n";
        
        // Verify password
        echo "\nالتحقق من كلمة المرور:\n";
        if (password_verify($newPassword, $hashedPassword)) {
            echo "✅ كلمة المرور صحيحة";
        } else {
            echo "❌ هناك مشكلة في كلمة المرور";
        }
    } else {
        echo "لا يوجد حساب مدير. جاري إنشاء واحد...";
        
        $newPassword = 'admin123';
        $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
        
        $stmt = $conn->prepare("INSERT INTO users (name, email, phone, password, role, status) VALUES (?, ?, ?, ?, 'admin', 'approved')");
        $stmt->execute(['المدير', '<EMAIL>', '123456789', $hashedPassword]);
        
        echo "✅ تم إنشاء حساب المدير بنجاح\n";
        echo "البريد الإلكتروني: <EMAIL>\n";
        echo "كلمة المرور: " . $newPassword;
    }
    
} catch(PDOException $e) {
    die("خطأ: " . $e->getMessage());
}
?>
