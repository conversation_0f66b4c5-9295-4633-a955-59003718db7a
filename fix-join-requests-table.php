<?php
require_once 'config/database.php';

echo "<h2>إصلاح جدول طلبات الانضمام</h2>";

try {
    // التحقق من وجود الجدول
    $stmt = $conn->query("SHOW TABLES LIKE 'join_requests'");
    if ($stmt->rowCount() == 0) {
        echo "<p style='color: red;'>❌ جدول join_requests غير موجود</p>";
        exit;
    }
    
    // عرض هيكل الجدول الحالي
    echo "<h3>هيكل الجدول الحالي:</h3>";
    $stmt = $conn->query("DESCRIBE join_requests");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>اسم العمود</th><th>النوع</th><th>NULL</th><th>Default</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . ($column['Default'] ?? 'NULL') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    $column_names = array_column($columns, 'Field');
    
    // التحقق من الأعمدة المطلوبة وإضافتها إذا لزم الأمر
    $required_columns = [
        'id' => 'INT AUTO_INCREMENT PRIMARY KEY',
        'student_id' => 'INT NOT NULL',
        'course_id' => 'INT NOT NULL', 
        'message' => 'TEXT',
        'status' => "ENUM('pending', 'approved', 'rejected') DEFAULT 'pending'",
        'created_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
        'processed_at' => 'TIMESTAMP NULL',
        'processed_by' => 'INT NULL'
    ];
    
    echo "<h3>إضافة/تحديث الأعمدة:</h3>";
    
    foreach ($required_columns as $column_name => $column_definition) {
        if (!in_array($column_name, $column_names)) {
            try {
                if ($column_name === 'id') {
                    // تخطي إضافة عمود ID إذا لم يكن موجود (سيتم إنشاء الجدول من جديد)
                    continue;
                }
                $conn->exec("ALTER TABLE join_requests ADD COLUMN $column_name $column_definition");
                echo "<p style='color: green;'>✅ تم إضافة العمود: $column_name</p>";
            } catch (PDOException $e) {
                echo "<p style='color: red;'>❌ خطأ في إضافة العمود $column_name: " . $e->getMessage() . "</p>";
            }
        } else {
            echo "<p style='color: blue;'>ℹ️ العمود $column_name موجود بالفعل</p>";
        }
    }
    
    // إذا كان الجدول يحتوي على أعمدة بأسماء مختلفة، قم بتحديثها
    if (in_array('requested_at', $column_names) && !in_array('created_at', $column_names)) {
        try {
            $conn->exec("ALTER TABLE join_requests CHANGE requested_at created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP");
            echo "<p style='color: green;'>✅ تم تغيير اسم العمود من requested_at إلى created_at</p>";
        } catch (PDOException $e) {
            echo "<p style='color: red;'>❌ خطأ في تغيير اسم العمود: " . $e->getMessage() . "</p>";
        }
    }
    
    // عرض البيانات الموجودة
    echo "<h3>البيانات الموجودة:</h3>";
    try {
        $stmt = $conn->query("SELECT * FROM join_requests ORDER BY id DESC LIMIT 10");
        $requests = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (empty($requests)) {
            echo "<p>لا توجد بيانات في الجدول</p>";
        } else {
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr style='background: #f8f9fa;'>";
            foreach (array_keys($requests[0]) as $header) {
                echo "<th>" . htmlspecialchars($header) . "</th>";
            }
            echo "</tr>";
            
            foreach ($requests as $request) {
                echo "<tr>";
                foreach ($request as $value) {
                    echo "<td>" . htmlspecialchars($value ?? '') . "</td>";
                }
                echo "</tr>";
            }
            echo "</table>";
        }
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ خطأ في قراءة البيانات: " . $e->getMessage() . "</p>";
    }
    
    // إنشاء طلب انضمام تجريبي إذا لم توجد بيانات
    if (empty($requests)) {
        echo "<h3>إنشاء طلب انضمام تجريبي:</h3>";
        
        // جلب طالب وكورس
        $stmt = $conn->query("SELECT id FROM users WHERE role = 'student' LIMIT 1");
        $student = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $stmt = $conn->query("SELECT id FROM courses LIMIT 1");
        $course = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($student && $course) {
            try {
                $stmt = $conn->prepare("
                    INSERT INTO join_requests (student_id, course_id, message, status) 
                    VALUES (?, ?, ?, 'pending')
                ");
                $stmt->execute([
                    $student['id'], 
                    $course['id'], 
                    'أرغب في الانضمام لهذا الكورس لتطوير مهاراتي في البرمجة'
                ]);
                
                echo "<p style='color: green;'>✅ تم إنشاء طلب انضمام تجريبي</p>";
            } catch (PDOException $e) {
                echo "<p style='color: red;'>❌ خطأ في إنشاء طلب تجريبي: " . $e->getMessage() . "</p>";
            }
        }
    }
    
    // عرض هيكل الجدول النهائي
    echo "<h3>هيكل الجدول النهائي:</h3>";
    $stmt = $conn->query("DESCRIBE join_requests");
    $final_columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>اسم العمود</th><th>النوع</th><th>NULL</th><th>Default</th></tr>";
    foreach ($final_columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . ($column['Default'] ?? 'NULL') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h3 style='color: green;'>✅ تم إصلاح جدول طلبات الانضمام!</h3>";
    
    echo "<h3>🔗 اختبار النظام:</h3>";
    echo "<ul>";
    echo "<li><a href='quick-test-join.php'>اختبار سريع</a></li>";
    echo "<li><a href='instructor/course-join-requests.php?course_id=1'>صفحة طلبات الانضمام للمدرب</a></li>";
    echo "<li><a href='test-join-course.php'>اختبار إرسال طلب جديد</a></li>";
    echo "</ul>";
    
} catch (PDOException $e) {
    echo "<h3 style='color: red;'>❌ خطأ: " . $e->getMessage() . "</h3>";
}
?>
