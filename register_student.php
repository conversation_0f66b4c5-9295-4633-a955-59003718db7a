<?php
require_once 'config/database.php';
require_once 'includes/functions.php';

$error = '';
$success = '';

// التحقق من وجود معرف المدرب في الرابط
$instructor_id = isset($_GET['instructor']) ? (int)$_GET['instructor'] : 0;

// التحقق من وجود المدرب
if ($instructor_id > 0) {
    $stmt = $conn->prepare("SELECT id, name FROM users WHERE id = ? AND role = 'instructor' AND status = 'active'");
    $stmt->execute([$instructor_id]);
    $instructor = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$instructor) {
        die('رابط غير صالح');
    }
} else {
    die('رابط غير صالح');
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = sanitize($_POST['name']);
    $email = filter_var($_POST['email'], FILTER_SANITIZE_EMAIL);
    $phone = sanitize($_POST['phone']);
    $password = $_POST['password'];
    
    if (empty($name) || empty($email) || empty($phone) || empty($password)) {
        $error = 'جميع الحقول مطلوبة';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = 'البريد الإلكتروني غير صالح';
    } elseif (strlen($password) < 6) {
        $error = 'يجب أن تكون كلمة المرور 6 أحرف على الأقل';
    } else {
        try {
            $conn->beginTransaction();
            
            // Check if email already exists
            $stmt = $conn->prepare("SELECT id FROM users WHERE email = ?");
            $stmt->execute([$email]);
            
            if ($stmt->rowCount() > 0) {
                $error = 'البريد الإلكتروني مسجل مسبقاً';
            } else {
                // Insert new user
                // إنشاء اسم المستخدم من البريد الإلكتروني
                $username = explode('@', $email)[0];
                $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
                
                $stmt = $conn->prepare("INSERT INTO users (name, email, username, phone, password, role, status) VALUES (?, ?, ?, ?, ?, 'student', 'pending')");
                
                if ($stmt->execute([$name, $email, $username, $phone, $hashedPassword])) {
                    $student_id = $conn->lastInsertId();
                    
                    // ربط الطالب بالمدرب
                    $stmt = $conn->prepare("INSERT INTO instructor_students (instructor_id, student_id) VALUES (?, ?)");
                    $stmt->execute([$instructor_id, $student_id]);
                    
                    $conn->commit();
                    $success = 'تم التسجيل بنجاح. يرجى انتظار موافقة المدرب';
                    
                    // Notify admin (you can implement this later with actual admin email)
                    $adminMessage = "تم تسجيل طالب جديد: $name";
                    // sendEmail('<EMAIL>', 'تسجيل طالب جديد', $adminMessage);
                } else {
                    $conn->rollBack();
                    $error = 'حدث خطأ أثناء حفظ البيانات';
                }
            }
        } catch(PDOException $e) {
            $conn->rollBack();
            error_log("Registration error: " . $e->getMessage());
            if ($e->getCode() == '23000') {
                $error = 'البريد الإلكتروني مسجل مسبقاً';
            } elseif ($e->getCode() == '42S02') {
                $error = 'خطأ في قاعدة البيانات. يرجى التواصل مع مسؤول النظام';
            } else {
                $error = 'حدث خطأ في النظام. يرجى المحاولة مرة أخرى لاحقاً. ' . ($e->getCode() ? 'رمز الخطأ: ' . $e->getCode() : '');
            }
        }
    }
}
?>
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل حساب جديد</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .card { box-shadow: 0 0 15px rgba(0,0,0,0.1); }
    </style>
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header text-center bg-primary text-white">
                        <h3>تسجيل حساب جديد</h3>
                    </div>
                    <div class="card-body">
                        <?php if ($error): ?>
                            <div class="alert alert-danger"><?php echo $error; ?></div>
                        <?php endif; ?>
                        <?php if ($success): ?>
                            <div class="alert alert-success"><?php echo $success; ?></div>
                        <?php endif; ?>
                        
                        <form method="POST" action="">
                            <div class="mb-3">
                                <label for="name" class="form-label">الاسم الكامل</label>
                                <input type="text" class="form-control" id="name" name="name" required>
                            </div>
                            <div class="mb-3">
                                <label for="email" class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control" id="email" name="email" required>
                            </div>
                            <div class="mb-3">
                                <label for="phone" class="form-label">رقم الهاتف</label>
                                <input type="tel" class="form-control" id="phone" name="phone" required>
                            </div>
                            <div class="mb-3">
                                <label for="password" class="form-label">كلمة المرور</label>
                                <input type="password" class="form-control" id="password" name="password" required>
                            </div>
                            <button type="submit" class="btn btn-primary w-100">تسجيل</button>
                        </form>
                        
                        <div class="text-center mt-3">
                            <a href="login.php" class="text-decoration-none">لديك حساب؟ تسجيل الدخول</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
