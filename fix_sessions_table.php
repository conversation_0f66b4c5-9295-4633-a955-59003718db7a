<?php
// إصلاح جدول الجلسات بإضافة العمود المفقود
require_once 'config/database.php';

echo "<h2>إصلاح جدول الجلسات</h2>";

try {
    // التحقق من وجود العمود zoom_join_url
    $stmt = $conn->query("SHOW COLUMNS FROM sessions LIKE 'zoom_join_url'");
    $column_exists = $stmt->rowCount() > 0;
    
    if (!$column_exists) {
        echo "<p>إضافة العمود zoom_join_url...</p>";
        $conn->exec("ALTER TABLE sessions ADD COLUMN zoom_join_url VARCHAR(500) AFTER zoom_meeting_password");
        echo "<p style='color: green;'>✅ تم إضافة العمود zoom_join_url بنجاح</p>";
    } else {
        echo "<p style='color: blue;'>ℹ️ العمود zoom_join_url موجود بالفعل</p>";
    }
    
    // عرض هيكل الجدول الحالي
    echo "<h3>هيكل جدول sessions الحالي:</h3>";
    $stmt = $conn->query("DESCRIBE sessions");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background-color: #f0f0f0;'>";
    echo "<th style='padding: 8px;'>Field</th>";
    echo "<th style='padding: 8px;'>Type</th>";
    echo "<th style='padding: 8px;'>Null</th>";
    echo "<th style='padding: 8px;'>Key</th>";
    echo "<th style='padding: 8px;'>Default</th>";
    echo "</tr>";
    
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td style='padding: 8px;'>" . $column['Field'] . "</td>";
        echo "<td style='padding: 8px;'>" . $column['Type'] . "</td>";
        echo "<td style='padding: 8px;'>" . $column['Null'] . "</td>";
        echo "<td style='padding: 8px;'>" . $column['Key'] . "</td>";
        echo "<td style='padding: 8px;'>" . ($column['Default'] ?? 'NULL') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // تحديث الجلسات الموجودة بروابط افتراضية
    echo "<h3>تحديث الجلسات الموجودة:</h3>";
    $stmt = $conn->query("SELECT id, zoom_meeting_id FROM sessions WHERE zoom_join_url IS NULL AND zoom_meeting_id IS NOT NULL");
    $sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($sessions)) {
        foreach ($sessions as $session) {
            $zoom_url = 'https://zoom.us/j/' . $session['zoom_meeting_id'];
            $update_stmt = $conn->prepare("UPDATE sessions SET zoom_join_url = ? WHERE id = ?");
            $update_stmt->execute([$zoom_url, $session['id']]);
            echo "<p>✅ تم تحديث الجلسة رقم {$session['id']} برابط: $zoom_url</p>";
        }
    } else {
        echo "<p>ℹ️ لا توجد جلسات تحتاج لتحديث</p>";
    }
    
    echo "<h3 style='color: green;'>✅ تم إصلاح جدول الجلسات بنجاح!</h3>";
    echo "<p><a href='instructor/sessions.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>الذهاب لصفحة الجلسات</a></p>";
    echo "<p><a href='instructor/edit-session.php?id=1' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>تعديل الجلسة الأولى</a></p>";
    
} catch (PDOException $e) {
    echo "<h3 style='color: red;'>❌ خطأ: " . $e->getMessage() . "</h3>";
}
?>
