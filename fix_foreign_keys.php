<?php
/**
 * إصلاح مشاكل المفاتيح الخارجية وإعادة إنشاء الجداول
 * Fix foreign key issues and recreate tables
 */

require_once 'config/database.php';

echo "<!DOCTYPE html>";
echo "<html dir='rtl' lang='ar'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>إصلاح المفاتيح الخارجية</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "</head>";
echo "<body>";
echo "<div class='container mt-4'>";
echo "<h2>🔧 إصلاح مشاكل المفاتيح الخارجية</h2>";

try {
    // تعطيل فحص المفاتيح الخارجية مؤقتاً
    $conn->exec("SET FOREIGN_KEY_CHECKS = 0");
    echo "<div class='alert alert-info'>✅ تم تعطيل فحص المفاتيح الخارجية مؤقتاً</div>";

    // 1. حذف الجداول الموجودة وإعادة إنشائها
    echo "<h4>🗑️ حذف الجداول الموجودة</h4>";
    
    $tables_to_drop = [
        'session_attendance',
        'student_grades', 
        'course_enrollments',
        'sessions',
        'join_requests'
    ];
    
    foreach ($tables_to_drop as $table) {
        try {
            $conn->exec("DROP TABLE IF EXISTS $table");
            echo "<div class='alert alert-success'>✅ تم حذف جدول: $table</div>";
        } catch (Exception $e) {
            echo "<div class='alert alert-warning'>⚠️ لم يتم العثور على جدول: $table</div>";
        }
    }

    // 2. التحقق من وجود جدول courses وإنشاؤه إذا لم يكن موجود
    echo "<h4>📚 التحقق من جدول courses</h4>";
    
    $stmt = $conn->query("SHOW TABLES LIKE 'courses'");
    if ($stmt->rowCount() == 0) {
        $conn->exec("
            CREATE TABLE courses (
                id INT AUTO_INCREMENT PRIMARY KEY,
                title VARCHAR(255) NOT NULL,
                description TEXT,
                instructor_id INT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                status ENUM('active','inactive','completed') DEFAULT 'active',
                start_date DATE NOT NULL,
                end_date DATE NOT NULL,
                max_students INT DEFAULT 30,
                category_id INT DEFAULT NULL,
                image_path VARCHAR(255) DEFAULT NULL
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        echo "<div class='alert alert-success'>✅ تم إنشاء جدول courses</div>";
    } else {
        echo "<div class='alert alert-info'>ℹ️ جدول courses موجود</div>";
    }

    // 3. التحقق من وجود جدول users
    echo "<h4>👥 التحقق من جدول users</h4>";
    
    $stmt = $conn->query("SHOW TABLES LIKE 'users'");
    if ($stmt->rowCount() == 0) {
        $conn->exec("
            CREATE TABLE users (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                email VARCHAR(255) UNIQUE NOT NULL,
                password VARCHAR(255) NOT NULL,
                role ENUM('admin', 'instructor', 'student') DEFAULT 'student',
                phone VARCHAR(20) DEFAULT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                status ENUM('active', 'inactive') DEFAULT 'active'
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        echo "<div class='alert alert-success'>✅ تم إنشاء جدول users</div>";
    } else {
        echo "<div class='alert alert-info'>ℹ️ جدول users موجود</div>";
    }

    // 4. إنشاء جدول join_requests بدون مفاتيح خارجية
    echo "<h4>📋 إنشاء جدول join_requests</h4>";
    
    $conn->exec("
        CREATE TABLE join_requests (
            id INT AUTO_INCREMENT PRIMARY KEY,
            course_id INT NOT NULL DEFAULT 1,
            student_name VARCHAR(255) NOT NULL DEFAULT '',
            student_email VARCHAR(255) NOT NULL DEFAULT '',
            student_phone VARCHAR(20) DEFAULT NULL,
            message TEXT DEFAULT NULL,
            status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
            request_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            processed_date TIMESTAMP NULL,
            processed_by INT DEFAULT NULL,
            rejection_reason TEXT DEFAULT NULL,
            
            INDEX idx_course_id (course_id),
            INDEX idx_status (status),
            INDEX idx_email (student_email)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "<div class='alert alert-success'>✅ تم إنشاء جدول join_requests</div>";

    // 5. إنشاء جدول sessions بدون مفاتيح خارجية
    echo "<h4>🎥 إنشاء جدول sessions</h4>";
    
    $conn->exec("
        CREATE TABLE sessions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            course_id INT NOT NULL DEFAULT 1,
            title VARCHAR(255) NOT NULL DEFAULT 'جلسة جديدة',
            description TEXT DEFAULT NULL,
            session_date DATE NOT NULL DEFAULT '2025-01-01',
            start_time TIME NOT NULL DEFAULT '10:00:00',
            end_time TIME NOT NULL DEFAULT '11:00:00',
            zoom_link VARCHAR(500) DEFAULT NULL,
            meeting_id VARCHAR(100) DEFAULT NULL,
            passcode VARCHAR(50) DEFAULT NULL,
            status ENUM('scheduled', 'ongoing', 'completed', 'cancelled') DEFAULT 'scheduled',
            max_attendees INT DEFAULT 100,
            recording_url VARCHAR(500) DEFAULT NULL,
            session_file VARCHAR(500) DEFAULT NULL,
            file_name VARCHAR(255) DEFAULT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            
            INDEX idx_course_id (course_id),
            INDEX idx_session_date (session_date),
            INDEX idx_status (status)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "<div class='alert alert-success'>✅ تم إنشاء جدول sessions</div>";

    // 6. إنشاء جدول course_enrollments بدون مفاتيح خارجية
    echo "<h4>📚 إنشاء جدول course_enrollments</h4>";
    
    $conn->exec("
        CREATE TABLE course_enrollments (
            id INT AUTO_INCREMENT PRIMARY KEY,
            course_id INT NOT NULL DEFAULT 1,
            student_id INT NOT NULL DEFAULT 1,
            enrollment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            status ENUM('active', 'completed', 'dropped', 'suspended') DEFAULT 'active',
            final_grade DECIMAL(5,2) DEFAULT NULL,
            completion_date TIMESTAMP NULL,
            notes TEXT DEFAULT NULL,
            
            INDEX idx_course_id (course_id),
            INDEX idx_student_id (student_id),
            INDEX idx_status (status)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "<div class='alert alert-success'>✅ تم إنشاء جدول course_enrollments</div>";

    // 7. إنشاء جدول student_grades بدون مفاتيح خارجية
    echo "<h4>📝 إنشاء جدول student_grades</h4>";
    
    $conn->exec("
        CREATE TABLE student_grades (
            id INT AUTO_INCREMENT PRIMARY KEY,
            course_id INT NOT NULL DEFAULT 1,
            student_id INT NOT NULL DEFAULT 1,
            assignment_name VARCHAR(255) NOT NULL DEFAULT 'تقييم',
            grade DECIMAL(5,2) NOT NULL DEFAULT 0,
            max_grade DECIMAL(5,2) NOT NULL DEFAULT 100,
            grade_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            notes TEXT DEFAULT NULL,
            graded_by INT NOT NULL DEFAULT 1,
            
            INDEX idx_course_student (course_id, student_id),
            INDEX idx_assignment (assignment_name)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "<div class='alert alert-success'>✅ تم إنشاء جدول student_grades</div>";

    // 8. إنشاء جدول session_attendance بدون مفاتيح خارجية
    echo "<h4>📊 إنشاء جدول session_attendance</h4>";
    
    $conn->exec("
        CREATE TABLE session_attendance (
            id INT AUTO_INCREMENT PRIMARY KEY,
            session_id INT NOT NULL DEFAULT 1,
            student_id INT NOT NULL DEFAULT 1,
            attendance_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            leave_time TIMESTAMP NULL,
            duration_minutes INT DEFAULT 0,
            status ENUM('present', 'absent', 'late', 'left_early') DEFAULT 'present',
            notes TEXT DEFAULT NULL,
            
            INDEX idx_session_id (session_id),
            INDEX idx_student_id (student_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "<div class='alert alert-success'>✅ تم إنشاء جدول session_attendance</div>";

    // 9. إضافة بيانات تجريبية
    echo "<h4>📝 إضافة بيانات تجريبية</h4>";
    
    // التحقق من وجود كورسات
    $stmt = $conn->query("SELECT COUNT(*) FROM courses");
    $courses_count = $stmt->fetchColumn();
    
    if ($courses_count > 0) {
        // إضافة طلبات انضمام
        $sample_requests = [
            [1, 'سارة أحمد محمد', '<EMAIL>', '0501234567', 'أرغب في الانضمام لكورس البرمجة'],
            [1, 'محمد علي حسن', '<EMAIL>', '0507654321', 'مهتم بتعلم البرمجة وتطوير المواقع'],
            [1, 'فاطمة خالد', '<EMAIL>', '0509876543', 'أريد تعلم أساسيات البرمجة'],
            [2, 'أحمد سالم', '<EMAIL>', '0502468135', 'مهتم بتعلم التصميم الجرافيكي'],
            [2, 'نور الدين', '<EMAIL>', '0508642097', 'أريد تطوير مهاراتي في التصميم']
        ];
        
        $stmt = $conn->prepare("INSERT INTO join_requests (course_id, student_name, student_email, student_phone, message) VALUES (?, ?, ?, ?, ?)");
        foreach ($sample_requests as $request) {
            $stmt->execute($request);
        }
        echo "<div class='alert alert-success'>✅ تم إضافة " . count($sample_requests) . " طلب انضمام</div>";
        
        // إضافة جلسات تجريبية
        $sample_sessions = [
            [1, 'مقدمة في البرمجة', 'جلسة تعريفية بأساسيات البرمجة', '2025-06-10', '10:00:00', '11:30:00', 'https://zoom.us/j/123456789'],
            [1, 'أساسيات HTML', 'تعلم لغة HTML لبناء المواقع', '2025-06-12', '10:00:00', '11:30:00', 'https://zoom.us/j/123456790'],
            [1, 'CSS والتنسيق', 'تعلم تنسيق المواقع باستخدام CSS', '2025-06-14', '10:00:00', '11:30:00', 'https://zoom.us/j/123456791'],
            [2, 'أساسيات التصميم', 'مبادئ التصميم الجرافيكي', '2025-06-11', '14:00:00', '15:30:00', 'https://zoom.us/j/123456792'],
            [2, 'استخدام Photoshop', 'تعلم برنامج فوتوشوب', '2025-06-13', '14:00:00', '15:30:00', 'https://zoom.us/j/123456793']
        ];
        
        $stmt = $conn->prepare("INSERT INTO sessions (course_id, title, description, session_date, start_time, end_time, zoom_link) VALUES (?, ?, ?, ?, ?, ?, ?)");
        foreach ($sample_sessions as $session) {
            $stmt->execute($session);
        }
        echo "<div class='alert alert-success'>✅ تم إضافة " . count($sample_sessions) . " جلسة</div>";
        
    } else {
        echo "<div class='alert alert-warning'>⚠️ لا توجد كورسات، لم يتم إضافة بيانات تجريبية</div>";
    }

    // 10. إنشاء مجلدات الملفات
    echo "<h4>📁 إنشاء مجلدات الملفات</h4>";
    
    $directories = [
        'uploads/courses',
        'uploads/sessions', 
        'uploads/assignments',
        'uploads/materials'
    ];
    
    foreach ($directories as $dir) {
        if (!file_exists($dir)) {
            if (mkdir($dir, 0777, true)) {
                echo "<div class='alert alert-success'>✅ تم إنشاء مجلد: $dir</div>";
            } else {
                echo "<div class='alert alert-danger'>❌ فشل في إنشاء مجلد: $dir</div>";
            }
        } else {
            echo "<div class='alert alert-info'>ℹ️ مجلد موجود: $dir</div>";
        }
    }

    // إعادة تفعيل فحص المفاتيح الخارجية
    $conn->exec("SET FOREIGN_KEY_CHECKS = 1");
    echo "<div class='alert alert-info'>✅ تم إعادة تفعيل فحص المفاتيح الخارجية</div>";

    // عرض ملخص النتائج
    echo "<h4>📊 ملخص النتائج</h4>";
    echo "<div class='row'>";
    
    // عدد الكورسات
    $stmt = $conn->query("SELECT COUNT(*) FROM courses");
    $courses_count = $stmt->fetchColumn();
    echo "<div class='col-md-2'>";
    echo "<div class='card bg-primary text-white'>";
    echo "<div class='card-body text-center'>";
    echo "<h3>$courses_count</h3>";
    echo "<p class='mb-0'>كورسات</p>";
    echo "</div></div></div>";

    // عدد الجلسات
    $stmt = $conn->query("SELECT COUNT(*) FROM sessions");
    $sessions_count = $stmt->fetchColumn();
    echo "<div class='col-md-2'>";
    echo "<div class='card bg-success text-white'>";
    echo "<div class='card-body text-center'>";
    echo "<h3>$sessions_count</h3>";
    echo "<p class='mb-0'>جلسات</p>";
    echo "</div></div></div>";

    // عدد طلبات الانضمام
    $stmt = $conn->query("SELECT COUNT(*) FROM join_requests");
    $requests_count = $stmt->fetchColumn();
    echo "<div class='col-md-2'>";
    echo "<div class='card bg-warning text-white'>";
    echo "<div class='card-body text-center'>";
    echo "<h3>$requests_count</h3>";
    echo "<p class='mb-0'>طلبات انضمام</p>";
    echo "</div></div></div>";

    // عدد المستخدمين
    $stmt = $conn->query("SELECT COUNT(*) FROM users");
    $users_count = $stmt->fetchColumn();
    echo "<div class='col-md-2'>";
    echo "<div class='card bg-info text-white'>";
    echo "<div class='card-body text-center'>";
    echo "<h3>$users_count</h3>";
    echo "<p class='mb-0'>مستخدمين</p>";
    echo "</div></div></div>";

    // عدد التسجيلات
    $stmt = $conn->query("SELECT COUNT(*) FROM course_enrollments");
    $enrollments_count = $stmt->fetchColumn();
    echo "<div class='col-md-2'>";
    echo "<div class='card bg-secondary text-white'>";
    echo "<div class='card-body text-center'>";
    echo "<h3>$enrollments_count</h3>";
    echo "<p class='mb-0'>تسجيلات</p>";
    echo "</div></div></div>";

    // عدد الدرجات
    $stmt = $conn->query("SELECT COUNT(*) FROM student_grades");
    $grades_count = $stmt->fetchColumn();
    echo "<div class='col-md-2'>";
    echo "<div class='card bg-dark text-white'>";
    echo "<div class='card-body text-center'>";
    echo "<h3>$grades_count</h3>";
    echo "<p class='mb-0'>درجات</p>";
    echo "</div></div></div>";

    echo "</div>";

    echo "<div class='alert alert-success mt-4'>";
    echo "<h5>🎉 تم الإصلاح بنجاح!</h5>";
    echo "<p>جميع الجداول تم إنشاؤها بدون مفاتيح خارجية لتجنب مشاكل التبعيات.</p>";
    echo "<p>يمكنك الآن استخدام النظام بدون مشاكل في المفاتيح الخارجية.</p>";
    echo "</div>";

} catch (Exception $e) {
    echo "<div class='alert alert-danger'>❌ خطأ: " . htmlspecialchars($e->getMessage()) . "</div>";
    
    // إعادة تفعيل فحص المفاتيح الخارجية في حالة الخطأ
    try {
        $conn->exec("SET FOREIGN_KEY_CHECKS = 1");
    } catch (Exception $e2) {
        // تجاهل
    }
}

echo "<div class='mt-4'>";
echo "<a href='instructor/course-details.php?id=1' class='btn btn-primary btn-lg me-2'>📋 تفاصيل كورس</a>";
echo "<a href='instructor/course-students.php?course_id=1' class='btn btn-success btn-lg me-2'>👥 الطلاب</a>";
echo "<a href='instructor/course-grades.php?course_id=1' class='btn btn-warning btn-lg me-2'>📝 الدرجات</a>";
echo "<a href='instructor/course-sessions.php?course_id=1' class='btn btn-info btn-lg me-2'>🎥 الجلسات</a>";
echo "<a href='instructor/courses.php' class='btn btn-secondary btn-lg'>📚 جميع الكورسات</a>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>
