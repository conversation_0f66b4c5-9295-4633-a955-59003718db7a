<?php
require_once 'config/database.php';

try {
    $newPassword = 'admin123';
    $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
    
    $stmt = $conn->prepare("UPDATE users SET password = ? WHERE email = '<EMAIL>'");
    $stmt->execute([$hashedPassword]);
    
    echo "✅ تم تحديث كلمة مرور المدير بنجاح\n";
    echo "البريد الإلكتروني: <EMAIL>\n";
    echo "كلمة المرور الجديدة: " . $newPassword;
    
} catch(PDOException $e) {
    die("خطأ: " . $e->getMessage());
}
?>
