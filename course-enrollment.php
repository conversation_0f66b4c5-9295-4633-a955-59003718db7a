<?php
require_once 'config/database.php';
require_once 'includes/functions.php';

$course_id = isset($_GET['course_id']) ? (int)$_GET['course_id'] : 0;
$error = '';
$success = '';

// جلب معلومات الكورس
try {
    $stmt = $conn->prepare("
        SELECT c.*, u.name as instructor_name, u.email as instructor_email,
               (SELECT COUNT(*) FROM course_enrollments WHERE course_id = c.id AND status = 'active') as enrolled_students
        FROM courses c
        JOIN users u ON c.instructor_id = u.id
        WHERE c.id = ? AND c.status = 'active'
    ");
    $stmt->execute([$course_id]);
    $course = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$course) {
        $error = 'الكورس غير موجود أو غير متاح';
    }
} catch (PDOException $e) {
    $error = 'حدث خطأ في جلب معلومات الكورس';
}

// التحقق من حالة التسجيل
$enrollment_status = null;
$existing_payment = null;

if (isLoggedIn() && $course) {
    try {
        // التحقق من التسجيل الحالي
        $stmt = $conn->prepare("
            SELECT * FROM course_enrollments 
            WHERE course_id = ? AND student_id = ?
        ");
        $stmt->execute([$course_id, $_SESSION['user_id']]);
        $enrollment_status = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // التحقق من المدفوعات السابقة
        $stmt = $conn->prepare("
            SELECT * FROM course_payments 
            WHERE course_id = ? AND student_id = ? 
            ORDER BY payment_date DESC LIMIT 1
        ");
        $stmt->execute([$course_id, $_SESSION['user_id']]);
        $existing_payment = $stmt->fetch(PDO::FETCH_ASSOC);
        
    } catch (PDOException $e) {
        // تجاهل الخطأ
    }
}

// معالجة طلب التسجيل
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['enroll'])) {
    if (!isLoggedIn()) {
        header('Location: login.php?redirect=' . urlencode($_SERVER['REQUEST_URI']));
        exit;
    }
    
    if (!isStudent()) {
        $error = 'يمكن للطلاب فقط التسجيل في الكورسات';
    } else {
        try {
            // التحقق من عدم وجود تسجيل سابق
            $stmt = $conn->prepare("
                SELECT * FROM course_enrollments 
                WHERE course_id = ? AND student_id = ?
            ");
            $stmt->execute([$course_id, $_SESSION['user_id']]);
            $existing_enrollment = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($existing_enrollment) {
                if ($existing_enrollment['status'] === 'active') {
                    $error = 'أنت مسجل بالفعل في هذا الكورس';
                } else {
                    $error = 'لديك طلب تسجيل معلق لهذا الكورس';
                }
            } else {
                // إنشاء طلب انضمام أولاً
                $stmt = $conn->prepare("
                    INSERT INTO join_requests (student_id, course_id, message, status, requested_at)
                    VALUES (?, ?, ?, 'pending', NOW())
                ");
                $stmt->execute([$_SESSION['user_id'], $course_id, 'طلب انضمام من صفحة التسجيل']);

                $success = 'تم إرسال طلب الانضمام بنجاح! سيتم مراجعة طلبك من قبل المدرب وإشعارك بالنتيجة.';
            }
            
        } catch (PDOException $e) {
            $error = 'حدث خطأ أثناء التسجيل. يرجى المحاولة مرة أخرى';
        }
    }
}

$pageTitle = $course ? 'التسجيل في ' . $course['title'] : 'التسجيل في الكورس';
?>

<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($pageTitle); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .enrollment-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            margin-top: 50px;
        }
        
        .course-header {
            background: linear-gradient(45deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }
        
        .course-image {
            width: 120px;
            height: 120px;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 3rem;
        }
        
        .enrollment-content {
            padding: 40px;
        }
        
        .price-display {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .price-display.free {
            background: linear-gradient(45deg, #17a2b8, #6f42c1);
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
        }
        
        .feature-list li {
            padding: 10px 0;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            align-items: center;
        }
        
        .feature-list li:last-child {
            border-bottom: none;
        }
        
        .feature-list i {
            color: #28a745;
            margin-left: 10px;
            width: 20px;
        }
        
        .btn-enroll {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 25px;
            padding: 15px 40px;
            font-size: 1.1rem;
            font-weight: bold;
            color: white;
            width: 100%;
            transition: all 0.3s ease;
        }
        
        .btn-enroll:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
            color: white;
        }
        
        .btn-enroll:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        
        .status-badge {
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: bold;
            text-align: center;
            margin-bottom: 20px;
        }
        
        .status-enrolled {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
        }
        
        .status-pending {
            background: linear-gradient(45deg, #ffc107, #fd7e14);
            color: white;
        }
        
        .login-prompt {
            background: linear-gradient(45deg, #6f42c1, #e83e8c);
            color: white;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-graduation-cap text-primary me-2"></i>
                منصة التعلم
            </a>
            
            <div class="d-flex">
                <a href="course-details.php?id=<?php echo $course_id; ?>" class="btn btn-outline-primary me-2">
                    <i class="fas fa-arrow-right me-1"></i>
                    العودة للتفاصيل
                </a>
                
                <?php if (isLoggedIn()): ?>
                    <a href="<?php echo $_SESSION['role']; ?>/dashboard.php" class="btn btn-primary">
                        <i class="fas fa-tachometer-alt me-1"></i>
                        لوحة التحكم
                    </a>
                <?php else: ?>
                    <a href="login.php" class="btn btn-primary">
                        <i class="fas fa-sign-in-alt me-1"></i>
                        تسجيل الدخول
                    </a>
                <?php endif; ?>
            </div>
        </div>
    </nav>

    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <?php if ($error): ?>
                    <div class="alert alert-danger mt-4">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <?php echo htmlspecialchars($error); ?>
                    </div>
                <?php endif; ?>
                
                <?php if ($success): ?>
                    <div class="alert alert-success mt-4">
                        <i class="fas fa-check-circle me-2"></i>
                        <?php echo htmlspecialchars($success); ?>
                    </div>
                <?php endif; ?>

                <?php if ($course): ?>
                    <div class="enrollment-container">
                        <!-- رأس الكورس -->
                        <div class="course-header">
                            <div class="course-image">
                                <i class="fas fa-graduation-cap"></i>
                            </div>
                            <h2><?php echo htmlspecialchars($course['title']); ?></h2>
                            <p class="mb-0">مع المدرب: <?php echo htmlspecialchars($course['instructor_name']); ?></p>
                        </div>

                        <div class="enrollment-content">
                            <!-- عرض السعر -->
                            <div class="price-display <?php echo $course['course_type'] === 'free' ? 'free' : ''; ?>">
                                <?php if ($course['course_type'] === 'paid'): ?>
                                    <h3 class="mb-2">
                                        <?php echo number_format($course['price'], 0); ?> 
                                        <?php echo $course['currency']; ?>
                                    </h3>
                                    <p class="mb-0">دفعة واحدة - وصول مدى الحياة</p>
                                <?php else: ?>
                                    <h3 class="mb-2">مجاني تماماً</h3>
                                    <p class="mb-0">ابدأ التعلم الآن بدون أي تكلفة</p>
                                <?php endif; ?>
                            </div>

                            <!-- حالة التسجيل -->
                            <?php if ($enrollment_status): ?>
                                <?php if ($enrollment_status['status'] === 'active'): ?>
                                    <div class="status-badge status-enrolled">
                                        <i class="fas fa-check-circle me-2"></i>
                                        أنت مسجل في هذا الكورس
                                    </div>
                                    <div class="text-center">
                                        <a href="student/course-content.php?course_id=<?php echo $course_id; ?>" 
                                           class="btn btn-enroll">
                                            <i class="fas fa-play me-2"></i>
                                            ادخل للكورس
                                        </a>
                                    </div>
                                <?php else: ?>
                                    <div class="status-badge status-pending">
                                        <i class="fas fa-clock me-2"></i>
                                        طلب التسجيل معلق
                                    </div>
                                <?php endif; ?>
                            <?php elseif (!isLoggedIn()): ?>
                                <!-- دعوة لتسجيل الدخول -->
                                <div class="login-prompt">
                                    <i class="fas fa-user-plus fa-3x mb-3"></i>
                                    <h4>سجل دخولك للمتابعة</h4>
                                    <p class="mb-4">يجب تسجيل الدخول أو إنشاء حساب جديد للتسجيل في الكورس</p>
                                    <div class="d-grid gap-2">
                                        <a href="login.php?redirect=<?php echo urlencode($_SERVER['REQUEST_URI']); ?>" 
                                           class="btn btn-light btn-lg">
                                            <i class="fas fa-sign-in-alt me-2"></i>
                                            تسجيل الدخول
                                        </a>
                                        <a href="register.php?redirect=<?php echo urlencode($_SERVER['REQUEST_URI']); ?>" 
                                           class="btn btn-outline-light btn-lg">
                                            <i class="fas fa-user-plus me-2"></i>
                                            إنشاء حساب جديد
                                        </a>
                                    </div>
                                </div>
                            <?php else: ?>
                                <!-- نموذج التسجيل -->
                                <div class="mb-4">
                                    <h5>ما ستحصل عليه:</h5>
                                    <ul class="feature-list">
                                        <li>
                                            <i class="fas fa-video"></i>
                                            وصول لجميع فيديوهات الكورس
                                        </li>
                                        <li>
                                            <i class="fas fa-download"></i>
                                            إمكانية تحميل الفيديوهات
                                        </li>
                                        <li>
                                            <i class="fas fa-file-alt"></i>
                                            المواد التعليمية والملفات
                                        </li>
                                        <li>
                                            <i class="fas fa-infinity"></i>
                                            وصول مدى الحياة
                                        </li>
                                        <li>
                                            <i class="fas fa-certificate"></i>
                                            شهادة إتمام الكورس
                                        </li>
                                        <li>
                                            <i class="fas fa-headset"></i>
                                            دعم فني من المدرب
                                        </li>
                                    </ul>
                                </div>

                                <form method="POST">
                                    <div class="text-center">
                                        <button type="submit" name="enroll" class="btn btn-enroll">
                                            <i class="fas fa-rocket me-2"></i>
                                            <?php if ($course['course_type'] === 'paid'): ?>
                                                ادفع واشترك الآن
                                            <?php else: ?>
                                                اشترك مجاناً الآن
                                            <?php endif; ?>
                                        </button>
                                        
                                        <p class="text-muted mt-3 mb-0">
                                            <i class="fas fa-shield-alt me-1"></i>
                                            ضمان استرداد المال خلال 30 يوم
                                        </p>
                                    </div>
                                </form>
                            <?php endif; ?>

                            <!-- معلومات إضافية -->
                            <div class="mt-4 pt-4 border-top">
                                <div class="row text-center">
                                    <div class="col-4">
                                        <i class="fas fa-users fa-2x text-primary mb-2"></i>
                                        <h6><?php echo $course['enrolled_students']; ?></h6>
                                        <small class="text-muted">طالب مسجل</small>
                                    </div>
                                    <div class="col-4">
                                        <i class="fas fa-clock fa-2x text-success mb-2"></i>
                                        <h6>مدى الحياة</h6>
                                        <small class="text-muted">وصول دائم</small>
                                    </div>
                                    <div class="col-4">
                                        <i class="fas fa-mobile-alt fa-2x text-info mb-2"></i>
                                        <h6>جميع الأجهزة</h6>
                                        <small class="text-muted">هاتف وكمبيوتر</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="enrollment-container">
                        <div class="enrollment-content text-center">
                            <i class="fas fa-exclamation-triangle fa-5x text-warning mb-4"></i>
                            <h3>الكورس غير موجود</h3>
                            <p class="text-muted mb-4">الكورس المطلوب غير متاح أو تم حذفه</p>
                            <a href="courses.php" class="btn btn-primary">
                                <i class="fas fa-search me-2"></i>
                                تصفح الكورسات المتاحة
                            </a>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
