<?php
require_once 'config/database.php';

echo "<h2>إصلاح نظام طلبات الانضمام</h2>";

try {
    // 1. إصلاح جدول student_grades
    echo "<h3>إصلاح جدول student_grades...</h3>";
    
    // التحقق من وجود عمود graded_at
    $columns = $conn->query("SHOW COLUMNS FROM student_grades LIKE 'graded_at'")->fetchAll();
    if (empty($columns)) {
        $conn->exec("ALTER TABLE student_grades ADD COLUMN graded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP");
        echo "<p>✅ تم إضافة عمود graded_at</p>";
    }
    
    // التحقق من وجود عمود graded_by
    $columns = $conn->query("SHOW COLUMNS FROM student_grades LIKE 'graded_by'")->fetchAll();
    if (empty($columns)) {
        $conn->exec("ALTER TABLE student_grades ADD COLUMN graded_by INT NULL");
        echo "<p>✅ تم إضافة عمود graded_by</p>";
    }
    
    // 2. إنشاء جدول course_join_requests إذا لم يكن موجود
    echo "<h3>إنشاء جدول course_join_requests...</h3>";
    
    $conn->exec("
        CREATE TABLE IF NOT EXISTS course_join_requests (
            id INT AUTO_INCREMENT PRIMARY KEY,
            course_id INT NOT NULL,
            student_id INT NOT NULL,
            message TEXT DEFAULT NULL,
            status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
            requested_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            processed_at TIMESTAMP NULL,
            processed_by INT DEFAULT NULL,
            notes TEXT DEFAULT NULL,
            INDEX idx_course_id (course_id),
            INDEX idx_student_id (student_id),
            INDEX idx_status (status),
            UNIQUE KEY unique_request (course_id, student_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "<p>✅ تم إنشاء جدول course_join_requests</p>";
    
    // 3. إضافة طلبات انضمام تجريبية
    echo "<h3>إضافة طلبات انضمام تجريبية...</h3>";
    
    // جلب الطلاب والكورسات
    $students = $conn->query("SELECT id, name FROM users WHERE role = 'student' LIMIT 5")->fetchAll();
    $courses = $conn->query("SELECT id, title, instructor_id FROM courses WHERE status = 'active' LIMIT 3")->fetchAll();
    
    if (!empty($students) && !empty($courses)) {
        $messages = [
            'أرغب في الانضمام لهذا الكورس لتطوير مهاراتي',
            'أتطلع للاستفادة من هذا الكورس في مجال عملي',
            'سمعت عن جودة هذا الكورس وأريد المشاركة',
            'أحتاج هذا الكورس لإكمال دراستي',
            'أريد تعلم مهارات جديدة من خلال هذا الكورس'
        ];
        
        foreach ($students as $student) {
            foreach ($courses as $course) {
                // إضافة طلب انضمام عشوائي
                if (rand(1, 100) <= 60) { // 60% احتمالية
                    $check_request = $conn->prepare("
                        SELECT id FROM course_join_requests 
                        WHERE course_id = ? AND student_id = ?
                    ");
                    $check_request->execute([$course['id'], $student['id']]);
                    
                    if (!$check_request->fetch()) {
                        $insert_request = $conn->prepare("
                            INSERT INTO course_join_requests (course_id, student_id, message, requested_at)
                            VALUES (?, ?, ?, NOW() - INTERVAL ? HOUR)
                        ");
                        
                        $random_message = $messages[array_rand($messages)];
                        $hours_ago = rand(1, 72);
                        
                        $insert_request->execute([
                            $course['id'],
                            $student['id'],
                            $random_message,
                            $hours_ago
                        ]);
                        
                        echo "<p>📝 تم إضافة طلب انضمام: {$student['name']} → {$course['title']}</p>";
                    }
                }
            }
        }
    }
    
    // 4. إضافة طلاب جدد لديهم طلبات انضمام
    echo "<h3>إضافة طلاب جدد مع طلبات انضمام...</h3>";
    
    $new_students = [
        ['name' => 'علي حسن محمد', 'email' => '<EMAIL>', 'phone' => '0501111111'],
        ['name' => 'زينب أحمد علي', 'email' => '<EMAIL>', 'phone' => '0502222222'],
        ['name' => 'حسام محمود سالم', 'email' => '<EMAIL>', 'phone' => '0503333333']
    ];
    
    foreach ($new_students as $student_data) {
        // التحقق من عدم وجود الطالب
        $check_user = $conn->prepare("SELECT id FROM users WHERE email = ?");
        $check_user->execute([$student_data['email']]);
        
        if (!$check_user->fetch()) {
            // إنشاء الطالب
            $insert_user = $conn->prepare("
                INSERT INTO users (name, email, phone, password, role, status)
                VALUES (?, ?, ?, ?, 'student', 'active')
            ");
            $insert_user->execute([
                $student_data['name'],
                $student_data['email'],
                $student_data['phone'],
                password_hash('123456', PASSWORD_DEFAULT)
            ]);
            
            $student_id = $conn->lastInsertId();
            echo "<p>✅ تم إنشاء الطالب: {$student_data['name']}</p>";
            
            // إضافة طلب انضمام لكورس عشوائي
            if (!empty($courses)) {
                $random_course = $courses[array_rand($courses)];
                $random_message = $messages[array_rand($messages)];
                
                $insert_request = $conn->prepare("
                    INSERT INTO course_join_requests (course_id, student_id, message, requested_at)
                    VALUES (?, ?, ?, NOW() - INTERVAL ? MINUTE)
                ");
                
                $insert_request->execute([
                    $random_course['id'],
                    $student_id,
                    $random_message,
                    rand(30, 1440) // من 30 دقيقة إلى 24 ساعة
                ]);
                
                echo "<p>&nbsp;&nbsp;📝 تم إضافة طلب انضمام للكورس: {$random_course['title']}</p>";
            }
        }
    }
    
    // 5. عرض الإحصائيات
    echo "<h3>الإحصائيات النهائية:</h3>";
    
    $stats = $conn->query("
        SELECT 
            (SELECT COUNT(*) FROM course_join_requests WHERE status = 'pending') as pending_requests,
            (SELECT COUNT(*) FROM course_join_requests WHERE status = 'approved') as approved_requests,
            (SELECT COUNT(*) FROM course_join_requests WHERE status = 'rejected') as rejected_requests,
            (SELECT COUNT(*) FROM course_enrollments) as total_enrollments
    ")->fetch();
    
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p><strong>⏳ طلبات معلقة:</strong> {$stats['pending_requests']}</p>";
    echo "<p><strong>✅ طلبات مقبولة:</strong> {$stats['approved_requests']}</p>";
    echo "<p><strong>❌ طلبات مرفوضة:</strong> {$stats['rejected_requests']}</p>";
    echo "<p><strong>📚 إجمالي التسجيلات:</strong> {$stats['total_enrollments']}</p>";
    echo "</div>";
    
    echo "<p style='color: green; font-weight: bold;'>✅ تم إصلاح نظام طلبات الانضمام بنجاح!</p>";
    echo "<p><a href='instructor/enrollment-requests.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>عرض طلبات الانضمام</a></p>";
    echo "<p><a href='instructor/students.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;'>عرض الطلاب</a></p>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>خطأ في قاعدة البيانات: " . $e->getMessage() . "</p>";
}
?>
