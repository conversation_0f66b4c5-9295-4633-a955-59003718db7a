<?php
require_once 'config/database.php';

echo "<h2>إصلاح جدول التصنيفات</h2>";

try {
    // التحقق من وجود جدول categories
    $stmt = $conn->query("SHOW TABLES LIKE 'categories'");
    if ($stmt->rowCount() == 0) {
        echo "<p>إنشاء جدول categories...</p>";
        $conn->exec("CREATE TABLE categories (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            description TEXT,
            icon VARCHAR(100),
            color VARCHAR(20),
            status ENUM('active', 'inactive') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_status (status)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
        echo "<p style='color: green;'>✅ تم إنشاء جدول categories</p>";
        
        // إضافة تصنيفات افتراضية
        $default_categories = [
            ['البرمجة', 'كورسات البرمجة وتطوير البرمجيات', 'fas fa-code', '#007bff'],
            ['التصميم', 'كورسات التصميم الجرافيكي وتصميم المواقع', 'fas fa-paint-brush', '#28a745'],
            ['التسويق', 'كورسات التسويق الرقمي والتسويق الإلكتروني', 'fas fa-bullhorn', '#ffc107'],
            ['الأعمال', 'كورسات إدارة الأعمال وريادة الأعمال', 'fas fa-briefcase', '#dc3545'],
            ['اللغات', 'كورسات تعلم اللغات المختلفة', 'fas fa-language', '#6f42c1'],
            ['العلوم', 'كورسات العلوم والرياضيات', 'fas fa-flask', '#20c997'],
            ['التقنية', 'كورسات التقنية والذكاء الاصطناعي', 'fas fa-robot', '#fd7e14'],
            ['الصحة', 'كورسات الصحة واللياقة البدنية', 'fas fa-heartbeat', '#e83e8c']
        ];
        
        foreach ($default_categories as $category) {
            $stmt = $conn->prepare("INSERT INTO categories (name, description, icon, color) VALUES (?, ?, ?, ?)");
            $stmt->execute($category);
        }
        
        echo "<p style='color: green;'>✅ تم إضافة " . count($default_categories) . " تصنيف افتراضي</p>";
    } else {
        echo "<p style='color: blue;'>ℹ️ جدول categories موجود بالفعل</p>";
    }
    
    // عرض التصنيفات الموجودة
    echo "<h3>التصنيفات الموجودة:</h3>";
    $stmt = $conn->query("SELECT * FROM categories ORDER BY name");
    $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($categories)) {
        echo "<p>لا توجد تصنيفات</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th>ID</th><th>الاسم</th><th>الوصف</th><th>الأيقونة</th><th>اللون</th><th>الحالة</th>";
        echo "</tr>";
        
        foreach ($categories as $category) {
            echo "<tr>";
            echo "<td>" . $category['id'] . "</td>";
            echo "<td>" . htmlspecialchars($category['name']) . "</td>";
            echo "<td>" . htmlspecialchars($category['description'] ?? '') . "</td>";
            echo "<td><i class='" . htmlspecialchars($category['icon'] ?? '') . "'></i> " . htmlspecialchars($category['icon'] ?? '') . "</td>";
            echo "<td><span style='background: " . htmlspecialchars($category['color'] ?? '#000') . "; color: white; padding: 2px 8px; border-radius: 3px;'>" . htmlspecialchars($category['color'] ?? '') . "</span></td>";
            echo "<td>" . htmlspecialchars($category['status']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // التحقق من جدول course_reviews
    echo "<h3>التحقق من الجداول الأخرى:</h3>";
    
    $tables_to_check = [
        'course_reviews' => "CREATE TABLE course_reviews (
            id INT AUTO_INCREMENT PRIMARY KEY,
            course_id INT NOT NULL,
            student_id INT NOT NULL,
            rating DECIMAL(2,1) NOT NULL CHECK (rating >= 1 AND rating <= 5),
            review TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY unique_review (course_id, student_id),
            INDEX idx_course_id (course_id),
            INDEX idx_rating (rating),
            FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
            FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
        
        'sessions' => "CREATE TABLE sessions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            course_id INT NOT NULL,
            title VARCHAR(255) NOT NULL,
            description TEXT,
            session_date DATETIME,
            duration_minutes INT DEFAULT 60,
            session_type ENUM('live', 'recorded', 'assignment') DEFAULT 'live',
            meeting_link VARCHAR(500),
            recording_link VARCHAR(500),
            materials TEXT,
            status ENUM('scheduled', 'ongoing', 'completed', 'cancelled') DEFAULT 'scheduled',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_course_id (course_id),
            INDEX idx_session_date (session_date),
            INDEX idx_status (status),
            FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci"
    ];
    
    foreach ($tables_to_check as $table_name => $create_sql) {
        $stmt = $conn->query("SHOW TABLES LIKE '$table_name'");
        if ($stmt->rowCount() == 0) {
            echo "<p>إنشاء جدول $table_name...</p>";
            $conn->exec($create_sql);
            echo "<p style='color: green;'>✅ تم إنشاء جدول $table_name</p>";
        } else {
            echo "<p style='color: blue;'>ℹ️ جدول $table_name موجود بالفعل</p>";
        }
    }
    
    // تحديث الكورسات لتحتوي على تصنيفات
    echo "<h3>تحديث الكورسات:</h3>";
    $stmt = $conn->query("SELECT COUNT(*) FROM courses WHERE category_id IS NULL");
    $courses_without_category = $stmt->fetchColumn();
    
    if ($courses_without_category > 0) {
        // تعيين تصنيف "البرمجة" كتصنيف افتراضي
        $stmt = $conn->query("SELECT id FROM categories WHERE name = 'البرمجة' LIMIT 1");
        $programming_category = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($programming_category) {
            $conn->exec("UPDATE courses SET category_id = " . $programming_category['id'] . " WHERE category_id IS NULL");
            echo "<p style='color: green;'>✅ تم تحديث $courses_without_category كورس بتصنيف البرمجة</p>";
        }
    }
    
    // إضافة بعض الجلسات التجريبية
    $stmt = $conn->query("SELECT COUNT(*) FROM sessions");
    $session_count = $stmt->fetchColumn();
    
    if ($session_count == 0) {
        echo "<p>إضافة جلسات تجريبية...</p>";
        $stmt = $conn->query("SELECT id, title FROM courses LIMIT 3");
        $courses = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        foreach ($courses as $course) {
            for ($i = 1; $i <= 3; $i++) {
                $stmt = $conn->prepare("
                    INSERT INTO sessions (course_id, title, description, session_date, duration_minutes) 
                    VALUES (?, ?, ?, DATE_ADD(NOW(), INTERVAL ? DAY), 90)
                ");
                $stmt->execute([
                    $course['id'],
                    "الجلسة $i - " . $course['title'],
                    "وصف الجلسة $i للكورس " . $course['title'],
                    $i * 7 // كل أسبوع
                ]);
            }
        }
        echo "<p style='color: green;'>✅ تم إضافة جلسات تجريبية</p>";
    }
    
    echo "<h3 style='color: green;'>✅ تم إصلاح جميع الجداول بنجاح!</h3>";
    echo "<p><a href='student/browse-courses.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>اختبار صفحة تصفح الكورسات</a></p>";
    
} catch (PDOException $e) {
    echo "<h3 style='color: red;'>❌ خطأ: " . $e->getMessage() . "</h3>";
}
?>
