<?php
/**
 * إصلاح سريع لجدول join_requests
 * Quick fix for join_requests table
 */

require_once 'config/database.php';

echo "<!DOCTYPE html>";
echo "<html dir='rtl' lang='ar'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>إصلاح سريع - جدول join_requests</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "</head>";
echo "<body>";
echo "<div class='container mt-4'>";
echo "<h2>🔧 إصلاح سريع - جدول join_requests</h2>";

try {
    // فحص الجدول الحالي
    echo "<h4>🔍 فحص الجدول الحالي:</h4>";
    
    $stmt = $conn->query("SHOW TABLES LIKE 'join_requests'");
    if ($stmt->rowCount() > 0) {
        echo "<div class='alert alert-info'>الجدول موجود، فحص الأعمدة...</div>";
        
        // عرض الأعمدة الحالية
        $stmt = $conn->query("DESCRIBE join_requests");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<div class='table-responsive'>";
        echo "<table class='table table-sm table-bordered'>";
        echo "<thead><tr><th>العمود</th><th>النوع</th><th>Null</th><th>Key</th><th>Default</th></tr></thead>";
        echo "<tbody>";
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>" . $column['Field'] . "</td>";
            echo "<td>" . $column['Type'] . "</td>";
            echo "<td>" . $column['Null'] . "</td>";
            echo "<td>" . $column['Key'] . "</td>";
            echo "<td>" . ($column['Default'] ?? 'NULL') . "</td>";
            echo "</tr>";
        }
        echo "</tbody></table>";
        echo "</div>";
        
        // فحص الأعمدة المطلوبة
        $existing_columns = array_column($columns, 'Field');
        $required_columns = [
            'course_id' => 'INT NOT NULL DEFAULT 1',
            'student_name' => 'VARCHAR(255) NOT NULL DEFAULT ""',
            'student_email' => 'VARCHAR(255) NOT NULL DEFAULT ""',
            'student_phone' => 'VARCHAR(20) DEFAULT NULL',
            'message' => 'TEXT DEFAULT NULL',
            'processed_by' => 'INT DEFAULT NULL',
            'rejection_reason' => 'TEXT DEFAULT NULL'
        ];
        
        echo "<h4>➕ إضافة الأعمدة المفقودة:</h4>";
        
        foreach ($required_columns as $column_name => $column_definition) {
            if (!in_array($column_name, $existing_columns)) {
                try {
                    $conn->exec("ALTER TABLE join_requests ADD COLUMN $column_name $column_definition");
                    echo "<div class='alert alert-success'>✅ تم إضافة عمود: $column_name</div>";
                } catch (Exception $e) {
                    echo "<div class='alert alert-danger'>❌ فشل في إضافة عمود $column_name: " . $e->getMessage() . "</div>";
                }
            } else {
                echo "<div class='alert alert-info'>ℹ️ عمود موجود: $column_name</div>";
            }
        }
        
    } else {
        echo "<div class='alert alert-warning'>الجدول غير موجود، إنشاء جدول جديد...</div>";
        
        // إنشاء الجدول من الصفر
        $conn->exec("
            CREATE TABLE join_requests (
                id INT AUTO_INCREMENT PRIMARY KEY,
                course_id INT NOT NULL DEFAULT 1,
                student_name VARCHAR(255) NOT NULL DEFAULT '',
                student_email VARCHAR(255) NOT NULL DEFAULT '',
                student_phone VARCHAR(20) DEFAULT NULL,
                message TEXT DEFAULT NULL,
                status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
                request_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                processed_date TIMESTAMP NULL,
                processed_by INT DEFAULT NULL,
                rejection_reason TEXT DEFAULT NULL,
                
                INDEX idx_course_id (course_id),
                INDEX idx_status (status),
                INDEX idx_email (student_email)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        echo "<div class='alert alert-success'>✅ تم إنشاء جدول join_requests</div>";
    }
    
    // إضافة بيانات تجريبية
    echo "<h4>📝 إضافة بيانات تجريبية:</h4>";
    
    // التحقق من وجود بيانات
    $stmt = $conn->query("SELECT COUNT(*) FROM join_requests");
    $count = $stmt->fetchColumn();
    
    if ($count == 0) {
        $sample_requests = [
            [1, 'سارة أحمد محمد', '<EMAIL>', '0501234567', 'أرغب في الانضمام لكورس البرمجة لتطوير مهاراتي التقنية'],
            [1, 'محمد علي حسن', '<EMAIL>', '0507654321', 'مهتم بتعلم البرمجة وتطوير المواقع'],
            [1, 'فاطمة خالد', '<EMAIL>', '0509876543', 'أريد تعلم أساسيات البرمجة'],
            [2, 'أحمد سالم', '<EMAIL>', '0502468135', 'مهتم بتعلم التصميم الجرافيكي'],
            [2, 'نور الدين', '<EMAIL>', '0508642097', 'أريد تطوير مهاراتي في التصميم']
        ];
        
        $stmt = $conn->prepare("
            INSERT INTO join_requests (course_id, student_name, student_email, student_phone, message) 
            VALUES (?, ?, ?, ?, ?)
        ");
        
        foreach ($sample_requests as $request) {
            $stmt->execute($request);
        }
        
        echo "<div class='alert alert-success'>✅ تم إضافة " . count($sample_requests) . " طلب انضمام تجريبي</div>";
    } else {
        echo "<div class='alert alert-info'>ℹ️ يوجد $count طلب انضمام في الجدول</div>";
    }
    
    // عرض الجدول النهائي
    echo "<h4>📋 الجدول النهائي:</h4>";
    $stmt = $conn->query("DESCRIBE join_requests");
    $final_columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<div class='table-responsive'>";
    echo "<table class='table table-sm table-bordered'>";
    echo "<thead class='table-success'><tr><th>العمود</th><th>النوع</th><th>Null</th><th>Key</th><th>Default</th></tr></thead>";
    echo "<tbody>";
    foreach ($final_columns as $column) {
        echo "<tr>";
        echo "<td><strong>" . $column['Field'] . "</strong></td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . ($column['Default'] ?? 'NULL') . "</td>";
        echo "</tr>";
    }
    echo "</tbody></table>";
    echo "</div>";
    
    // عرض البيانات الموجودة
    echo "<h4>📊 البيانات الموجودة:</h4>";
    $stmt = $conn->query("SELECT * FROM join_requests ORDER BY request_date DESC LIMIT 10");
    $requests = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($requests)) {
        echo "<div class='table-responsive'>";
        echo "<table class='table table-striped'>";
        echo "<thead><tr><th>ID</th><th>Course ID</th><th>الاسم</th><th>البريد</th><th>الهاتف</th><th>الحالة</th><th>التاريخ</th></tr></thead>";
        echo "<tbody>";
        foreach ($requests as $request) {
            $status_class = $request['status'] === 'pending' ? 'warning' : ($request['status'] === 'approved' ? 'success' : 'danger');
            echo "<tr>";
            echo "<td>" . $request['id'] . "</td>";
            echo "<td>" . $request['course_id'] . "</td>";
            echo "<td>" . htmlspecialchars($request['student_name']) . "</td>";
            echo "<td>" . htmlspecialchars($request['student_email']) . "</td>";
            echo "<td>" . htmlspecialchars($request['student_phone'] ?? '') . "</td>";
            echo "<td><span class='badge bg-$status_class'>" . $request['status'] . "</span></td>";
            echo "<td>" . date('Y-m-d H:i', strtotime($request['request_date'])) . "</td>";
            echo "</tr>";
        }
        echo "</tbody></table>";
        echo "</div>";
    } else {
        echo "<div class='alert alert-info'>لا توجد طلبات انضمام</div>";
    }
    
    echo "<div class='alert alert-success mt-4'>";
    echo "<h5>✅ تم الإصلاح بنجاح!</h5>";
    echo "<p>جدول join_requests جاهز للاستخدام مع جميع الأعمدة المطلوبة.</p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>❌ خطأ: " . htmlspecialchars($e->getMessage()) . "</div>";
}

echo "<div class='mt-4'>";
echo "<a href='fix_course_management_database.php' class='btn btn-primary btn-lg me-2'>🔧 إصلاح شامل</a>";
echo "<a href='instructor/course-details.php?id=1' class='btn btn-success btn-lg me-2'>📋 تفاصيل كورس</a>";
echo "<a href='admin/manage_join_requests.php' class='btn btn-warning btn-lg me-2'>📝 طلبات الانضمام</a>";
echo "<a href='instructor/courses.php' class='btn btn-info btn-lg'>📚 الكورسات</a>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>
