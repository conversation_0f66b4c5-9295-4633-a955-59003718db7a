<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>غير متصل - منصة التعلم</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .offline-container {
            background: white;
            border-radius: 20px;
            padding: 60px 40px;
            text-align: center;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            max-width: 500px;
            width: 100%;
            position: relative;
            overflow: hidden;
        }

        .offline-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .offline-icon {
            font-size: 80px;
            color: #667eea;
            margin-bottom: 30px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        .offline-title {
            font-size: 32px;
            font-weight: bold;
            color: #2d3748;
            margin-bottom: 20px;
        }

        .offline-message {
            font-size: 18px;
            color: #718096;
            margin-bottom: 40px;
            line-height: 1.6;
        }

        .retry-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 50px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 20px;
            display: inline-flex;
            align-items: center;
            gap: 10px;
        }

        .retry-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }

        .retry-button:active {
            transform: translateY(0);
        }

        .offline-tips {
            background: #f7fafc;
            border-radius: 15px;
            padding: 25px;
            margin-top: 30px;
            text-align: right;
        }

        .offline-tips h3 {
            color: #2d3748;
            margin-bottom: 15px;
            font-size: 20px;
        }

        .offline-tips ul {
            list-style: none;
            padding: 0;
        }

        .offline-tips li {
            margin-bottom: 10px;
            padding-right: 25px;
            position: relative;
            color: #4a5568;
        }

        .offline-tips li::before {
            content: '✓';
            position: absolute;
            right: 0;
            color: #48bb78;
            font-weight: bold;
        }

        .connection-status {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
            margin-top: 20px;
        }

        .status-offline {
            background: #fed7d7;
            color: #c53030;
        }

        .status-online {
            background: #c6f6d5;
            color: #2f855a;
        }

        .loading-spinner {
            display: none;
            width: 20px;
            height: 20px;
            border: 2px solid #ffffff;
            border-top: 2px solid transparent;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .retry-button.loading .loading-spinner {
            display: block;
        }

        .retry-button.loading .retry-text {
            display: none;
        }

        @media (max-width: 600px) {
            .offline-container {
                padding: 40px 20px;
                margin: 10px;
            }

            .offline-icon {
                font-size: 60px;
            }

            .offline-title {
                font-size: 24px;
            }

            .offline-message {
                font-size: 16px;
            }

            .offline-tips {
                text-align: center;
            }
        }

        .wifi-icon {
            position: relative;
            display: inline-block;
        }

        .wifi-icon::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 60px;
            height: 4px;
            background: #e53e3e;
            transform: translate(-50%, -50%) rotate(45deg);
            border-radius: 2px;
        }
    </style>
</head>
<body>
    <div class="offline-container">
        <div class="wifi-icon offline-icon">
            📶
        </div>
        
        <h1 class="offline-title">غير متصل بالإنترنت</h1>
        
        <p class="offline-message">
            يبدو أنك غير متصل بالإنترنت حالياً. تحقق من اتصالك وحاول مرة أخرى.
        </p>

        <button class="retry-button" onclick="retryConnection()">
            <div class="loading-spinner"></div>
            <span class="retry-text">
                🔄 إعادة المحاولة
            </span>
        </button>

        <div class="connection-status status-offline" id="connectionStatus">
            🔴 غير متصل
        </div>

        <div class="offline-tips">
            <h3>نصائح للاتصال:</h3>
            <ul>
                <li>تحقق من اتصال الواي فاي أو البيانات</li>
                <li>تأكد من أن جهاز التوجيه يعمل بشكل صحيح</li>
                <li>جرب إعادة تشغيل المتصفح</li>
                <li>تحقق من إعدادات الشبكة</li>
                <li>اتصل بمزود خدمة الإنترنت إذا استمرت المشكلة</li>
            </ul>
        </div>
    </div>

    <script>
        // مراقبة حالة الاتصال
        function updateConnectionStatus() {
            const statusElement = document.getElementById('connectionStatus');
            
            if (navigator.onLine) {
                statusElement.className = 'connection-status status-online';
                statusElement.innerHTML = '🟢 متصل';
                
                // إعادة توجيه تلقائي عند عودة الاتصال
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            } else {
                statusElement.className = 'connection-status status-offline';
                statusElement.innerHTML = '🔴 غير متصل';
            }
        }

        // مراقبة تغيير حالة الاتصال
        window.addEventListener('online', updateConnectionStatus);
        window.addEventListener('offline', updateConnectionStatus);

        // تحديث الحالة عند تحميل الصفحة
        updateConnectionStatus();

        // وظيفة إعادة المحاولة
        function retryConnection() {
            const button = document.querySelector('.retry-button');
            button.classList.add('loading');
            
            // محاولة إعادة تحميل الصفحة
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        }

        // محاولة إعادة الاتصال كل 30 ثانية
        setInterval(() => {
            if (navigator.onLine) {
                // جرب تحميل صورة صغيرة للتأكد من الاتصال
                const img = new Image();
                img.onload = () => {
                    window.location.reload();
                };
                img.onerror = () => {
                    console.log('لا يزال غير متصل');
                };
                img.src = '/assets/images/icons/icon-72x72.png?' + Date.now();
            }
        }, 30000);

        // تحسين تجربة المستخدم
        document.addEventListener('DOMContentLoaded', function() {
            // إضافة تأثيرات بصرية
            const container = document.querySelector('.offline-container');
            container.style.opacity = '0';
            container.style.transform = 'translateY(20px)';
            
            setTimeout(() => {
                container.style.transition = 'all 0.5s ease';
                container.style.opacity = '1';
                container.style.transform = 'translateY(0)';
            }, 100);

            // إضافة اختصارات لوحة المفاتيح
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' || e.key === ' ') {
                    retryConnection();
                }
                if (e.key === 'F5' || (e.ctrlKey && e.key === 'r')) {
                    e.preventDefault();
                    retryConnection();
                }
            });
        });

        // تسجيل Service Worker إذا لم يكن مسجلاً
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.register('/sw.js')
                .then(registration => {
                    console.log('Service Worker registered successfully');
                })
                .catch(error => {
                    console.log('Service Worker registration failed');
                });
        }

        // إضافة معلومات إضافية للمطورين
        console.log('صفحة Offline محملة');
        console.log('حالة الاتصال:', navigator.onLine ? 'متصل' : 'غير متصل');
        console.log('نوع الاتصال:', navigator.connection ? navigator.connection.effectiveType : 'غير معروف');
    </script>
</body>
</html>
