<?php
/**
 * إنشاء نظام دعوة الطلاب للكورسات
 * Create student invitation system for courses
 */

require_once 'config/database.php';

echo "<!DOCTYPE html>";
echo "<html dir='rtl' lang='ar'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>إنشاء نظام دعوة الطلاب</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "</head>";
echo "<body>";
echo "<div class='container mt-4'>";
echo "<h2>🔗 إنشاء نظام دعوة الطلاب للكورسات</h2>";

try {
    // 1. إنشاء جدول روابط الدعوة
    echo "<h4>📋 إنشاء جدول روابط الدعوة</h4>";
    
    $stmt = $conn->query("SHOW TABLES LIKE 'course_invitations'");
    if ($stmt->rowCount() == 0) {
        $conn->exec("
            CREATE TABLE course_invitations (
                id INT AUTO_INCREMENT PRIMARY KEY,
                course_id INT NOT NULL,
                instructor_id INT NOT NULL,
                invitation_code VARCHAR(32) UNIQUE NOT NULL,
                invitation_link VARCHAR(255) NOT NULL,
                max_uses INT DEFAULT NULL,
                current_uses INT DEFAULT 0,
                expires_at TIMESTAMP NULL,
                status ENUM('active', 'inactive', 'expired') DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                
                INDEX idx_course_id (course_id),
                INDEX idx_instructor_id (instructor_id),
                INDEX idx_invitation_code (invitation_code),
                INDEX idx_status (status),
                
                FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
                FOREIGN KEY (instructor_id) REFERENCES users(id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        echo "<div class='alert alert-success'>✅ تم إنشاء جدول روابط الدعوة</div>";
    } else {
        echo "<div class='alert alert-info'>ℹ️ جدول روابط الدعوة موجود</div>";
    }

    // 2. إنشاء جدول طلبات الانضمام عبر الدعوة
    echo "<h4>📝 إنشاء جدول طلبات الانضمام</h4>";
    
    $stmt = $conn->query("SHOW TABLES LIKE 'course_join_requests'");
    if ($stmt->rowCount() == 0) {
        $conn->exec("
            CREATE TABLE course_join_requests (
                id INT AUTO_INCREMENT PRIMARY KEY,
                course_id INT NOT NULL,
                student_id INT NOT NULL,
                invitation_id INT DEFAULT NULL,
                student_name VARCHAR(255) NOT NULL,
                student_email VARCHAR(255) NOT NULL,
                student_phone VARCHAR(20) DEFAULT NULL,
                message TEXT DEFAULT NULL,
                status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
                requested_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                processed_at TIMESTAMP NULL,
                processed_by INT DEFAULT NULL,
                notes TEXT DEFAULT NULL,
                
                INDEX idx_course_id (course_id),
                INDEX idx_student_id (student_id),
                INDEX idx_invitation_id (invitation_id),
                INDEX idx_status (status),
                INDEX idx_requested_at (requested_at),
                
                FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
                FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
                FOREIGN KEY (invitation_id) REFERENCES course_invitations(id) ON DELETE SET NULL,
                FOREIGN KEY (processed_by) REFERENCES users(id) ON DELETE SET NULL,
                
                UNIQUE KEY unique_course_student (course_id, student_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        echo "<div class='alert alert-success'>✅ تم إنشاء جدول طلبات الانضمام</div>";
    } else {
        echo "<div class='alert alert-info'>ℹ️ جدول طلبات الانضمام موجود</div>";
    }

    // 3. إنشاء روابط دعوة للكورسات الموجودة
    echo "<h4>🔗 إنشاء روابط دعوة للكورسات الموجودة</h4>";
    
    $stmt = $conn->query("
        SELECT c.id, c.title, c.instructor_id, u.name as instructor_name
        FROM courses c
        JOIN users u ON c.instructor_id = u.id
        WHERE c.status = 'active'
    ");
    $courses = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $created_invitations = 0;
    
    foreach ($courses as $course) {
        // التحقق من وجود رابط دعوة للكورس
        $stmt = $conn->prepare("SELECT id FROM course_invitations WHERE course_id = ? AND status = 'active'");
        $stmt->execute([$course['id']]);
        
        if (!$stmt->fetch()) {
            // إنشاء رابط دعوة جديد
            $invitation_code = bin2hex(random_bytes(16)); // كود فريد 32 حرف
            $invitation_link = "http://localhost/Zoom/join-course.php?code=" . $invitation_code;
            
            $stmt = $conn->prepare("
                INSERT INTO course_invitations (course_id, instructor_id, invitation_code, invitation_link, status, created_at)
                VALUES (?, ?, ?, ?, 'active', NOW())
            ");
            $stmt->execute([$course['id'], $course['instructor_id'], $invitation_code, $invitation_link]);
            
            $created_invitations++;
            echo "<div class='alert alert-success'>✅ تم إنشاء رابط دعوة للكورس: " . htmlspecialchars($course['title']) . "</div>";
        }
    }
    
    if ($created_invitations == 0) {
        echo "<div class='alert alert-info'>ℹ️ جميع الكورسات لديها روابط دعوة</div>";
    }

    // 4. عرض روابط الدعوة الموجودة
    echo "<h4>📊 روابط الدعوة الحالية</h4>";
    
    $stmt = $conn->query("
        SELECT 
            ci.*,
            c.title as course_title,
            u.name as instructor_name,
            (SELECT COUNT(*) FROM course_join_requests WHERE invitation_id = ci.id) as total_requests
        FROM course_invitations ci
        JOIN courses c ON ci.course_id = c.id
        JOIN users u ON ci.instructor_id = u.id
        WHERE ci.status = 'active'
        ORDER BY ci.created_at DESC
    ");
    $invitations = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($invitations)) {
        echo "<div class='table-responsive'>";
        echo "<table class='table table-striped'>";
        echo "<thead class='table-dark'>";
        echo "<tr>";
        echo "<th>الكورس</th>";
        echo "<th>المدرب</th>";
        echo "<th>رابط الدعوة</th>";
        echo "<th>عدد الطلبات</th>";
        echo "<th>تاريخ الإنشاء</th>";
        echo "<th>الإجراءات</th>";
        echo "</tr>";
        echo "</thead>";
        echo "<tbody>";
        
        foreach ($invitations as $invitation) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($invitation['course_title']) . "</td>";
            echo "<td>" . htmlspecialchars($invitation['instructor_name']) . "</td>";
            echo "<td>";
            echo "<div class='input-group'>";
            echo "<input type='text' class='form-control' value='{$invitation['invitation_link']}' readonly onclick='this.select()'>";
            echo "<button class='btn btn-outline-secondary' onclick='copyToClipboard(\"{$invitation['invitation_link']}\")'>نسخ</button>";
            echo "</div>";
            echo "</td>";
            echo "<td><span class='badge bg-info'>{$invitation['total_requests']}</span></td>";
            echo "<td>" . date('Y-m-d', strtotime($invitation['created_at'])) . "</td>";
            echo "<td>";
            echo "<a href='instructor/course-details.php?id={$invitation['course_id']}' class='btn btn-sm btn-primary'>عرض الكورس</a>";
            echo "</td>";
            echo "</tr>";
        }
        
        echo "</tbody>";
        echo "</table>";
        echo "</div>";
    }

    // 5. إنشاء بعض طلبات الانضمام التجريبية
    echo "<h4>👥 إنشاء طلبات انضمام تجريبية</h4>";
    
    // البحث عن طلاب
    $stmt = $conn->query("SELECT id, name, email FROM users WHERE role = 'student' LIMIT 3");
    $students = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($students) && !empty($invitations)) {
        $sample_requests = 0;
        
        foreach ($students as $student) {
            foreach (array_slice($invitations, 0, 2) as $invitation) { // أول كورسين فقط
                // التحقق من عدم وجود طلب سابق
                $stmt = $conn->prepare("SELECT id FROM course_join_requests WHERE course_id = ? AND student_id = ?");
                $stmt->execute([$invitation['course_id'], $student['id']]);
                
                if (!$stmt->fetch()) {
                    $stmt = $conn->prepare("
                        INSERT INTO course_join_requests 
                        (course_id, student_id, invitation_id, student_name, student_email, message, status, requested_at)
                        VALUES (?, ?, ?, ?, ?, ?, 'pending', NOW())
                    ");
                    $stmt->execute([
                        $invitation['course_id'],
                        $student['id'],
                        $invitation['id'],
                        $student['name'],
                        $student['email'],
                        'أرغب في الانضمام لهذا الكورس المفيد'
                    ]);
                    
                    $sample_requests++;
                }
            }
        }
        
        if ($sample_requests > 0) {
            echo "<div class='alert alert-success'>✅ تم إنشاء $sample_requests طلب انضمام تجريبي</div>";
        } else {
            echo "<div class='alert alert-info'>ℹ️ طلبات الانضمام موجودة مسبقاً</div>";
        }
    }

    // 6. إحصائيات النظام
    echo "<h4>📈 إحصائيات نظام الدعوة</h4>";
    
    $stmt = $conn->query("SELECT COUNT(*) FROM course_invitations WHERE status = 'active'");
    $active_invitations = $stmt->fetchColumn();
    
    $stmt = $conn->query("SELECT COUNT(*) FROM course_join_requests WHERE status = 'pending'");
    $pending_requests = $stmt->fetchColumn();
    
    $stmt = $conn->query("SELECT COUNT(*) FROM course_join_requests WHERE status = 'approved'");
    $approved_requests = $stmt->fetchColumn();
    
    echo "<div class='row'>";
    
    echo "<div class='col-md-3'>";
    echo "<div class='card bg-primary text-white'>";
    echo "<div class='card-body text-center'>";
    echo "<h3>$active_invitations</h3>";
    echo "<p class='mb-0'>روابط دعوة نشطة</p>";
    echo "</div></div></div>";
    
    echo "<div class='col-md-3'>";
    echo "<div class='card bg-warning text-white'>";
    echo "<div class='card-body text-center'>";
    echo "<h3>$pending_requests</h3>";
    echo "<p class='mb-0'>طلبات معلقة</p>";
    echo "</div></div></div>";
    
    echo "<div class='col-md-3'>";
    echo "<div class='card bg-success text-white'>";
    echo "<div class='card-body text-center'>";
    echo "<h3>$approved_requests</h3>";
    echo "<p class='mb-0'>طلبات مقبولة</p>";
    echo "</div></div></div>";
    
    echo "<div class='col-md-3'>";
    echo "<div class='card bg-info text-white'>";
    echo "<div class='card-body text-center'>";
    echo "<h3>" . count($courses) . "</h3>";
    echo "<p class='mb-0'>كورسات نشطة</p>";
    echo "</div></div></div>";
    
    echo "</div>";

    echo "<div class='alert alert-success mt-4'>";
    echo "<h5>🎉 تم إنشاء نظام دعوة الطلاب بنجاح!</h5>";
    echo "<ul class='mb-0'>";
    echo "<li>✅ تم إنشاء جداول النظام</li>";
    echo "<li>✅ تم إنشاء روابط دعوة للكورسات</li>";
    echo "<li>✅ تم إنشاء طلبات انضمام تجريبية</li>";
    echo "<li>✅ النظام جاهز للاستخدام</li>";
    echo "</ul>";
    echo "</div>";

} catch (Exception $e) {
    echo "<div class='alert alert-danger'>❌ خطأ: " . htmlspecialchars($e->getMessage()) . "</div>";
}

echo "<div class='text-center mt-4'>";
echo "<a href='instructor/course-details.php?id=18' class='btn btn-primary btn-lg me-2'>📚 عرض تفاصيل الكورس</a>";
echo "<a href='join-course.php' class='btn btn-success btn-lg me-2'>🔗 صفحة الانضمام</a>";
echo "<a href='instructor/dashboard.php' class='btn btn-info btn-lg'>🏠 لوحة المدرب</a>";
echo "</div>";

echo "<div class='mt-4'>";
echo "<div class='alert alert-info'>";
echo "<h6>💡 كيفية استخدام النظام:</h6>";
echo "<ol>";
echo "<li>المدرب يحصل على رابط دعوة فريد لكل كورس</li>";
echo "<li>المدرب يرسل الرابط للطلاب المهتمين</li>";
echo "<li>الطالب يضغط على الرابط وينشئ حساب أو يسجل دخول</li>";
echo "<li>الطالب يرسل طلب انضمام للكورس</li>";
echo "<li>المدرب يراجع ويقبل أو يرفض الطلبات</li>";
echo "</ol>";
echo "</div>";
echo "</div>";

echo "</div>";

echo "<script>";
echo "function copyToClipboard(text) {";
echo "    navigator.clipboard.writeText(text).then(function() {";
echo "        alert('تم نسخ الرابط!');";
echo "    });";
echo "}";
echo "</script>";

echo "</body>";
echo "</html>";
?>
