<?php
require_once 'config/database.php';

echo "<h2>اختبار سريع لطلب الانضمام</h2>";

try {
    // جلب طالب وكورس للاختبار
    $stmt = $conn->query("SELECT id, username FROM users WHERE role = 'student' LIMIT 1");
    $student = $stmt->fetch(PDO::FETCH_ASSOC);
    
    $stmt = $conn->query("SELECT id, title FROM courses WHERE status = 'active' LIMIT 1");
    $course = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$student) {
        echo "<p style='color: red;'>❌ لا يوجد طلاب في النظام</p>";
        echo "<p><a href='create_sample_students.php'>إنشاء طلاب تجريبيين</a></p>";
        exit;
    }
    
    if (!$course) {
        echo "<p style='color: red;'>❌ لا توجد كورسات في النظام</p>";
        exit;
    }
    
    echo "<p><strong>الطالب:</strong> " . htmlspecialchars($student['username']) . " (ID: " . $student['id'] . ")</p>";
    echo "<p><strong>الكورس:</strong> " . htmlspecialchars($course['title']) . " (ID: " . $course['id'] . ")</p>";
    
    // التحقق من وجود جدول join_requests
    $stmt = $conn->query("SHOW TABLES LIKE 'join_requests'");
    if ($stmt->rowCount() == 0) {
        echo "<p>إنشاء جدول join_requests...</p>";
        $conn->exec("CREATE TABLE join_requests (
            id INT AUTO_INCREMENT PRIMARY KEY,
            student_id INT NOT NULL,
            course_id INT NOT NULL,
            message TEXT,
            status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            processed_at TIMESTAMP NULL,
            processed_by INT NULL,
            INDEX idx_student_id (student_id),
            INDEX idx_course_id (course_id),
            INDEX idx_status (status)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
        echo "<p style='color: green;'>✅ تم إنشاء جدول join_requests</p>";
    }
    
    // التحقق من وجود طلب سابق
    $stmt = $conn->prepare("SELECT id FROM join_requests WHERE student_id = ? AND course_id = ?");
    $stmt->execute([$student['id'], $course['id']]);
    
    if ($stmt->fetch()) {
        echo "<p style='color: orange;'>⚠️ يوجد طلب انضمام سابق لهذا الطالب في هذا الكورس</p>";
    } else {
        // إنشاء طلب انضمام جديد
        $message = "أرغب في الانضمام لهذا الكورس لتطوير مهاراتي في البرمجة. لدي اهتمام كبير بالموضوع وأريد تعلم المزيد.";
        
        $stmt = $conn->prepare("
            INSERT INTO join_requests (student_id, course_id, message) 
            VALUES (?, ?, ?)
        ");
        $stmt->execute([$student['id'], $course['id'], $message]);
        
        $request_id = $conn->lastInsertId();
        
        echo "<p style='color: green;'>✅ تم إنشاء طلب انضمام جديد برقم: $request_id</p>";
        echo "<p><strong>الرسالة:</strong> " . htmlspecialchars($message) . "</p>";
    }
    
    // عرض جميع طلبات الانضمام
    echo "<h3>جميع طلبات الانضمام:</h3>";
    $stmt = $conn->query("
        SELECT jr.*, 
               u.username as student_name,
               c.title as course_title
        FROM join_requests jr
        INNER JOIN users u ON jr.student_id = u.id
        INNER JOIN courses c ON jr.course_id = c.id
        ORDER BY jr.created_at DESC
    ");
    $requests = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($requests)) {
        echo "<p>لا توجد طلبات انضمام</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th>ID</th><th>الطالب</th><th>الكورس</th><th>الحالة</th><th>التاريخ</th><th>الرسالة</th>";
        echo "</tr>";
        
        foreach ($requests as $request) {
            $status_color = '';
            switch ($request['status']) {
                case 'pending': $status_color = 'orange'; break;
                case 'approved': $status_color = 'green'; break;
                case 'rejected': $status_color = 'red'; break;
            }
            
            echo "<tr>";
            echo "<td>" . $request['id'] . "</td>";
            echo "<td>" . htmlspecialchars($request['student_name']) . "</td>";
            echo "<td>" . htmlspecialchars($request['course_title']) . "</td>";
            echo "<td style='color: $status_color; font-weight: bold;'>" . $request['status'] . "</td>";
            echo "<td>" . date('Y-m-d H:i', strtotime($request['created_at'])) . "</td>";
            echo "<td>" . htmlspecialchars(substr($request['message'] ?? '', 0, 50)) . "...</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // إحصائيات
    $stmt = $conn->query("
        SELECT 
            COUNT(*) as total,
            SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
            SUM(CASE WHEN status = 'approved' THEN 1 ELSE 0 END) as approved,
            SUM(CASE WHEN status = 'rejected' THEN 1 ELSE 0 END) as rejected
        FROM join_requests
    ");
    $stats = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "<h3>📊 الإحصائيات:</h3>";
    echo "<ul>";
    echo "<li><strong>إجمالي الطلبات:</strong> " . $stats['total'] . "</li>";
    echo "<li><strong>طلبات معلقة:</strong> " . $stats['pending'] . "</li>";
    echo "<li><strong>طلبات مقبولة:</strong> " . $stats['approved'] . "</li>";
    echo "<li><strong>طلبات مرفوضة:</strong> " . $stats['rejected'] . "</li>";
    echo "</ul>";
    
    echo "<h3>🔗 الخطوات التالية:</h3>";
    echo "<ol>";
    echo "<li><a href='instructor/course-join-requests.php?course_id=" . $course['id'] . "' target='_blank'>مراجعة طلبات الكورس</a></li>";
    echo "<li><a href='test-join-course.php?course_id=" . $course['id'] . "' target='_blank'>اختبار إرسال طلب جديد</a></li>";
    echo "<li><a href='debug-join-requests.php' target='_blank'>عرض جميع الطلبات</a></li>";
    echo "</ol>";
    
} catch (PDOException $e) {
    echo "<h3 style='color: red;'>❌ خطأ: " . $e->getMessage() . "</h3>";
}
?>
