<?php
require_once 'config/database.php';
require_once 'includes/session_config.php';

echo "<h2>فحص تسجيلات الطالب</h2>";

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    echo "<p style='color: red;'>❌ لم يتم تسجيل الدخول</p>";
    echo "<p><a href='login.php'>تسجيل الدخول</a></p>";
    exit;
}

$student_id = $_SESSION['user_id'];
echo "<p><strong>معرف الطالب:</strong> $student_id</p>";

try {
    // التحقق من بيانات الطالب
    $stmt = $conn->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$student_id]);
    $student = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$student) {
        echo "<p style='color: red;'>❌ الطالب غير موجود</p>";
        exit;
    }
    
    echo "<h3>بيانات الطالب:</h3>";
    echo "<ul>";
    echo "<li><strong>الاسم:</strong> " . htmlspecialchars($student['username']) . "</li>";
    echo "<li><strong>البريد الإلكتروني:</strong> " . htmlspecialchars($student['email']) . "</li>";
    echo "<li><strong>الدور:</strong> " . htmlspecialchars($student['role']) . "</li>";
    echo "</ul>";
    
    // فحص جدول course_enrollments
    echo "<h3>التسجيلات في جدول course_enrollments:</h3>";
    $stmt = $conn->prepare("
        SELECT ce.*, c.title as course_title, u.username as instructor_name
        FROM course_enrollments ce
        INNER JOIN courses c ON ce.course_id = c.id
        INNER JOIN users u ON c.instructor_id = u.id
        WHERE ce.student_id = ?
        ORDER BY ce.enrolled_at DESC
    ");
    $stmt->execute([$student_id]);
    $enrollments = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($enrollments)) {
        echo "<p style='color: orange;'>⚠️ لا توجد تسجيلات في جدول course_enrollments</p>";
        
        // إنشاء تسجيل تجريبي
        echo "<h4>إنشاء تسجيل تجريبي:</h4>";
        
        // جلب أول كورس متاح
        $stmt = $conn->query("SELECT id, title FROM courses WHERE status = 'active' LIMIT 1");
        $course = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($course) {
            // التحقق من عدم وجود تسجيل سابق
            $stmt = $conn->prepare("SELECT id FROM course_enrollments WHERE student_id = ? AND course_id = ?");
            $stmt->execute([$student_id, $course['id']]);
            
            if (!$stmt->fetch()) {
                $stmt = $conn->prepare("
                    INSERT INTO course_enrollments (course_id, student_id, status, progress) 
                    VALUES (?, ?, 'active', 0.00)
                ");
                $stmt->execute([$course['id'], $student_id]);
                
                echo "<p style='color: green;'>✅ تم إنشاء تسجيل تجريبي في الكورس: " . htmlspecialchars($course['title']) . "</p>";
                
                // إعادة جلب التسجيلات
                $stmt = $conn->prepare("
                    SELECT ce.*, c.title as course_title, u.username as instructor_name
                    FROM course_enrollments ce
                    INNER JOIN courses c ON ce.course_id = c.id
                    INNER JOIN users u ON c.instructor_id = u.id
                    WHERE ce.student_id = ?
                    ORDER BY ce.enrolled_at DESC
                ");
                $stmt->execute([$student_id]);
                $enrollments = $stmt->fetchAll(PDO::FETCH_ASSOC);
            } else {
                echo "<p style='color: blue;'>ℹ️ يوجد تسجيل سابق في هذا الكورس</p>";
            }
        } else {
            echo "<p style='color: red;'>❌ لا توجد كورسات متاحة</p>";
        }
    }
    
    if (!empty($enrollments)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th>ID</th><th>الكورس</th><th>المدرب</th><th>الحالة</th><th>التقدم</th><th>تاريخ التسجيل</th>";
        echo "</tr>";
        
        foreach ($enrollments as $enrollment) {
            echo "<tr>";
            echo "<td>" . $enrollment['id'] . "</td>";
            echo "<td>" . htmlspecialchars($enrollment['course_title']) . "</td>";
            echo "<td>" . htmlspecialchars($enrollment['instructor_name']) . "</td>";
            echo "<td>" . htmlspecialchars($enrollment['status']) . "</td>";
            echo "<td>" . $enrollment['progress'] . "%</td>";
            echo "<td>" . date('Y-m-d H:i', strtotime($enrollment['enrolled_at'])) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // فحص طلبات الانضمام
    echo "<h3>طلبات الانضمام:</h3>";
    $stmt = $conn->prepare("
        SELECT jr.*, c.title as course_title, u.username as instructor_name
        FROM join_requests jr
        INNER JOIN courses c ON jr.course_id = c.id
        INNER JOIN users u ON c.instructor_id = u.id
        WHERE jr.student_id = ?
        ORDER BY jr.created_at DESC
    ");
    $stmt->execute([$student_id]);
    $join_requests = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($join_requests)) {
        echo "<p>لا توجد طلبات انضمام</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th>ID</th><th>الكورس</th><th>المدرب</th><th>الحالة</th><th>تاريخ الطلب</th>";
        echo "</tr>";
        
        foreach ($join_requests as $request) {
            echo "<tr>";
            echo "<td>" . $request['id'] . "</td>";
            echo "<td>" . htmlspecialchars($request['course_title']) . "</td>";
            echo "<td>" . htmlspecialchars($request['instructor_name']) . "</td>";
            echo "<td>" . htmlspecialchars($request['status']) . "</td>";
            echo "<td>" . date('Y-m-d H:i', strtotime($request['created_at'])) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // عرض جميع الكورسات المتاحة
    echo "<h3>الكورسات المتاحة:</h3>";
    $stmt = $conn->query("
        SELECT c.id, c.title, u.username as instructor_name, c.status
        FROM courses c
        INNER JOIN users u ON c.instructor_id = u.id
        ORDER BY c.created_at DESC
        LIMIT 5
    ");
    $available_courses = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($available_courses)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th>ID</th><th>العنوان</th><th>المدرب</th><th>الحالة</th><th>إجراءات</th>";
        echo "</tr>";
        
        foreach ($available_courses as $course) {
            echo "<tr>";
            echo "<td>" . $course['id'] . "</td>";
            echo "<td>" . htmlspecialchars($course['title']) . "</td>";
            echo "<td>" . htmlspecialchars($course['instructor_name']) . "</td>";
            echo "<td>" . htmlspecialchars($course['status']) . "</td>";
            echo "<td>";
            
            // التحقق من التسجيل
            $stmt = $conn->prepare("SELECT id FROM course_enrollments WHERE student_id = ? AND course_id = ?");
            $stmt->execute([$student_id, $course['id']]);
            $is_enrolled = $stmt->fetch();
            
            if ($is_enrolled) {
                echo "<span style='color: green;'>✅ مسجل</span>";
            } else {
                echo "<a href='test-enroll.php?course_id=" . $course['id'] . "' style='background: #007bff; color: white; padding: 5px 10px; text-decoration: none; border-radius: 3px;'>تسجيل</a>";
            }
            echo "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<h3>🔗 روابط مفيدة:</h3>";
    echo "<ul>";
    echo "<li><a href='student/courses.php'>صفحة كورساتي</a></li>";
    echo "<li><a href='student/browse-courses.php'>تصفح الكورسات</a></li>";
    echo "<li><a href='test-join-course.php'>إرسال طلب انضمام</a></li>";
    echo "</ul>";
    
} catch (PDOException $e) {
    echo "<h3 style='color: red;'>❌ خطأ: " . $e->getMessage() . "</h3>";
}
?>
