<?php
/**
 * إنشاء جدول التصنيفات وإضافة بيانات تجريبية
 * Create Categories Table and Add Sample Data
 */

require_once 'config/database.php';

echo "<!DOCTYPE html>";
echo "<html dir='rtl' lang='ar'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>إنشاء جدول التصنيفات</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "</head>";
echo "<body>";
echo "<div class='container mt-4'>";
echo "<h2>📚 إنشاء جدول التصنيفات</h2>";

try {
    // إنشاء جدول categories
    echo "<h4>🔧 إنشاء جدول categories...</h4>";
    $conn->exec("
        CREATE TABLE IF NOT EXISTS categories (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            description TEXT,
            icon VARCHAR(100) DEFAULT NULL,
            color VARCHAR(7) DEFAULT '#007bff',
            status ENUM('active', 'inactive') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            
            INDEX idx_name (name),
            INDEX idx_status (status)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "<div class='alert alert-success'>✅ تم إنشاء جدول categories بنجاح</div>";

    // إضافة تصنيفات تجريبية
    echo "<h4>📋 إضافة تصنيفات تجريبية...</h4>";
    
    $categories = [
        ['البرمجة وتطوير المواقع', 'تعلم لغات البرمجة وتطوير تطبيقات الويب', 'fas fa-code', '#007bff'],
        ['التصميم الجرافيكي', 'تصميم الشعارات والمواد الإعلانية', 'fas fa-paint-brush', '#28a745'],
        ['التسويق الرقمي', 'استراتيجيات التسويق عبر الإنترنت', 'fas fa-bullhorn', '#dc3545'],
        ['إدارة الأعمال', 'مهارات إدارة المشاريع والفرق', 'fas fa-briefcase', '#ffc107'],
        ['اللغات', 'تعلم اللغات المختلفة', 'fas fa-language', '#17a2b8'],
        ['العلوم والتكنولوجيا', 'المواضيع العلمية والتقنية', 'fas fa-flask', '#6f42c1'],
        ['الفنون والإبداع', 'الرسم والموسيقى والفنون', 'fas fa-palette', '#e83e8c'],
        ['الصحة واللياقة', 'التغذية والرياضة والصحة العامة', 'fas fa-heartbeat', '#fd7e14'],
        ['التطوير الشخصي', 'مهارات التطوير الذاتي والقيادة', 'fas fa-user-graduate', '#20c997'],
        ['المالية والمحاسبة', 'إدارة الأموال والمحاسبة', 'fas fa-calculator', '#6c757d']
    ];

    $insertCategory = $conn->prepare("
        INSERT INTO categories (name, description, icon, color) 
        VALUES (?, ?, ?, ?)
    ");

    foreach ($categories as $category) {
        $insertCategory->execute($category);
    }

    echo "<div class='alert alert-success'>✅ تم إضافة " . count($categories) . " تصنيف</div>";

    // إضافة عمود category_id إلى جدول courses إذا لم يكن موجوداً
    echo "<h4>🔗 تحديث جدول الكورسات...</h4>";
    
    try {
        $conn->exec("ALTER TABLE courses ADD COLUMN category_id INT DEFAULT NULL");
        $conn->exec("ALTER TABLE courses ADD FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL");
        echo "<div class='alert alert-success'>✅ تم إضافة عمود category_id إلى جدول courses</div>";
    } catch (Exception $e) {
        echo "<div class='alert alert-info'>ℹ️ عمود category_id موجود مسبقاً في جدول courses</div>";
    }

    // تحديث الكورسات الموجودة بتصنيفات عشوائية
    echo "<h4>🎯 تحديث الكورسات الموجودة...</h4>";
    
    $stmt = $conn->query("SELECT id FROM courses WHERE category_id IS NULL");
    $courses = $stmt->fetchAll();
    
    $stmt = $conn->query("SELECT id FROM categories");
    $categoryIds = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    if (!empty($courses) && !empty($categoryIds)) {
        $updateCourse = $conn->prepare("UPDATE courses SET category_id = ? WHERE id = ?");
        
        foreach ($courses as $course) {
            $randomCategoryId = $categoryIds[array_rand($categoryIds)];
            $updateCourse->execute([$randomCategoryId, $course['id']]);
        }
        
        echo "<div class='alert alert-success'>✅ تم تحديث " . count($courses) . " كورس بتصنيفات</div>";
    }

    // عرض التصنيفات المضافة
    echo "<h4>📊 التصنيفات المضافة:</h4>";
    
    $stmt = $conn->query("SELECT * FROM categories ORDER BY name");
    $allCategories = $stmt->fetchAll();
    
    echo "<div class='row'>";
    foreach ($allCategories as $cat) {
        echo "<div class='col-md-4 mb-3'>";
        echo "<div class='card' style='border-left: 4px solid {$cat['color']}'>";
        echo "<div class='card-body'>";
        echo "<h6 class='card-title'>";
        echo "<i class='{$cat['icon']} me-2' style='color: {$cat['color']}'></i>";
        echo htmlspecialchars($cat['name']);
        echo "</h6>";
        echo "<p class='card-text'><small class='text-muted'>" . htmlspecialchars($cat['description']) . "</small></p>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
    }
    echo "</div>";

    // إحصائيات
    echo "<h4>📈 الإحصائيات:</h4>";
    
    $stmt = $conn->query("SELECT COUNT(*) FROM categories");
    $totalCategories = $stmt->fetchColumn();
    
    $stmt = $conn->query("SELECT COUNT(*) FROM courses WHERE category_id IS NOT NULL");
    $coursesWithCategories = $stmt->fetchColumn();
    
    $stmt = $conn->query("SELECT COUNT(*) FROM courses");
    $totalCourses = $stmt->fetchColumn();

    echo "<div class='row'>";
    echo "<div class='col-md-4'>";
    echo "<div class='card bg-primary text-white'>";
    echo "<div class='card-body text-center'>";
    echo "<h3>{$totalCategories}</h3>";
    echo "<p>إجمالي التصنيفات</p>";
    echo "</div>";
    echo "</div>";
    echo "</div>";
    
    echo "<div class='col-md-4'>";
    echo "<div class='card bg-success text-white'>";
    echo "<div class='card-body text-center'>";
    echo "<h3>{$coursesWithCategories}</h3>";
    echo "<p>كورسات مع تصنيفات</p>";
    echo "</div>";
    echo "</div>";
    echo "</div>";
    
    echo "<div class='col-md-4'>";
    echo "<div class='card bg-info text-white'>";
    echo "<div class='card-body text-center'>";
    echo "<h3>{$totalCourses}</h3>";
    echo "<p>إجمالي الكورسات</p>";
    echo "</div>";
    echo "</div>";
    echo "</div>";
    echo "</div>";

    echo "<div class='mt-4'>";
    echo "<a href='instructor/add-course.php' class='btn btn-primary btn-lg me-2'>➕ إضافة كورس جديد</a>";
    echo "<a href='admin/dashboard.php' class='btn btn-success btn-lg me-2'>📊 لوحة التحكم</a>";
    echo "<a href='instructor/dashboard.php' class='btn btn-info btn-lg'>👨‍🏫 لوحة المدرب</a>";
    echo "</div>";

} catch (Exception $e) {
    echo "<div class='alert alert-danger'>";
    echo "<h5>❌ حدث خطأ</h5>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "</div>";
echo "</body>";
echo "</html>";
?>
