<?php
require_once 'config/database.php';

echo "<h2>إضافة طلاب تجريبيين سريع</h2>";

try {
    // إنشاء طلاب تجريبيين
    $test_students = [
        ['name' => 'أحمد محمد علي', 'email' => '<EMAIL>', 'phone' => '0501234567'],
        ['name' => 'فاطمة علي حسن', 'email' => '<EMAIL>', 'phone' => '0507654321'],
        ['name' => 'محمد سالم أحمد', 'email' => '<EMAIL>', 'phone' => '0509876543'],
        ['name' => 'نورا أحمد عبدالله', 'email' => '<EMAIL>', 'phone' => '0502468135'],
        ['name' => 'خالد عبدالله محمد', 'email' => '<EMAIL>', 'phone' => '0508642097']
    ];
    
    echo "<h3>إنشاء الطلاب...</h3>";
    
    foreach ($test_students as $student) {
        // التحقق من عدم وجود الطالب
        $check_user = $conn->prepare("SELECT id FROM users WHERE email = ?");
        $check_user->execute([$student['email']]);
        
        if (!$check_user->fetch()) {
            // إنشاء الطالب
            $insert_user = $conn->prepare("
                INSERT INTO users (name, email, phone, password, role, status)
                VALUES (?, ?, ?, ?, 'student', 'active')
            ");
            $insert_user->execute([
                $student['name'],
                $student['email'],
                $student['phone'],
                password_hash('123456', PASSWORD_DEFAULT)
            ]);
            
            $student_id = $conn->lastInsertId();
            echo "<p>✅ تم إنشاء الطالب: {$student['name']} (ID: {$student_id})</p>";
            
            // جلب جميع الكورسات النشطة
            $courses_stmt = $conn->prepare("SELECT id, title FROM courses WHERE status = 'active' LIMIT 3");
            $courses_stmt->execute();
            $courses = $courses_stmt->fetchAll();
            
            if (!empty($courses)) {
                foreach ($courses as $course) {
                    // التحقق من عدم وجود تسجيل مسبق
                    $check_enrollment = $conn->prepare("
                        SELECT id FROM course_enrollments 
                        WHERE course_id = ? AND student_id = ?
                    ");
                    $check_enrollment->execute([$course['id'], $student_id]);
                    
                    if (!$check_enrollment->fetch()) {
                        // التحقق من وجود عمود progress_percentage
                        $columns = $conn->query("SHOW COLUMNS FROM course_enrollments LIKE 'progress_percentage'")->fetchAll();
                        
                        if (empty($columns)) {
                            // إضافة العمود إذا لم يكن موجود
                            $conn->exec("ALTER TABLE course_enrollments ADD COLUMN progress_percentage DECIMAL(5,2) DEFAULT 0.00");
                        }
                        
                        $enroll_stmt = $conn->prepare("
                            INSERT INTO course_enrollments (course_id, student_id, status, enrolled_at, progress_percentage)
                            VALUES (?, ?, 'active', NOW(), ?)
                        ");
                        
                        $progress = rand(10, 95);
                        
                        $enroll_stmt->execute([
                            $course['id'],
                            $student_id,
                            $progress
                        ]);
                        
                        echo "<p>&nbsp;&nbsp;📚 تم تسجيل الطالب في كورس: {$course['title']} (التقدم: {$progress}%)</p>";
                    }
                }
            }
        } else {
            echo "<p>⚠️ الطالب موجود مسبقاً: {$student['name']}</p>";
        }
    }
    
    // عرض الإحصائيات النهائية
    echo "<h3>الإحصائيات النهائية:</h3>";
    
    $stats = $conn->query("
        SELECT 
            (SELECT COUNT(*) FROM users WHERE role = 'student') as total_students,
            (SELECT COUNT(*) FROM course_enrollments) as total_enrollments
    ")->fetch();
    
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p><strong>📊 إجمالي الطلاب:</strong> {$stats['total_students']}</p>";
    echo "<p><strong>📚 إجمالي التسجيلات:</strong> {$stats['total_enrollments']}</p>";
    echo "</div>";
    
    echo "<p style='color: green; font-weight: bold;'>✅ تم إنشاء البيانات التجريبية بنجاح!</p>";
    echo "<p><a href='instructor/students.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>عرض صفحة الطلاب</a></p>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>خطأ في قاعدة البيانات: " . $e->getMessage() . "</p>";
}
?>
