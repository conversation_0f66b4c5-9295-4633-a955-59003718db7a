<?php
/**
 * إصلاح مشكلة اسم المستخدم في إدارة الطلاب
 * Fix username issue in student management
 */

require_once 'config/database.php';

echo "<!DOCTYPE html>";
echo "<html dir='rtl' lang='ar'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>إصلاح مشكلة إدارة الطلاب</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "</head>";
echo "<body>";
echo "<div class='container mt-4'>";
echo "<h2>🔧 إصلاح مشكلة اسم المستخدم في إدارة الطلاب</h2>";

try {
    // 1. فحص جدول users
    echo "<h4>👤 فحص جدول المستخدمين</h4>";
    
    $stmt = $conn->query("SHOW COLUMNS FROM users");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    if (!in_array('username', $columns)) {
        echo "<div class='alert alert-warning'>⚠️ حقل username غير موجود في جدول users</div>";
        
        // إضافة حقل username
        $conn->exec("ALTER TABLE users ADD COLUMN username VARCHAR(50) UNIQUE DEFAULT NULL AFTER email");
        echo "<div class='alert alert-success'>✅ تم إضافة حقل username</div>";
        
        // تحديث أسماء المستخدمين الموجودة
        $stmt = $conn->query("SELECT id, email FROM users WHERE username IS NULL");
        $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $updated_count = 0;
        foreach ($users as $user) {
            $username = substr($user['email'], 0, strpos($user['email'], '@'));
            // التأكد من أن اسم المستخدم فريد
            $counter = 1;
            $original_username = $username;
            
            while (true) {
                $stmt = $conn->prepare("SELECT id FROM users WHERE username = ?");
                $stmt->execute([$username]);
                
                if (!$stmt->fetch()) {
                    break; // اسم المستخدم متاح
                }
                
                $username = $original_username . $counter;
                $counter++;
            }
            
            $stmt = $conn->prepare("UPDATE users SET username = ? WHERE id = ?");
            $stmt->execute([$username, $user['id']]);
            $updated_count++;
        }
        
        echo "<div class='alert alert-success'>✅ تم تحديث $updated_count اسم مستخدم</div>";
    } else {
        echo "<div class='alert alert-info'>ℹ️ حقل username موجود</div>";
    }

    // 2. فحص وإصلاح أسماء المستخدمين الفارغة
    echo "<h4>🔍 فحص أسماء المستخدمين الفارغة</h4>";
    
    $stmt = $conn->query("SELECT COUNT(*) FROM users WHERE username IS NULL OR username = ''");
    $empty_usernames = $stmt->fetchColumn();
    
    if ($empty_usernames > 0) {
        echo "<div class='alert alert-warning'>⚠️ يوجد $empty_usernames مستخدم بدون اسم مستخدم</div>";
        
        $stmt = $conn->query("SELECT id, email FROM users WHERE username IS NULL OR username = ''");
        $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $fixed_count = 0;
        foreach ($users as $user) {
            $username = substr($user['email'], 0, strpos($user['email'], '@'));
            $counter = 1;
            $original_username = $username;
            
            // التأكد من أن اسم المستخدم فريد
            while (true) {
                $stmt = $conn->prepare("SELECT id FROM users WHERE username = ? AND id != ?");
                $stmt->execute([$username, $user['id']]);
                
                if (!$stmt->fetch()) {
                    break;
                }
                
                $username = $original_username . $counter;
                $counter++;
            }
            
            $stmt = $conn->prepare("UPDATE users SET username = ? WHERE id = ?");
            $stmt->execute([$username, $user['id']]);
            $fixed_count++;
            
            echo "<div class='alert alert-success'>✅ تم تحديث اسم المستخدم للبريد: " . htmlspecialchars($user['email']) . " إلى: $username</div>";
        }
        
        echo "<div class='alert alert-info'>ℹ️ تم إصلاح $fixed_count اسم مستخدم</div>";
    } else {
        echo "<div class='alert alert-info'>ℹ️ جميع المستخدمين لديهم أسماء مستخدمين</div>";
    }

    // 3. فحص وإصلاح حقل last_login
    echo "<h4>⏰ فحص حقل آخر تسجيل دخول</h4>";
    
    if (!in_array('last_login', $columns)) {
        echo "<div class='alert alert-warning'>⚠️ حقل last_login غير موجود</div>";
        
        $conn->exec("ALTER TABLE users ADD COLUMN last_login TIMESTAMP NULL DEFAULT NULL AFTER created_at");
        echo "<div class='alert alert-success'>✅ تم إضافة حقل last_login</div>";
    } else {
        echo "<div class='alert alert-info'>ℹ️ حقل last_login موجود</div>";
    }

    // 4. اختبار الاستعلام المحدث
    echo "<h4>🧪 اختبار الاستعلام المحدث</h4>";
    
    try {
        $stmt = $conn->prepare("
            SELECT DISTINCT u.id, u.name, u.email, u.phone, u.created_at, u.last_login,
            COALESCE(u.username, SUBSTRING(u.email, 1, LOCATE('@', u.email) - 1)) as username,
            (SELECT COUNT(DISTINCT course_id) 
             FROM course_enrollments 
             WHERE student_id = u.id) as enrolled_courses,
            (SELECT COUNT(DISTINCT sa.session_id)
             FROM session_attendance sa
             JOIN sessions s ON sa.session_id = s.id
             JOIN courses c ON s.course_id = c.id
             WHERE sa.student_id = u.id) as attended_sessions
            FROM users u
            WHERE u.role = 'student'
            ORDER BY u.created_at DESC
            LIMIT 5
        ");
        $stmt->execute();
        $test_students = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<div class='alert alert-success'>✅ الاستعلام يعمل بشكل صحيح</div>";
        
        if (!empty($test_students)) {
            echo "<div class='table-responsive'>";
            echo "<table class='table table-striped'>";
            echo "<thead class='table-dark'>";
            echo "<tr>";
            echo "<th>الاسم</th>";
            echo "<th>البريد الإلكتروني</th>";
            echo "<th>اسم المستخدم</th>";
            echo "<th>الكورسات</th>";
            echo "<th>الجلسات</th>";
            echo "</tr>";
            echo "</thead>";
            echo "<tbody>";
            
            foreach ($test_students as $student) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($student['name']) . "</td>";
                echo "<td>" . htmlspecialchars($student['email']) . "</td>";
                echo "<td>" . htmlspecialchars($student['username']) . "</td>";
                echo "<td>" . $student['enrolled_courses'] . "</td>";
                echo "<td>" . $student['attended_sessions'] . "</td>";
                echo "</tr>";
            }
            
            echo "</tbody>";
            echo "</table>";
            echo "</div>";
        }
        
    } catch (Exception $e) {
        echo "<div class='alert alert-danger'>❌ خطأ في الاستعلام: " . htmlspecialchars($e->getMessage()) . "</div>";
    }

    // 5. إنشاء طلاب تجريبيين إذا لم يكونوا موجودين
    echo "<h4>👥 إنشاء طلاب تجريبيين</h4>";
    
    $stmt = $conn->query("SELECT COUNT(*) FROM users WHERE role = 'student'");
    $student_count = $stmt->fetchColumn();
    
    if ($student_count < 3) {
        echo "<div class='alert alert-warning'>⚠️ عدد قليل من الطلاب ($student_count). سيتم إنشاء طلاب تجريبيين</div>";
        
        $sample_students = [
            ['أحمد محمد علي', '<EMAIL>', 'ahmed123'],
            ['فاطمة أحمد حسن', '<EMAIL>', 'fatima123'],
            ['محمد عبدالله سالم', '<EMAIL>', 'mohammed123'],
            ['نور الهدى يوسف', '<EMAIL>', 'noor123'],
            ['عبدالرحمن خالد', '<EMAIL>', 'abdulrahman123']
        ];
        
        $created_count = 0;
        foreach ($sample_students as $student_data) {
            // التحقق من عدم وجود البريد الإلكتروني
            $stmt = $conn->prepare("SELECT id FROM users WHERE email = ?");
            $stmt->execute([$student_data[1]]);
            
            if (!$stmt->fetch()) {
                $stmt = $conn->prepare("
                    INSERT INTO users (name, email, username, password, role, status, created_at)
                    VALUES (?, ?, ?, ?, 'student', 'active', NOW())
                ");
                $stmt->execute([
                    $student_data[0],
                    $student_data[1],
                    $student_data[2],
                    password_hash('student123', PASSWORD_DEFAULT)
                ]);
                
                $created_count++;
                echo "<div class='alert alert-success'>✅ تم إنشاء طالب: " . htmlspecialchars($student_data[0]) . "</div>";
            }
        }
        
        if ($created_count == 0) {
            echo "<div class='alert alert-info'>ℹ️ الطلاب التجريبيون موجودون مسبقاً</div>";
        }
    } else {
        echo "<div class='alert alert-info'>ℹ️ يوجد $student_count طالب في النظام</div>";
    }

    // 6. تسجيل بعض الطلاب في الكورسات
    echo "<h4>📚 تسجيل الطلاب في الكورسات</h4>";
    
    $stmt = $conn->query("SELECT id FROM courses WHERE status = 'active' LIMIT 3");
    $courses = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $stmt = $conn->query("SELECT id FROM users WHERE role = 'student' LIMIT 5");
    $students = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $enrollments_created = 0;
    
    foreach ($students as $student_id) {
        foreach ($courses as $course_id) {
            // التحقق من عدم وجود تسجيل سابق
            $stmt = $conn->prepare("SELECT id FROM course_enrollments WHERE course_id = ? AND student_id = ?");
            $stmt->execute([$course_id, $student_id]);
            
            if (!$stmt->fetch()) {
                $stmt = $conn->prepare("
                    INSERT INTO course_enrollments (course_id, student_id, enrollment_date, status)
                    VALUES (?, ?, NOW(), 'active')
                ");
                $stmt->execute([$course_id, $student_id]);
                $enrollments_created++;
            }
        }
    }
    
    if ($enrollments_created > 0) {
        echo "<div class='alert alert-success'>✅ تم إنشاء $enrollments_created تسجيل في الكورسات</div>";
    } else {
        echo "<div class='alert alert-info'>ℹ️ الطلاب مسجلين في الكورسات مسبقاً</div>";
    }

    echo "<div class='alert alert-success mt-4'>";
    echo "<h5>🎉 تم إصلاح جميع المشاكل بنجاح!</h5>";
    echo "<ul class='mb-0'>";
    echo "<li>✅ تم إضافة حقل username إذا لم يكن موجوداً</li>";
    echo "<li>✅ تم تحديث أسماء المستخدمين الفارغة</li>";
    echo "<li>✅ تم إضافة حقل last_login</li>";
    echo "<li>✅ تم اختبار الاستعلام المحدث</li>";
    echo "<li>✅ تم إنشاء طلاب تجريبيين</li>";
    echo "<li>✅ تم تسجيل الطلاب في الكورسات</li>";
    echo "</ul>";
    echo "</div>";

} catch (Exception $e) {
    echo "<div class='alert alert-danger'>❌ خطأ: " . htmlspecialchars($e->getMessage()) . "</div>";
}

echo "<div class='text-center mt-4'>";
echo "<a href='instructor/manage-students.php' class='btn btn-primary btn-lg me-2'>👥 إدارة الطلاب</a>";
echo "<a href='instructor/dashboard.php' class='btn btn-success btn-lg me-2'>🏠 لوحة المدرب</a>";
echo "<a href='login.php' class='btn btn-info btn-lg'>🔐 تسجيل الدخول</a>";
echo "</div>";

echo "<div class='mt-4'>";
echo "<div class='alert alert-info'>";
echo "<h6>💡 بيانات تسجيل الدخول للطلاب التجريبيين:</h6>";
echo "<ul>";
echo "<li><strong>البريد:</strong> <EMAIL> | <strong>كلمة المرور:</strong> student123</li>";
echo "<li><strong>البريد:</strong> <EMAIL> | <strong>كلمة المرور:</strong> student123</li>";
echo "<li><strong>البريد:</strong> <EMAIL> | <strong>كلمة المرور:</strong> student123</li>";
echo "</ul>";
echo "</div>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>
