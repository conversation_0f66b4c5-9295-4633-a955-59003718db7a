<?php
/**
 * إصلاح أعمدة قاعدة البيانات المفقودة
 * Fix Missing Database Columns
 */

// تفعيل عرض الأخطاء
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// تضمين ملف الاتصال بقاعدة البيانات
require_once 'config/database.php';

echo "<h1>🔧 إصلاح أعمدة قاعدة البيانات</h1>";

try {
    echo "<p>✅ تم الاتصال بقاعدة البيانات بنجاح</p>";
    
    // فحص جدول المستخدمين
    $stmt = $conn->query("DESCRIBE users");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<h3>الأعمدة الحالية في جدول المستخدمين:</h3>";
    echo "<ul>";
    foreach ($columns as $column) {
        echo "<li>$column</li>";
    }
    echo "</ul>";
    
    // إضافة العمود المفقود phone إذا لم يكن موجوداً
    if (!in_array('phone', $columns)) {
        echo "<p>🔄 إضافة عمود phone...</p>";
        $conn->exec("ALTER TABLE users ADD COLUMN phone VARCHAR(20) DEFAULT NULL");
        echo "<p>✅ تم إضافة عمود phone بنجاح</p>";
    } else {
        echo "<p>ℹ️ عمود phone موجود مسبقاً</p>";
    }
    
    // فحص جدول الجلسات
    $stmt = $conn->query("DESCRIBE sessions");
    $sessionColumns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<h3>الأعمدة الحالية في جدول الجلسات:</h3>";
    echo "<ul>";
    foreach ($sessionColumns as $column) {
        echo "<li>$column</li>";
    }
    echo "</ul>";
    
    // إضافة الأعمدة المفقودة في جدول الجلسات
    $requiredSessionColumns = [
        'course_id' => 'INT DEFAULT NULL',
        'created_by' => 'INT DEFAULT NULL',
        'zoom_join_url' => 'TEXT DEFAULT NULL',
        'topic' => 'VARCHAR(255) DEFAULT NULL'
    ];
    
    foreach ($requiredSessionColumns as $columnName => $columnDefinition) {
        if (!in_array($columnName, $sessionColumns)) {
            echo "<p>🔄 إضافة عمود $columnName في جدول sessions...</p>";
            $conn->exec("ALTER TABLE sessions ADD COLUMN $columnName $columnDefinition");
            echo "<p>✅ تم إضافة عمود $columnName بنجاح</p>";
        } else {
            echo "<p>ℹ️ عمود $columnName موجود مسبقاً في جدول sessions</p>";
        }
    }
    
    // إضافة المؤشرات المفقودة
    echo "<h3>إضافة المؤشرات:</h3>";
    
    $indexes = [
        "CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)",
        "CREATE INDEX IF NOT EXISTS idx_users_role ON users(role)",
        "CREATE INDEX IF NOT EXISTS idx_sessions_start_time ON sessions(start_time)",
        "CREATE INDEX IF NOT EXISTS idx_sessions_course_id ON sessions(course_id)"
    ];
    
    foreach ($indexes as $index) {
        try {
            $conn->exec($index);
            echo "<p>✅ تم إنشاء مؤشر بنجاح</p>";
        } catch (PDOException $e) {
            if (strpos($e->getMessage(), 'already exists') !== false || strpos($e->getMessage(), 'Duplicate key') !== false) {
                echo "<p>ℹ️ المؤشر موجود مسبقاً</p>";
            } else {
                echo "<p>⚠️ خطأ في إنشاء المؤشر: " . $e->getMessage() . "</p>";
            }
        }
    }
    
    // تحديث بيانات المستخدمين لإضافة أرقام هواتف تجريبية
    echo "<h3>تحديث بيانات المستخدمين:</h3>";
    
    $updateUsers = [
        ['email' => '<EMAIL>', 'phone' => '+966501234567'],
        ['email' => '<EMAIL>', 'phone' => '+966507654321'],
        ['email' => '<EMAIL>', 'phone' => '+966509876543']
    ];
    
    $updateStmt = $conn->prepare("UPDATE users SET phone = ? WHERE email = ? AND phone IS NULL");
    
    foreach ($updateUsers as $user) {
        $updateStmt->execute([$user['phone'], $user['email']]);
        echo "<p>✅ تم تحديث رقم هاتف {$user['email']}</p>";
    }
    
    // التحقق من النتائج النهائية
    echo "<h3>التحقق من النتائج:</h3>";
    
    $stmt = $conn->query("SELECT id, name, email, phone, role FROM users LIMIT 5");
    $users = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>الاسم</th><th>البريد</th><th>الهاتف</th><th>الدور</th></tr>";
    
    foreach ($users as $user) {
        echo "<tr>";
        echo "<td>{$user['id']}</td>";
        echo "<td>{$user['name']}</td>";
        echo "<td>{$user['email']}</td>";
        echo "<td>" . ($user['phone'] ?? 'غير محدد') . "</td>";
        echo "<td>{$user['role']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h2>🎉 تم إصلاح جميع المشاكل بنجاح!</h2>";
    echo "<p><a href='login.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>العودة لتسجيل الدخول</a></p>";
    echo "<p><a href='profile.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;'>اختبار الملف الشخصي</a></p>";
    
} catch (Exception $e) {
    echo "<h2>❌ حدث خطأ:</h2>";
    echo "<p><strong>الخطأ:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>الملف:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>السطر:</strong> " . $e->getLine() . "</p>";
}
?>

<style>
body {
    font-family: 'Tahoma', sans-serif;
    direction: rtl;
    text-align: right;
    margin: 20px;
    background-color: #f8f9fa;
}

h1, h2, h3 {
    color: #333;
}

table {
    background: white;
    margin: 10px 0;
}

th {
    background: #007bff;
    color: white;
    padding: 10px;
}

td {
    padding: 8px;
    border: 1px solid #ddd;
}

p {
    margin: 5px 0;
}

ul {
    background: white;
    padding: 15px;
    border-radius: 5px;
    border-left: 4px solid #007bff;
}
</style>
