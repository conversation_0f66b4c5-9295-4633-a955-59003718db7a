<?php
require_once 'config/database.php';

echo "<h2>إنشاء طلبات انضمام تجريبية</h2>";

if (isset($_GET['create'])) {
    $test_requests = [
        [
            'name' => 'أحمد محمد علي',
            'email' => '<EMAIL>',
            'phone' => '0501234567'
        ],
        [
            'name' => 'فاطمة أحمد',
            'email' => '<EMAIL>',
            'phone' => '0507654321'
        ],
        [
            'name' => 'محمد عبدالله',
            'email' => '<EMAIL>',
            'phone' => '0509876543'
        ],
        [
            'name' => 'نورا سالم',
            'email' => '<EMAIL>',
            'phone' => '0502468135'
        ],
        [
            'name' => 'خالد يوسف',
            'email' => '<EMAIL>',
            'phone' => '0508642097'
        ]
    ];
    
    try {
        foreach ($test_requests as $request) {
            // التحقق من عدم وجود الطلب مسبقاً
            $stmt = $conn->prepare("SELECT id FROM join_requests WHERE email = ?");
            $stmt->execute([$request['email']]);
            
            if ($stmt->rowCount() == 0) {
                $stmt = $conn->prepare("INSERT INTO join_requests (name, email, phone, status) VALUES (?, ?, ?, 'pending')");
                $stmt->execute([$request['name'], $request['email'], $request['phone']]);
                echo "<p style='color: green;'>✅ تم إنشاء طلب انضمام لـ " . htmlspecialchars($request['name']) . "</p>";
            } else {
                echo "<p style='color: orange;'>⚠️ طلب انضمام " . htmlspecialchars($request['name']) . " موجود مسبقاً</p>";
            }
        }
        
        echo "<p style='color: blue;'>✅ تم الانتهاء من إنشاء الطلبات التجريبية</p>";
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ خطأ: " . $e->getMessage() . "</p>";
    }
}

// عرض الطلبات الحالية
try {
    $stmt = $conn->prepare("SELECT * FROM join_requests ORDER BY created_at DESC LIMIT 10");
    $stmt->execute();
    $requests = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>آخر 10 طلبات انضمام:</h3>";
    if ($requests) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>الاسم</th><th>البريد</th><th>الهاتف</th><th>الحالة</th><th>تاريخ الإنشاء</th></tr>";
        foreach ($requests as $request) {
            echo "<tr>";
            echo "<td>" . $request['id'] . "</td>";
            echo "<td>" . htmlspecialchars($request['name']) . "</td>";
            echo "<td>" . htmlspecialchars($request['email']) . "</td>";
            echo "<td>" . htmlspecialchars($request['phone']) . "</td>";
            echo "<td>" . $request['status'] . "</td>";
            echo "<td>" . $request['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>لا توجد طلبات انضمام</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في جلب الطلبات: " . $e->getMessage() . "</p>";
}
?>

<p><a href="?create=1">إنشاء طلبات تجريبية</a></p>
<p><a href="admin/join-requests.php">عرض صفحة إدارة طلبات الانضمام</a></p>
<p><a href="register.php">صفحة التسجيل</a></p>
