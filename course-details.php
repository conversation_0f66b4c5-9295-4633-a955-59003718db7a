<?php
require_once 'config/database.php';
require_once 'includes/functions.php';

$course_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
$error = '';

// جلب تفاصيل الكورس
try {
    $stmt = $conn->prepare("
        SELECT c.*, u.name as instructor_name, u.email as instructor_email, u.phone as instructor_phone,
               (SELECT COUNT(*) FROM course_enrollments WHERE course_id = c.id AND status = 'active') as enrolled_students,
               (SELECT COUNT(*) FROM sessions WHERE course_id = c.id) as total_sessions,
               (SELECT AVG(rating) FROM course_reviews WHERE course_id = c.id) as avg_rating,
               (SELECT COUNT(*) FROM course_reviews WHERE course_id = c.id) as total_reviews
        FROM courses c
        JOIN users u ON c.instructor_id = u.id
        WHERE c.id = ? AND c.status = 'active'
    ");
    $stmt->execute([$course_id]);
    $course = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$course) {
        $error = 'الكورس غير موجود أو غير متاح';
    }
    
    // جلب جلسات الكورس
    if ($course) {
        $stmt = $conn->prepare("
            SELECT * FROM sessions 
            WHERE course_id = ? 
            ORDER BY session_date ASC, start_time ASC
        ");
        $stmt->execute([$course_id]);
        $sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // جلب المراجعات
        $stmt = $conn->prepare("
            SELECT cr.*, u.name as student_name
            FROM course_reviews cr
            JOIN users u ON cr.student_id = u.id
            WHERE cr.course_id = ?
            ORDER BY cr.created_at DESC
            LIMIT 10
        ");
        $stmt->execute([$course_id]);
        $reviews = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // جلب المواد التعليمية
        $stmt = $conn->prepare("
            SELECT * FROM course_materials
            WHERE course_id = ? AND status = 'active'
            ORDER BY created_at DESC
        ");
        $stmt->execute([$course_id]);
        $materials = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // جلب إحصائيات إضافية
        $stmt = $conn->prepare("
            SELECT
                (SELECT COUNT(*) FROM course_enrollments WHERE course_id = ? AND status = 'active') as active_enrollments,
                (SELECT COUNT(*) FROM sessions WHERE course_id = ? AND session_date >= CURDATE()) as upcoming_sessions,
                (SELECT COUNT(*) FROM sessions WHERE course_id = ? AND session_date < CURDATE()) as completed_sessions,
                (SELECT MIN(session_date) FROM sessions WHERE course_id = ? AND session_date >= CURDATE()) as next_session_date
        ");
        $stmt->execute([$course_id, $course_id, $course_id, $course_id]);
        $course_stats = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // جلب كورسات مشابهة
        $stmt = $conn->prepare("
            SELECT c.*, u.name as instructor_name,
                   (SELECT COUNT(*) FROM course_enrollments WHERE course_id = c.id AND status = 'active') as enrolled_students
            FROM courses c
            JOIN users u ON c.instructor_id = u.id
            WHERE c.category = ? AND c.id != ? AND c.status = 'active'
            ORDER BY c.created_at DESC
            LIMIT 3
        ");
        $stmt->execute([$course['category'], $course_id]);
        $similar_courses = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
} catch (PDOException $e) {
    $error = 'حدث خطأ في جلب بيانات الكورس';
}

$pageTitle = $course ? $course['title'] : 'تفاصيل الكورس';
?>

<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="<?php echo htmlspecialchars($course ? substr($course['description'], 0, 160) : 'تفاصيل الكورس'); ?>">
    <meta name="keywords" content="كورس, تعلم, تدريب, <?php echo htmlspecialchars($course ? $course['category'] : ''); ?>">
    <title><?php echo htmlspecialchars($pageTitle); ?> - منصة التعلم</title>

    <!-- الخطوط المحسنة -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Bootstrap و Font Awesome -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- AOS Animation -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">

    <!-- ملفات CSS المخصصة -->
    <link href="assets/css/main.css" rel="stylesheet">
    <link href="assets/css/components.css" rel="stylesheet">
    <style>
        body {
            font-family: var(--font-family-arabic);
            line-height: 1.6;
            background: var(--gray-50);
        }

        .course-header {
            background: var(--gradient-primary);
            color: var(--white);
            padding: 120px 0 80px;
            position: relative;
            overflow: hidden;
        }

        .course-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 80%, rgba(255,255,255,0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255,255,255,0.1) 0%, transparent 50%);
            animation: particleFloat 8s ease-in-out infinite;
        }

        .course-header::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 100px;
            background: linear-gradient(to right bottom, transparent 49%, var(--gray-50) 50%);
        }

        .course-header-content {
            position: relative;
            z-index: 2;
        }

        .course-image-enhanced {
            height: 350px;
            background: var(--gradient-primary);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--white);
            font-size: 6rem;
            border-radius: var(--border-radius-3xl);
            box-shadow: var(--shadow-xl);
            position: relative;
            overflow: hidden;
        }

        .course-image-enhanced img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: var(--border-radius-3xl);
        }

        .course-image-enhanced::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, transparent 25%, rgba(255,255,255,0.1) 25%, rgba(255,255,255,0.1) 50%, transparent 50%);
            background-size: 30px 30px;
            animation: shimmer 2s linear infinite;
        }

        @keyframes shimmer {
            0% { background-position: -30px 0; }
            100% { background-position: 30px 0; }
        }

        .price-card-enhanced {
            position: sticky;
            top: 120px;
            background: var(--white);
            border: var(--border-width-1) solid var(--gray-200);
            border-radius: var(--border-radius-3xl);
            box-shadow: var(--shadow-xl);
            overflow: hidden;
            transition: var(--transition-normal);
        }

        .price-card-enhanced:hover {
            transform: translateY(-8px);
            box-shadow: var(--shadow-2xl);
        }

        .price-card-enhanced::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 6px;
            background: var(--gradient-primary);
        }

        .btn-enroll-enhanced {
            background: var(--gradient-success);
            border: none;
            border-radius: var(--border-radius-full);
            padding: var(--spacing-4) var(--spacing-8);
            font-weight: var(--font-weight-bold);
            font-size: var(--font-size-lg);
            color: var(--white);
            transition: var(--transition-normal);
            position: relative;
            overflow: hidden;
        }

        .btn-enroll-enhanced:hover {
            transform: translateY(-3px);
            box-shadow: var(--shadow-xl);
            color: var(--white);
        }

        .btn-enroll-enhanced::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(255,255,255,0.3);
            border-radius: var(--border-radius-full);
            transform: translate(-50%, -50%);
            transition: var(--transition-fast);
        }

        .btn-enroll-enhanced:hover::before {
            width: 300px;
            height: 300px;
        }

        .rating-stars {
            color: var(--warning-color);
        }

        .session-item-enhanced {
            background: var(--white);
            border: var(--border-width-1) solid var(--gray-200);
            border-radius: var(--border-radius-2xl);
            padding: var(--spacing-6);
            margin-bottom: var(--spacing-4);
            transition: var(--transition-normal);
            position: relative;
            overflow: hidden;
        }

        .session-item-enhanced::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 4px;
            height: 100%;
            background: var(--gradient-primary);
            transform: scaleY(0);
            transition: var(--transition-normal);
        }

        .session-item-enhanced:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-lg);
            border-color: var(--primary-light);
        }

        .session-item-enhanced:hover::before {
            transform: scaleY(1);
        }

        .review-card-enhanced {
            background: var(--white);
            border: var(--border-width-1) solid var(--gray-200);
            border-radius: var(--border-radius-2xl);
            box-shadow: var(--shadow-sm);
            transition: var(--transition-normal);
            margin-bottom: var(--spacing-4);
        }

        .review-card-enhanced:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-md);
        }

        .similar-course-card-enhanced {
            transition: var(--transition-normal);
            border: none;
            border-radius: var(--border-radius-2xl);
            overflow: hidden;
            background: var(--white);
            box-shadow: var(--shadow-sm);
        }

        .similar-course-card-enhanced:hover {
            transform: translateY(-8px);
            box-shadow: var(--shadow-xl);
        }

        .breadcrumb-enhanced {
            background: transparent;
            padding: 0;
        }

        .breadcrumb-enhanced .breadcrumb-item + .breadcrumb-item::before {
            content: "←";
            color: rgba(255,255,255,0.7);
        }

        .breadcrumb-enhanced a {
            color: rgba(255,255,255,0.8);
            text-decoration: none;
            transition: var(--transition-fast);
        }

        .breadcrumb-enhanced a:hover {
            color: var(--white);
        }

        .info-badge {
            background: rgba(255,255,255,0.2);
            color: var(--white);
            padding: var(--spacing-2) var(--spacing-4);
            border-radius: var(--border-radius-full);
            font-size: var(--font-size-sm);
            font-weight: var(--font-weight-medium);
            display: inline-flex;
            align-items: center;
            gap: var(--spacing-2);
            margin: var(--spacing-1);
        }

        .content-card {
            background: var(--white);
            border: var(--border-width-1) solid var(--gray-200);
            border-radius: var(--border-radius-2xl);
            box-shadow: var(--shadow-sm);
            margin-bottom: var(--spacing-6);
            overflow: hidden;
        }

        .content-card-header {
            background: var(--gray-50);
            border-bottom: var(--border-width-1) solid var(--gray-200);
            padding: var(--spacing-5) var(--spacing-6);
        }

        .content-card-body {
            padding: var(--spacing-6);
        }

        .material-card {
            background: var(--gray-50);
            border: var(--border-width-1) solid var(--gray-200);
            border-radius: var(--border-radius-xl);
            transition: var(--transition-normal);
        }

        .material-card:hover {
            background: var(--white);
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--spacing-4);
            margin-bottom: var(--spacing-6);
        }

        .stat-item {
            background: rgba(255,255,255,0.1);
            padding: var(--spacing-4);
            border-radius: var(--border-radius-xl);
            text-align: center;
            backdrop-filter: blur(10px);
        }

        .stat-number {
            font-size: var(--font-size-2xl);
            font-weight: var(--font-weight-bold);
            display: block;
            margin-bottom: var(--spacing-1);
        }

        .stat-label {
            font-size: var(--font-size-sm);
            opacity: 0.9;
        }

        @media (max-width: 768px) {
            .course-header {
                padding: 100px 0 60px;
            }

            .course-image-enhanced {
                height: 250px;
                font-size: 4rem;
            }

            .price-card-enhanced {
                position: static;
                margin-bottom: var(--spacing-6);
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <!-- شريط التنقل المحسن -->
    <nav class="navbar navbar-expand-lg navbar-enhanced" id="mainNavbar">
        <div class="container">
            <a class="navbar-brand-enhanced" href="index.php">
                <div class="logo-icon">
                    <i class="fas fa-graduation-cap"></i>
                </div>
                <span>منصة التعلم</span>
            </a>

            <button class="navbar-toggler border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav mx-auto">
                    <li class="nav-item">
                        <a class="nav-link fw-medium" href="index.php">الرئيسية</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link fw-medium" href="index.php#courses">الكورسات</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link fw-medium" href="index.php#services">الخدمات</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link fw-medium" href="index.php#about">من نحن</a>
                    </li>
                </ul>

                <div class="d-flex gap-2">
                    <a href="login.php" class="btn-enhanced btn-secondary-enhanced">
                        <i class="fas fa-sign-in-alt"></i>
                        <span>تسجيل الدخول</span>
                    </a>
                    <a href="register.php" class="btn-enhanced btn-primary-enhanced">
                        <i class="fas fa-user-plus"></i>
                        <span>إنشاء حساب</span>
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <?php if ($error): ?>
        <div class="container mt-5 pt-5">
            <div class="row justify-content-center">
                <div class="col-md-6">
                    <div class="alert alert-danger text-center">
                        <i class="fas fa-exclamation-triangle fa-3x mb-3"></i>
                        <h4><?php echo htmlspecialchars($error); ?></h4>
                        <a href="index.php" class="btn btn-primary mt-3">العودة للرئيسية</a>
                    </div>
                </div>
            </div>
        </div>
    <?php else: ?>
        <!-- رأس الكورس المحسن -->
        <section class="course-header">
            <div class="container">
                <div class="course-header-content">
                    <div class="row align-items-center">
                        <div class="col-lg-8" data-aos="fade-right">
                            <nav aria-label="breadcrumb" class="mb-4">
                                <ol class="breadcrumb breadcrumb-enhanced">
                                    <li class="breadcrumb-item"><a href="index.php">الرئيسية</a></li>
                                    <li class="breadcrumb-item"><a href="index.php#courses">الكورسات</a></li>
                                    <li class="breadcrumb-item active"><?php echo htmlspecialchars($course['title']); ?></li>
                                </ol>
                            </nav>

                            <h1 class="display-4 fw-bold mb-4 text-gradient-white"><?php echo htmlspecialchars($course['title']); ?></h1>
                            <p class="lead mb-5 opacity-90"><?php echo htmlspecialchars(substr($course['description'], 0, 200)) . '...'; ?></p>

                            <!-- إحصائيات سريعة -->
                            <div class="stats-grid mb-5">
                                <div class="stat-item">
                                    <span class="stat-number"><?php echo $course['enrolled_students']; ?></span>
                                    <span class="stat-label">طالب مسجل</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-number"><?php echo $course['total_sessions']; ?></span>
                                    <span class="stat-label">جلسة</span>
                                </div>
                                <?php if ($course_stats['upcoming_sessions'] > 0): ?>
                                <div class="stat-item">
                                    <span class="stat-number"><?php echo $course_stats['upcoming_sessions']; ?></span>
                                    <span class="stat-label">جلسة قادمة</span>
                                </div>
                                <?php endif; ?>
                                <?php if ($course['avg_rating']): ?>
                                <div class="stat-item">
                                    <span class="stat-number"><?php echo number_format($course['avg_rating'], 1); ?></span>
                                    <span class="stat-label">تقييم</span>
                                </div>
                                <?php endif; ?>
                            </div>

                            <!-- معلومات إضافية -->
                            <div class="d-flex flex-wrap gap-2 mb-4">
                                <span class="info-badge">
                                    <i class="fas fa-user"></i>
                                    المدرب: <?php echo htmlspecialchars($course['instructor_name']); ?>
                                </span>

                                <?php if ($course['category']): ?>
                                <span class="info-badge">
                                    <i class="fas fa-tag"></i>
                                    <?php echo htmlspecialchars($course['category']); ?>
                                </span>
                                <?php endif; ?>

                                <?php if ($course_stats['next_session_date']): ?>
                                <span class="info-badge">
                                    <i class="fas fa-calendar"></i>
                                    الجلسة القادمة: <?php echo date('Y-m-d', strtotime($course_stats['next_session_date'])); ?>
                                </span>
                                <?php endif; ?>

                                <?php if ($course['avg_rating']): ?>
                                <span class="info-badge">
                                    <div class="rating-stars me-1">
                                        <?php
                                        $rating = round($course['avg_rating']);
                                        for ($i = 1; $i <= 5; $i++) {
                                            echo $i <= $rating ? '<i class="fas fa-star"></i>' : '<i class="far fa-star"></i>';
                                        }
                                        ?>
                                    </div>
                                    <?php echo $course['total_reviews']; ?> مراجعة
                                </span>
                                <?php endif; ?>
                            </div>

                            <!-- أزرار العمل -->
                            <div class="d-flex gap-3">
                                <a href="#course-content" class="btn-enhanced" style="background: rgba(255,255,255,0.2); color: white; border: 2px solid rgba(255,255,255,0.3);">
                                    <i class="fas fa-info-circle"></i>
                                    <span>تفاصيل الكورس</span>
                                </a>
                                <a href="#course-sessions" class="btn-enhanced" style="background: rgba(255,255,255,0.2); color: white; border: 2px solid rgba(255,255,255,0.3);">
                                    <i class="fas fa-video"></i>
                                    <span>الجلسات</span>
                                </a>
                            </div>
                        </div>

                        <div class="col-lg-4" data-aos="fade-left" data-aos-delay="200">
                            <div class="course-image-enhanced">
                                <?php if ($course['image_path']): ?>
                                    <img src="<?php echo htmlspecialchars($course['image_path']); ?>"
                                         alt="<?php echo htmlspecialchars($course['title']); ?>">
                                <?php else: ?>
                                    <i class="fas fa-graduation-cap"></i>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- محتوى الكورس -->
        <section class="py-5">
            <div class="container">
                <div class="row">
                    <!-- المحتوى الرئيسي -->
                    <div class="col-lg-8">
                        <!-- وصف الكورس -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-info-circle me-2"></i>
                                    وصف الكورس
                                </h5>
                            </div>
                            <div class="card-body">
                                <p><?php echo nl2br(htmlspecialchars($course['description'])); ?></p>
                                
                                <?php if ($course['category']): ?>
                                    <div class="mt-3">
                                        <span class="badge bg-primary fs-6">
                                            <i class="fas fa-tag me-1"></i>
                                            <?php echo htmlspecialchars($course['category']); ?>
                                        </span>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- جلسات الكورس -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-video me-2"></i>
                                    جلسات الكورس (<?php echo count($sessions); ?>)
                                </h5>
                            </div>
                            <div class="card-body">
                                <?php if (empty($sessions)): ?>
                                    <p class="text-muted">لم يتم إضافة جلسات لهذا الكورس بعد</p>
                                <?php else: ?>
                                    <?php foreach ($sessions as $index => $session): ?>
                                        <div class="session-item">
                                            <div class="d-flex justify-content-between align-items-start">
                                                <div>
                                                    <h6 class="mb-2">
                                                        <span class="badge bg-secondary me-2"><?php echo $index + 1; ?></span>
                                                        <?php echo htmlspecialchars($session['title']); ?>
                                                    </h6>
                                                    <?php if ($session['description']): ?>
                                                        <p class="text-muted mb-2"><?php echo htmlspecialchars($session['description']); ?></p>
                                                    <?php endif; ?>
                                                    <small class="text-muted">
                                                        <i class="fas fa-calendar me-1"></i>
                                                        <?php echo $session['session_date'] ? date('Y-m-d', strtotime($session['session_date'])) : 'لم يحدد بعد'; ?>
                                                        <?php if ($session['start_time']): ?>
                                                            <i class="fas fa-clock me-1 ms-3"></i>
                                                            <?php echo date('H:i', strtotime($session['start_time'])); ?>
                                                        <?php endif; ?>
                                                    </small>
                                                </div>
                                                <div>
                                                    <i class="fas fa-play-circle fa-2x text-primary"></i>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- المواد التعليمية -->
                        <?php if (!empty($materials)): ?>
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5 class="mb-0">
                                        <i class="fas fa-download me-2"></i>
                                        المواد التعليمية (<?php echo count($materials); ?>)
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <?php foreach ($materials as $material): ?>
                                            <div class="col-md-6 mb-3">
                                                <div class="card border">
                                                    <div class="card-body">
                                                        <h6 class="card-title">
                                                            <i class="fas fa-file-alt me-2 text-primary"></i>
                                                            <?php echo htmlspecialchars($material['title']); ?>
                                                        </h6>
                                                        <?php if ($material['description']): ?>
                                                            <p class="card-text text-muted"><?php echo htmlspecialchars($material['description']); ?></p>
                                                        <?php endif; ?>
                                                        <div class="d-flex justify-content-between align-items-center">
                                                            <small class="text-muted">
                                                                <i class="fas fa-calendar me-1"></i>
                                                                <?php echo date('Y-m-d', strtotime($material['created_at'])); ?>
                                                            </small>
                                                            <a href="<?php echo htmlspecialchars($material['file_path']); ?>"
                                                               class="btn btn-primary btn-sm" download>
                                                                <i class="fas fa-download me-1"></i>
                                                                تحميل
                                                            </a>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>

                        <!-- المراجعات -->
                        <?php if (!empty($reviews)): ?>
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5 class="mb-0">
                                        <i class="fas fa-star me-2"></i>
                                        آراء الطلاب (<?php echo $course['total_reviews']; ?>)
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <?php foreach ($reviews as $review): ?>
                                        <div class="review-card card mb-3">
                                            <div class="card-body">
                                                <div class="d-flex justify-content-between align-items-start mb-2">
                                                    <h6 class="mb-0"><?php echo htmlspecialchars($review['student_name']); ?></h6>
                                                    <div class="rating-stars">
                                                        <?php
                                                        for ($i = 1; $i <= 5; $i++) {
                                                            echo $i <= $review['rating'] ? '<i class="fas fa-star"></i>' : '<i class="far fa-star"></i>';
                                                        }
                                                        ?>
                                                    </div>
                                                </div>
                                                <p class="mb-1"><?php echo htmlspecialchars($review['comment']); ?></p>
                                                <small class="text-muted"><?php echo date('Y-m-d', strtotime($review['created_at'])); ?></small>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>

                                    <?php if ($course['total_reviews'] > count($reviews)): ?>
                                        <div class="text-center">
                                            <p class="text-muted">عرض <?php echo count($reviews); ?> من أصل <?php echo $course['total_reviews']; ?> مراجعة</p>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- الشريط الجانبي -->
                    <div class="col-lg-4">
                        <!-- بطاقة التسجيل -->
                        <div class="card price-card mb-4">
                            <div class="card-body text-center">
                                <div class="mb-3">
                                    <?php if ($course['course_type'] === 'paid'): ?>
                                        <h3 class="text-primary">
                                            <?php echo number_format($course['price'], 0); ?>
                                            <small><?php echo $course['currency']; ?></small>
                                        </h3>
                                    <?php else: ?>
                                        <h3 class="text-success">مجاني</h3>
                                    <?php endif; ?>
                                </div>

                                <button type="button" class="btn btn-enroll btn-success w-100 mb-3" data-bs-toggle="modal" data-bs-target="#joinRequestModal">
                                    <i class="fas fa-user-plus me-2"></i>
                                    طلب الانضمام للكورس
                                </button>

                                <p class="text-muted mb-0">
                                    <i class="fas fa-info-circle me-1"></i>
                                    يجب تسجيل الدخول أولاً لطلب الانضمام
                                </p>
                            </div>
                        </div>

                        <!-- معلومات الكورس -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-info-circle me-2"></i>
                                    معلومات الكورس
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="d-flex justify-content-between mb-2">
                                    <span>إجمالي الجلسات:</span>
                                    <strong><?php echo $course['total_sessions']; ?></strong>
                                </div>
                                <div class="d-flex justify-content-between mb-2">
                                    <span>الطلاب المسجلين:</span>
                                    <strong><?php echo $course['enrolled_students']; ?></strong>
                                </div>
                                <?php if ($course_stats['upcoming_sessions'] > 0): ?>
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>جلسات قادمة:</span>
                                        <strong class="text-success"><?php echo $course_stats['upcoming_sessions']; ?></strong>
                                    </div>
                                <?php endif; ?>
                                <?php if ($course_stats['completed_sessions'] > 0): ?>
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>جلسات مكتملة:</span>
                                        <strong class="text-info"><?php echo $course_stats['completed_sessions']; ?></strong>
                                    </div>
                                <?php endif; ?>
                                <?php if (!empty($materials)): ?>
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>المواد التعليمية:</span>
                                        <strong><?php echo count($materials); ?></strong>
                                    </div>
                                <?php endif; ?>
                                <?php if ($course['avg_rating']): ?>
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>التقييم:</span>
                                        <div>
                                            <strong><?php echo number_format($course['avg_rating'], 1); ?></strong>
                                            <div class="rating-stars d-inline">
                                                <?php
                                                $rating = round($course['avg_rating']);
                                                for ($i = 1; $i <= 5; $i++) {
                                                    echo $i <= $rating ? '<i class="fas fa-star"></i>' : '<i class="far fa-star"></i>';
                                                }
                                                ?>
                                            </div>
                                        </div>
                                    </div>
                                <?php endif; ?>
                                <div class="d-flex justify-content-between">
                                    <span>تاريخ الإنشاء:</span>
                                    <strong><?php echo date('Y-m-d', strtotime($course['created_at'])); ?></strong>
                                </div>
                            </div>
                        </div>

                        <!-- معلومات المدرب -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-user-tie me-2"></i>
                                    المدرب
                                </h6>
                            </div>
                            <div class="card-body">
                                <h6><?php echo htmlspecialchars($course['instructor_name']); ?></h6>
                                <p class="text-muted mb-2"><?php echo htmlspecialchars($course['instructor_email']); ?></p>
                                <?php if ($course['instructor_phone']): ?>
                                    <p class="text-muted"><?php echo htmlspecialchars($course['instructor_phone']); ?></p>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- كورسات مشابهة -->
                        <?php if (!empty($similar_courses)): ?>
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">
                                        <i class="fas fa-th-large me-2"></i>
                                        كورسات مشابهة
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <?php foreach ($similar_courses as $similar): ?>
                                        <div class="card similar-course-card mb-3">
                                            <div class="card-body">
                                                <h6 class="card-title">
                                                    <a href="course-details.php?id=<?php echo $similar['id']; ?>" class="text-decoration-none">
                                                        <?php echo htmlspecialchars($similar['title']); ?>
                                                    </a>
                                                </h6>
                                                <p class="card-text">
                                                    <small class="text-muted">
                                                        <i class="fas fa-user me-1"></i>
                                                        <?php echo htmlspecialchars($similar['instructor_name']); ?>
                                                    </small>
                                                </p>
                                                <p class="card-text">
                                                    <small class="text-muted">
                                                        <i class="fas fa-users me-1"></i>
                                                        <?php echo $similar['enrolled_students']; ?> طالب
                                                    </small>
                                                </p>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </section>
    <?php endif; ?>

    <!-- شريط التقدم -->
    <div class="scroll-indicator">
        <div class="scroll-progress"></div>
    </div>

    <!-- زر العودة للأعلى -->
    <button class="back-to-top" id="backToTop" aria-label="العودة للأعلى">
        <i class="fas fa-arrow-up"></i>
    </button>

    <!-- نموذج طلب الانضمام -->
    <div class="modal fade" id="joinRequestModal" tabindex="-1" aria-labelledby="joinRequestModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="joinRequestModalLabel">
                        <i class="fas fa-user-plus text-success me-2"></i>
                        طلب الانضمام للكورس
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>كورس:</strong> <?php echo htmlspecialchars($course['title']); ?>
                        <br>
                        <strong>المدرب:</strong> <?php echo htmlspecialchars($course['instructor_name']); ?>
                    </div>

                    <form id="joinRequestForm">
                        <input type="hidden" name="course_id" value="<?php echo $course['id']; ?>">

                        <div class="mb-3">
                            <label for="joinMessage" class="form-label">رسالة للمدرب (اختياري)</label>
                            <textarea class="form-control" id="joinMessage" name="message" rows="4"
                                      placeholder="اكتب سبب رغبتك في الانضمام للكورس أو أي معلومات إضافية..."></textarea>
                            <div class="form-text">
                                يمكنك كتابة سبب اهتمامك بالكورس أو خبرتك السابقة في المجال
                            </div>
                        </div>

                        <div class="alert alert-warning">
                            <i class="fas fa-clock me-2"></i>
                            <strong>ملاحظة:</strong> بعد إرسال الطلب، سيراجع المدرب طلبك وسيتم إشعارك بالنتيجة.
                            قد تستغرق المراجعة من 24-48 ساعة.
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>إلغاء
                    </button>
                    <button type="button" class="btn btn-success" id="submitJoinRequest">
                        <i class="fas fa-paper-plane me-1"></i>إرسال طلب الانضمام
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript Libraries -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script src="assets/js/enhanced.js"></script>

    <script>
        // تهيئة AOS
        AOS.init({
            duration: 800,
            easing: 'ease-in-out',
            once: true,
            offset: 100
        });

        document.addEventListener('DOMContentLoaded', function() {
            // تأثير شريط التنقل عند التمرير
            const navbar = document.getElementById('mainNavbar');
            window.addEventListener('scroll', function() {
                if (window.scrollY > 100) {
                    navbar.classList.add('scrolled');
                } else {
                    navbar.classList.remove('scrolled');
                }
            });

            // تأثير التمرير السلس المحسن
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        const offsetTop = target.offsetTop - 100;
                        window.scrollTo({
                            top: offsetTop,
                            behavior: 'smooth'
                        });
                    }
                });
            });

            // تحديث شريط التقدم
            const updateScrollProgress = () => {
                const winHeight = window.innerHeight;
                const docHeight = document.documentElement.scrollHeight;
                const scrollTop = window.pageYOffset;
                const progress = (scrollTop / (docHeight - winHeight)) * 100;

                const progressBar = document.querySelector('.scroll-progress');
                if (progressBar) {
                    progressBar.style.width = `${Math.min(progress, 100)}%`;
                }
            };

            // زر العودة للأعلى
            const backToTop = document.getElementById('backToTop');
            const toggleBackToTop = () => {
                if (window.scrollY > 500) {
                    backToTop.classList.add('show');
                } else {
                    backToTop.classList.remove('show');
                }
            };

            backToTop.addEventListener('click', () => {
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            });

            // أحداث التمرير
            window.addEventListener('scroll', () => {
                updateScrollProgress();
                toggleBackToTop();
            });
        });

        // معالجة طلب الانضمام
        document.getElementById('submitJoinRequest').addEventListener('click', function() {
            const form = document.getElementById('joinRequestForm');
            const formData = new FormData(form);
            const submitBtn = this;
            const originalText = submitBtn.innerHTML;

            // تعطيل الزر وإظهار التحميل
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>جاري الإرسال...';

            fetch('request-join.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // إظهار رسالة النجاح
                    const modal = bootstrap.Modal.getInstance(document.getElementById('joinRequestModal'));
                    modal.hide();

                    // إظهار تنبيه النجاح
                    const alertDiv = document.createElement('div');
                    alertDiv.className = 'alert alert-success alert-dismissible fade show position-fixed';
                    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; max-width: 400px;';
                    alertDiv.innerHTML = `
                        <i class="fas fa-check-circle me-2"></i>
                        ${data.message}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    `;
                    document.body.appendChild(alertDiv);

                    // إزالة التنبيه بعد 5 ثوان
                    setTimeout(() => {
                        if (alertDiv.parentNode) {
                            alertDiv.remove();
                        }
                    }, 5000);

                    // إعادة تعيين النموذج
                    form.reset();
                } else {
                    // إظهار رسالة الخطأ
                    const alertDiv = document.createElement('div');
                    alertDiv.className = 'alert alert-danger alert-dismissible fade show position-fixed';
                    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; max-width: 400px;';
                    alertDiv.innerHTML = `
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        ${data.message}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    `;
                    document.body.appendChild(alertDiv);

                    // إزالة التنبيه بعد 5 ثوان
                    setTimeout(() => {
                        if (alertDiv.parentNode) {
                            alertDiv.remove();
                        }
                    }, 5000);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                const alertDiv = document.createElement('div');
                alertDiv.className = 'alert alert-danger alert-dismissible fade show position-fixed';
                alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; max-width: 400px;';
                alertDiv.innerHTML = `
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;
                document.body.appendChild(alertDiv);

                setTimeout(() => {
                    if (alertDiv.parentNode) {
                        alertDiv.remove();
                    }
                }, 5000);
            })
            .finally(() => {
                // إعادة تفعيل الزر
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalText;
            });
        });

        // إضافة CSS للتأثيرات
        const style = document.createElement('style');
        style.textContent = `
            .scroll-indicator {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 4px;
                background: rgba(0,0,0,0.1);
                z-index: 9999;
            }

            .scroll-progress {
                height: 100%;
                background: var(--gradient-primary);
                width: 0%;
                transition: width 0.1s ease;
            }

            .back-to-top {
                position: fixed;
                bottom: 2rem;
                left: 2rem;
                width: 50px;
                height: 50px;
                background: var(--gradient-primary);
                color: white;
                border: none;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 1.2rem;
                box-shadow: var(--shadow-lg);
                transform: translateY(100px);
                opacity: 0;
                transition: var(--transition-normal);
                z-index: 1000;
                cursor: pointer;
            }

            .back-to-top.show {
                transform: translateY(0);
                opacity: 1;
            }

            .back-to-top:hover {
                transform: translateY(-5px);
                box-shadow: var(--shadow-xl);
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
