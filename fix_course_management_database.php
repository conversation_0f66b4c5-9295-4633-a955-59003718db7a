<?php
/**
 * إصلاح وإنشاء جداول إدارة الكورسات المتكاملة
 * Fix and create comprehensive course management tables
 */

require_once 'config/database.php';

echo "<!DOCTYPE html>";
echo "<html dir='rtl' lang='ar'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>إصلاح نظام إدارة الكورسات</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "</head>";
echo "<body>";
echo "<div class='container mt-4'>";
echo "<h2>🔧 إصلاح نظام إدارة الكورسات المتكامل</h2>";

try {
    // 1. إنشاء جدول course_enrollments (تسجيل الطلاب في الكورسات)
    echo "<h4>📚 إنشاء جدول course_enrollments</h4>";
    $conn->exec("
        CREATE TABLE IF NOT EXISTS course_enrollments (
            id INT AUTO_INCREMENT PRIMARY KEY,
            course_id INT NOT NULL,
            student_id INT NOT NULL,
            enrollment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            status ENUM('active', 'completed', 'dropped', 'suspended') DEFAULT 'active',
            final_grade DECIMAL(5,2) DEFAULT NULL,
            completion_date TIMESTAMP NULL,
            notes TEXT DEFAULT NULL,
            
            FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
            FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
            UNIQUE KEY unique_enrollment (course_id, student_id),
            INDEX idx_course_id (course_id),
            INDEX idx_student_id (student_id),
            INDEX idx_status (status)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "<div class='alert alert-success'>✅ تم إنشاء جدول course_enrollments</div>";

    // 2. إنشاء جدول sessions المحدث
    echo "<h4>🎥 تحديث جدول sessions</h4>";
    $conn->exec("
        CREATE TABLE IF NOT EXISTS sessions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            course_id INT NOT NULL,
            title VARCHAR(255) NOT NULL,
            description TEXT,
            session_date DATE NOT NULL,
            start_time TIME NOT NULL,
            end_time TIME NOT NULL,
            zoom_link VARCHAR(500),
            meeting_id VARCHAR(100),
            passcode VARCHAR(50),
            status ENUM('scheduled', 'ongoing', 'completed', 'cancelled') DEFAULT 'scheduled',
            max_attendees INT DEFAULT 100,
            recording_url VARCHAR(500) DEFAULT NULL,
            session_file VARCHAR(500) DEFAULT NULL,
            file_name VARCHAR(255) DEFAULT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            
            FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
            INDEX idx_course_id (course_id),
            INDEX idx_session_date (session_date),
            INDEX idx_status (status)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "<div class='alert alert-success'>✅ تم تحديث جدول sessions</div>";

    // 3. إنشاء جدول session_attendance (حضور الجلسات)
    echo "<h4>📊 إنشاء جدول session_attendance</h4>";
    $conn->exec("
        CREATE TABLE IF NOT EXISTS session_attendance (
            id INT AUTO_INCREMENT PRIMARY KEY,
            session_id INT NOT NULL,
            student_id INT NOT NULL,
            attendance_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            leave_time TIMESTAMP NULL,
            duration_minutes INT DEFAULT 0,
            status ENUM('present', 'absent', 'late', 'left_early') DEFAULT 'present',
            notes TEXT DEFAULT NULL,
            
            FOREIGN KEY (session_id) REFERENCES sessions(id) ON DELETE CASCADE,
            FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
            UNIQUE KEY unique_attendance (session_id, student_id),
            INDEX idx_session_id (session_id),
            INDEX idx_student_id (student_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "<div class='alert alert-success'>✅ تم إنشاء جدول session_attendance</div>";

    // 4. إنشاء جدول student_grades (درجات الطلاب)
    echo "<h4>📝 إنشاء جدول student_grades</h4>";
    $conn->exec("
        CREATE TABLE IF NOT EXISTS student_grades (
            id INT AUTO_INCREMENT PRIMARY KEY,
            course_id INT NOT NULL,
            student_id INT NOT NULL,
            assignment_name VARCHAR(255) NOT NULL,
            grade DECIMAL(5,2) NOT NULL,
            max_grade DECIMAL(5,2) NOT NULL DEFAULT 100,
            grade_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            notes TEXT DEFAULT NULL,
            graded_by INT NOT NULL,
            
            FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
            FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
            FOREIGN KEY (graded_by) REFERENCES users(id) ON DELETE CASCADE,
            INDEX idx_course_student (course_id, student_id),
            INDEX idx_assignment (assignment_name)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "<div class='alert alert-success'>✅ تم إنشاء جدول student_grades</div>";

    // 5. إصلاح جدول join_requests (طلبات الانضمام)
    echo "<h4>📋 إصلاح جدول join_requests</h4>";

    // التحقق من وجود الجدول أولاً
    $stmt = $conn->query("SHOW TABLES LIKE 'join_requests'");
    if ($stmt->rowCount() > 0) {
        // الجدول موجود، فحص الأعمدة
        $stmt = $conn->query("SHOW COLUMNS FROM join_requests");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $existing_columns = array_column($columns, 'Field');

        // إضافة الأعمدة المفقودة
        if (!in_array('course_id', $existing_columns)) {
            $conn->exec("ALTER TABLE join_requests ADD COLUMN course_id INT NOT NULL DEFAULT 1");
            echo "<div class='alert alert-success'>✅ تم إضافة عمود course_id</div>";
        }

        if (!in_array('student_name', $existing_columns)) {
            $conn->exec("ALTER TABLE join_requests ADD COLUMN student_name VARCHAR(255) NOT NULL DEFAULT ''");
            echo "<div class='alert alert-success'>✅ تم إضافة عمود student_name</div>";
        }

        if (!in_array('student_email', $existing_columns)) {
            $conn->exec("ALTER TABLE join_requests ADD COLUMN student_email VARCHAR(255) NOT NULL DEFAULT ''");
            echo "<div class='alert alert-success'>✅ تم إضافة عمود student_email</div>";
        }

        if (!in_array('student_phone', $existing_columns)) {
            $conn->exec("ALTER TABLE join_requests ADD COLUMN student_phone VARCHAR(20) DEFAULT NULL");
            echo "<div class='alert alert-success'>✅ تم إضافة عمود student_phone</div>";
        }

        if (!in_array('message', $existing_columns)) {
            $conn->exec("ALTER TABLE join_requests ADD COLUMN message TEXT DEFAULT NULL");
            echo "<div class='alert alert-success'>✅ تم إضافة عمود message</div>";
        }

        if (!in_array('processed_by', $existing_columns)) {
            $conn->exec("ALTER TABLE join_requests ADD COLUMN processed_by INT DEFAULT NULL");
            echo "<div class='alert alert-success'>✅ تم إضافة عمود processed_by</div>";
        }

        if (!in_array('rejection_reason', $existing_columns)) {
            $conn->exec("ALTER TABLE join_requests ADD COLUMN rejection_reason TEXT DEFAULT NULL");
            echo "<div class='alert alert-success'>✅ تم إضافة عمود rejection_reason</div>";
        }

        echo "<div class='alert alert-info'>✅ تم تحديث جدول join_requests</div>";
    } else {
        // إنشاء الجدول من الصفر
        $conn->exec("
            CREATE TABLE join_requests (
                id INT AUTO_INCREMENT PRIMARY KEY,
                course_id INT NOT NULL,
                student_name VARCHAR(255) NOT NULL,
                student_email VARCHAR(255) NOT NULL,
                student_phone VARCHAR(20) DEFAULT NULL,
                message TEXT DEFAULT NULL,
                status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
                request_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                processed_date TIMESTAMP NULL,
                processed_by INT DEFAULT NULL,
                rejection_reason TEXT DEFAULT NULL,

                INDEX idx_course_id (course_id),
                INDEX idx_status (status),
                INDEX idx_email (student_email)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        echo "<div class='alert alert-success'>✅ تم إنشاء جدول join_requests</div>";
    }

    // 6. إنشاء مجلدات الملفات
    echo "<h4>📁 إنشاء مجلدات الملفات</h4>";
    $directories = [
        'uploads/courses',
        'uploads/sessions',
        'uploads/assignments',
        'uploads/materials'
    ];

    foreach ($directories as $dir) {
        if (!file_exists($dir)) {
            if (mkdir($dir, 0777, true)) {
                echo "<div class='alert alert-success'>✅ تم إنشاء مجلد: $dir</div>";
            } else {
                echo "<div class='alert alert-danger'>❌ فشل في إنشاء مجلد: $dir</div>";
            }
        } else {
            echo "<div class='alert alert-info'>ℹ️ مجلد موجود: $dir</div>";
        }
    }

    // 7. إضافة بيانات تجريبية
    echo "<h4>🎯 إضافة بيانات تجريبية</h4>";
    
    // إضافة طلبات انضمام تجريبية
    $sample_requests = [
        ['course_id' => 1, 'name' => 'سارة أحمد', 'email' => '<EMAIL>', 'phone' => '0501234567', 'message' => 'أرغب في الانضمام لهذا الكورس'],
        ['course_id' => 1, 'name' => 'محمد علي', 'email' => '<EMAIL>', 'phone' => '0507654321', 'message' => 'مهتم بتعلم البرمجة'],
        ['course_id' => 2, 'name' => 'فاطمة خالد', 'email' => '<EMAIL>', 'phone' => '0509876543', 'message' => 'أريد تطوير مهاراتي في التصميم']
    ];

    $stmt = $conn->prepare("INSERT IGNORE INTO join_requests (course_id, student_name, student_email, student_phone, message) VALUES (?, ?, ?, ?, ?)");
    foreach ($sample_requests as $request) {
        $stmt->execute([$request['course_id'], $request['name'], $request['email'], $request['phone'], $request['message']]);
    }
    echo "<div class='alert alert-success'>✅ تم إضافة طلبات انضمام تجريبية</div>";

    // 8. إضافة جلسات تجريبية
    $sample_sessions = [
        ['course_id' => 1, 'title' => 'مقدمة في البرمجة', 'date' => '2025-06-10', 'start' => '10:00:00', 'end' => '11:30:00'],
        ['course_id' => 1, 'title' => 'أساسيات HTML', 'date' => '2025-06-12', 'start' => '10:00:00', 'end' => '11:30:00'],
        ['course_id' => 2, 'title' => 'أساسيات التصميم', 'date' => '2025-06-11', 'start' => '14:00:00', 'end' => '15:30:00']
    ];

    $stmt = $conn->prepare("INSERT IGNORE INTO sessions (course_id, title, description, session_date, start_time, end_time, zoom_link) VALUES (?, ?, ?, ?, ?, ?, ?)");
    foreach ($sample_sessions as $session) {
        $stmt->execute([
            $session['course_id'], 
            $session['title'], 
            'وصف الجلسة', 
            $session['date'], 
            $session['start'], 
            $session['end'],
            'https://zoom.us/j/123456789'
        ]);
    }
    echo "<div class='alert alert-success'>✅ تم إضافة جلسات تجريبية</div>";

    echo "<h4>📊 ملخص النتائج</h4>";
    echo "<div class='row'>";
    
    // عدد الكورسات
    $stmt = $conn->query("SELECT COUNT(*) FROM courses");
    $courses_count = $stmt->fetchColumn();
    echo "<div class='col-md-3'>";
    echo "<div class='card bg-primary text-white'>";
    echo "<div class='card-body text-center'>";
    echo "<h3>$courses_count</h3>";
    echo "<p>كورسات</p>";
    echo "</div></div></div>";

    // عدد الجلسات
    $stmt = $conn->query("SELECT COUNT(*) FROM sessions");
    $sessions_count = $stmt->fetchColumn();
    echo "<div class='col-md-3'>";
    echo "<div class='card bg-success text-white'>";
    echo "<div class='card-body text-center'>";
    echo "<h3>$sessions_count</h3>";
    echo "<p>جلسات</p>";
    echo "</div></div></div>";

    // عدد طلبات الانضمام
    $stmt = $conn->query("SELECT COUNT(*) FROM join_requests");
    $requests_count = $stmt->fetchColumn();
    echo "<div class='col-md-3'>";
    echo "<div class='card bg-warning text-white'>";
    echo "<div class='card-body text-center'>";
    echo "<h3>$requests_count</h3>";
    echo "<p>طلبات انضمام</p>";
    echo "</div></div></div>";

    // عدد المستخدمين
    $stmt = $conn->query("SELECT COUNT(*) FROM users");
    $users_count = $stmt->fetchColumn();
    echo "<div class='col-md-3'>";
    echo "<div class='card bg-info text-white'>";
    echo "<div class='card-body text-center'>";
    echo "<h3>$users_count</h3>";
    echo "<p>مستخدمين</p>";
    echo "</div></div></div>";

    echo "</div>";

} catch (Exception $e) {
    echo "<div class='alert alert-danger'>❌ خطأ: " . $e->getMessage() . "</div>";
}

echo "<div class='mt-4'>";
echo "<a href='instructor/courses.php' class='btn btn-primary btn-lg me-2'>📚 عرض الكورسات</a>";
echo "<a href='instructor/course-details.php?id=1' class='btn btn-success btn-lg me-2'>📋 تفاصيل كورس</a>";
echo "<a href='admin/manage_join_requests.php' class='btn btn-warning btn-lg me-2'>📝 طلبات الانضمام</a>";
echo "<a href='instructor/dashboard.php' class='btn btn-info btn-lg'>🏠 لوحة التحكم</a>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>
