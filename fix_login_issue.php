<?php
/**
 * إصلاح مشكلة تسجيل الدخول
 * Fix Login Issue Script
 */

// تفعيل عرض الأخطاء
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<!DOCTYPE html>";
echo "<html dir='rtl' lang='ar'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>إصلاح مشكلة تسجيل الدخول</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<style>";
echo "body { font-family: 'Tahoma', sans-serif; background-color: #f8f9fa; }";
echo ".container { max-width: 800px; margin-top: 2rem; }";
echo ".step { margin-bottom: 1rem; padding: 1rem; border-radius: 8px; }";
echo ".step.success { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; }";
echo ".step.error { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }";
echo ".step.info { background-color: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }";
echo "</style>";
echo "</head>";
echo "<body>";
echo "<div class='container'>";
echo "<h1 class='text-center mb-4'>🔧 إصلاح مشكلة تسجيل الدخول</h1>";

try {
    // الاتصال بقاعدة البيانات
    $host = 'localhost';
    $user = 'root';
    $pass = '';
    
    echo "<div class='step info'>";
    echo "<h5>🔍 فحص قواعد البيانات الموجودة</h5>";
    echo "</div>";
    
    $conn = new PDO("mysql:host=$host;charset=utf8mb4", $user, $pass);
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // فحص قواعد البيانات الموجودة
    $stmt = $conn->query("SHOW DATABASES LIKE '%zoom%'");
    $databases = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<div class='step info'>";
    echo "<h6>قواعد البيانات الموجودة:</h6>";
    echo "<ul>";
    foreach ($databases as $db) {
        echo "<li>$db</li>";
    }
    echo "</ul>";
    echo "</div>";
    
    // تحديد قاعدة البيانات المناسبة
    $targetDB = '';
    if (in_array('zoom_learning_system', $databases)) {
        $targetDB = 'zoom_learning_system';
    } elseif (in_array('zoom_db', $databases)) {
        $targetDB = 'zoom_db';
    } else {
        $targetDB = 'zoom_learning_system';
        echo "<div class='step info'>";
        echo "<h6>إنشاء قاعدة بيانات جديدة: $targetDB</h6>";
        echo "</div>";
    }
    
    // الاتصال بقاعدة البيانات المحددة
    $conn->exec("CREATE DATABASE IF NOT EXISTS $targetDB CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    $conn->exec("USE $targetDB");
    
    echo "<div class='step success'>";
    echo "<h5>✅ تم الاتصال بقاعدة البيانات: $targetDB</h5>";
    echo "</div>";
    
    // إنشاء جدول المستخدمين مع الحقول المطلوبة
    $createUsersTable = "
    CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        email VARCHAR(255) NOT NULL UNIQUE,
        username VARCHAR(50) UNIQUE,
        phone VARCHAR(20),
        password VARCHAR(255) NOT NULL,
        role ENUM('admin', 'instructor', 'student') NOT NULL DEFAULT 'student',
        status ENUM('active', 'inactive', 'pending', 'suspended') DEFAULT 'active',
        email_verified BOOLEAN DEFAULT TRUE,
        failed_attempts INT DEFAULT 0,
        lock_expires DATETIME DEFAULT NULL,
        last_login TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $conn->exec($createUsersTable);
    
    echo "<div class='step success'>";
    echo "<h6>✅ تم إنشاء/تحديث جدول المستخدمين</h6>";
    echo "</div>";
    
    // التحقق من وجود المستخدمين التجريبيين
    $stmt = $conn->query("SELECT COUNT(*) FROM users WHERE email IN ('<EMAIL>', '<EMAIL>', '<EMAIL>')");
    $existingUsers = $stmt->fetchColumn();
    
    if ($existingUsers == 0) {
        echo "<div class='step info'>";
        echo "<h6>🔄 إنشاء المستخدمين التجريبيين</h6>";
        echo "</div>";
        
        // كلمة المرور المشفرة لـ "password"
        $hashedPassword = password_hash('password', PASSWORD_DEFAULT);
        
        // إدراج المستخدمين التجريبيين
        $users = [
            ['مدير النظام', '<EMAIL>', 'admin', $hashedPassword, 'admin'],
            ['أحمد محمد - مدرب', '<EMAIL>', 'instructor_demo', $hashedPassword, 'instructor'],
            ['سارة أحمد - طالبة', '<EMAIL>', 'student_demo', $hashedPassword, 'student']
        ];
        
        $insertStmt = $conn->prepare("
            INSERT INTO users (name, email, username, password, role, status, email_verified) 
            VALUES (?, ?, ?, ?, ?, 'active', TRUE)
            ON DUPLICATE KEY UPDATE 
            password = VALUES(password),
            status = 'active',
            email_verified = TRUE
        ");
        
        foreach ($users as $user) {
            $insertStmt->execute($user);
            echo "<div class='step success'>";
            echo "<small>✅ تم إنشاء/تحديث المستخدم: {$user[1]}</small>";
            echo "</div>";
        }
    } else {
        echo "<div class='step info'>";
        echo "<h6>ℹ️ المستخدمون التجريبيون موجودون مسبقاً</h6>";
        echo "</div>";
        
        // تحديث كلمات المرور للتأكد
        $hashedPassword = password_hash('password', PASSWORD_DEFAULT);
        $updateStmt = $conn->prepare("
            UPDATE users 
            SET password = ?, status = 'active', email_verified = TRUE, failed_attempts = 0, lock_expires = NULL
            WHERE email IN ('<EMAIL>', '<EMAIL>', '<EMAIL>')
        ");
        $updateStmt->execute([$hashedPassword]);
        
        echo "<div class='step success'>";
        echo "<h6>✅ تم تحديث كلمات المرور وحالة المستخدمين</h6>";
        echo "</div>";
    }
    
    // التحقق من المستخدمين
    $stmt = $conn->query("SELECT id, name, email, role, status FROM users WHERE email IN ('<EMAIL>', '<EMAIL>', '<EMAIL>')");
    $users = $stmt->fetchAll();
    
    echo "<div class='step success'>";
    echo "<h5>👥 المستخدمون المتاحون:</h5>";
    echo "<table class='table table-striped'>";
    echo "<thead><tr><th>الاسم</th><th>البريد الإلكتروني</th><th>الدور</th><th>الحالة</th></tr></thead>";
    echo "<tbody>";
    foreach ($users as $user) {
        echo "<tr>";
        echo "<td>{$user['name']}</td>";
        echo "<td>{$user['email']}</td>";
        echo "<td>{$user['role']}</td>";
        echo "<td><span class='badge bg-success'>{$user['status']}</span></td>";
        echo "</tr>";
    }
    echo "</tbody></table>";
    echo "</div>";
    
    // اختبار تسجيل الدخول
    echo "<div class='step info'>";
    echo "<h5>🧪 اختبار تسجيل الدخول</h5>";
    echo "</div>";
    
    $testEmail = '<EMAIL>';
    $testPassword = 'password';
    
    $stmt = $conn->prepare("SELECT id, name, email, password, role, status FROM users WHERE email = ?");
    $stmt->execute([$testEmail]);
    $testUser = $stmt->fetch();
    
    if ($testUser && password_verify($testPassword, $testUser['password'])) {
        echo "<div class='step success'>";
        echo "<h6>✅ اختبار تسجيل الدخول نجح!</h6>";
        echo "<p>المستخدم: {$testUser['name']} ({$testUser['role']})</p>";
        echo "</div>";
    } else {
        echo "<div class='step error'>";
        echo "<h6>❌ اختبار تسجيل الدخول فشل!</h6>";
        echo "</div>";
    }
    
    // تحديث ملف database.php
    $configContent = "<?php
// Database Configuration
define('DB_HOST', 'localhost');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_NAME', '$targetDB');

// Error reporting for development (disable in production)
if (defined('DEVELOPMENT_MODE') && DEVELOPMENT_MODE) {
    ini_set('display_errors', 1);
    ini_set('display_startup_errors', 1);
    error_reporting(E_ALL);
}

try {
    \$conn = new PDO(\"mysql:host=\" . DB_HOST . \";dbname=\" . DB_NAME . \";charset=utf8mb4\", DB_USER, DB_PASS);
    \$conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    \$conn->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
    \$conn->setAttribute(PDO::ATTR_EMULATE_PREPARES, false);
} catch(PDOException \$e) {
    die(\"Connection failed: \" . \$e->getMessage());
}
?>";
    
    file_put_contents('config/database.php', $configContent);
    
    echo "<div class='step success'>";
    echo "<h6>✅ تم تحديث ملف config/database.php</h6>";
    echo "</div>";
    
    echo "<div class='step success'>";
    echo "<h5>🎉 تم إصلاح المشكلة بنجاح!</h5>";
    echo "<p><strong>بيانات تسجيل الدخول:</strong></p>";
    echo "<ul>";
    echo "<li><strong>المدير:</strong> <EMAIL> / password</li>";
    echo "<li><strong>المدرب:</strong> <EMAIL> / password</li>";
    echo "<li><strong>الطالب:</strong> <EMAIL> / password</li>";
    echo "</ul>";
    echo "<div class='mt-3'>";
    echo "<a href='login.php' class='btn btn-primary'>تجربة تسجيل الدخول الآن</a>";
    echo "</div>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='step error'>";
    echo "<h5>❌ حدث خطأ</h5>";
    echo "<p><strong>الخطأ:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "</div>";
echo "</body>";
echo "</html>";
?>
