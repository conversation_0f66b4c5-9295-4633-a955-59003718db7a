<?php
require_once 'config/database.php';

echo "<h2>فحص قاعدة البيانات</h2>";

try {
    // التحقق من وجود الجداول
    $tables = ['users', 'courses', 'quizzes', 'quiz_questions', 'quiz_question_options', 'quiz_attempts'];
    
    echo "<h3>الجداول:</h3>";
    foreach ($tables as $table) {
        $stmt = $conn->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            echo "✓ جدول $table موجود<br>";
        } else {
            echo "✗ جدول $table غير موجود<br>";
        }
    }
    
    echo "<h3>الإحصائيات:</h3>";
    
    // التحقق من وجود مدربين
    $stmt = $conn->query("SELECT COUNT(*) as count FROM users WHERE role = 'instructor'");
    $instructors = $stmt->fetch()['count'];
    echo "عدد المدربين: $instructors<br>";
    
    // التحقق من وجود كورسات
    $stmt = $conn->query("SELECT COUNT(*) as count FROM courses");
    $courses = $stmt->fetch()['count'];
    echo "عدد الكورسات: $courses<br>";
    
    // التحقق من وجود اختبارات
    $stmt = $conn->query("SELECT COUNT(*) as count FROM quizzes");
    $quizzes = $stmt->fetch()['count'];
    echo "عدد الاختبارات: $quizzes<br>";
    
    // عرض بعض البيانات التجريبية
    echo "<h3>المدربين:</h3>";
    $stmt = $conn->query("SELECT id, name, email FROM users WHERE role = 'instructor' LIMIT 5");
    while ($row = $stmt->fetch()) {
        echo "ID: {$row['id']}, الاسم: {$row['name']}, البريد: {$row['email']}<br>";
    }
    
    echo "<h3>الكورسات:</h3>";
    $stmt = $conn->query("SELECT id, title, instructor_id FROM courses LIMIT 5");
    while ($row = $stmt->fetch()) {
        echo "ID: {$row['id']}, العنوان: {$row['title']}, المدرب: {$row['instructor_id']}<br>";
    }
    
    // التحقق من بنية جدول quizzes
    echo "<h3>بنية جدول quizzes:</h3>";
    $stmt = $conn->query("DESCRIBE quizzes");
    while ($row = $stmt->fetch()) {
        echo "{$row['Field']} - {$row['Type']}<br>";
    }
    
} catch (Exception $e) {
    echo "خطأ: " . $e->getMessage();
}
?>
