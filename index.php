<?php
require_once 'config/database.php';
require_once 'includes/functions.php';

// جلب الكورسات المتاحة
try {
    $stmt = $conn->prepare("
        SELECT c.*, u.name as instructor_name, u.email as instructor_email,
               (SELECT COUNT(*) FROM course_enrollments WHERE course_id = c.id AND status = 'active') as enrolled_students,
               (SELECT COUNT(*) FROM sessions WHERE course_id = c.id) as total_sessions
        FROM courses c
        JOIN users u ON c.instructor_id = u.id
        WHERE c.status = 'active'
        ORDER BY c.created_at DESC
        LIMIT 12
    ");
    $stmt->execute();
    $courses = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // جلب التخصصات المتاحة
    $stmt = $conn->query("SELECT DISTINCT category FROM courses WHERE status = 'active' AND category IS NOT NULL ORDER BY category");
    $categories = $stmt->fetchAll(PDO::FETCH_COLUMN);

    // إحصائيات الموقع
    $stmt = $conn->query("SELECT COUNT(*) FROM courses WHERE status = 'active'");
    $total_courses = $stmt->fetchColumn();

    $stmt = $conn->query("SELECT COUNT(*) FROM users WHERE role = 'instructor'");
    $total_instructors = $stmt->fetchColumn();

    $stmt = $conn->query("SELECT COUNT(*) FROM users WHERE role = 'student'");
    $total_students = $stmt->fetchColumn();

    $stmt = $conn->query("SELECT COUNT(*) FROM course_enrollments WHERE status = 'active'");
    $total_enrollments = $stmt->fetchColumn();

} catch (PDOException $e) {
    $courses = [];
    $categories = [];
    $total_courses = $total_instructors = $total_students = $total_enrollments = 0;
}

$pageTitle = 'منصة التعلم الإلكتروني';
?>

<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="منصة التعلم الإلكتروني الرائدة - تعلم مهارات جديدة مع أفضل المدربين">
    <meta name="keywords" content="تعلم, كورسات, تدريب, مهارات, تعليم إلكتروني">
    <meta name="author" content="منصة التعلم">
    <meta property="og:title" content="<?php echo $pageTitle; ?>">
    <meta property="og:description" content="منصة التعلم الإلكتروني الرائدة">
    <meta property="og:type" content="website">
    <title><?php echo $pageTitle; ?></title>

    <!-- الخطوط المحسنة -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Bootstrap و Font Awesome -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- AOS Animation Library -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">

    <!-- ملفات CSS المخصصة -->
    <link href="assets/css/main.css" rel="stylesheet">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="assets/images/favicon.ico">
    <style>
        :root {
            --primary-color: #667eea;
            --primary-dark: #5a67d8;
            --secondary-color: #764ba2;
            --success-color: #48bb78;
            --warning-color: #ed8936;
            --danger-color: #f56565;
            --info-color: #4299e1;
            --light-color: #f7fafc;
            --dark-color: #2d3748;
            --border-radius: 12px;
            --shadow-sm: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
            --shadow-md: 0 4px 6px rgba(0,0,0,0.07), 0 1px 3px rgba(0,0,0,0.1);
            --shadow-lg: 0 10px 15px rgba(0,0,0,0.1), 0 4px 6px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            line-height: 1.6;
            color: var(--dark-color);
            background-color: var(--light-color);
            overflow-x: hidden;
        }

        .hero-section {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            padding: 120px 0 80px;
            position: relative;
            overflow: hidden;
            min-height: 100vh;
            display: flex;
            align-items: center;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 80%, rgba(255,255,255,0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255,255,255,0.1) 0%, transparent 50%);
        }

        .hero-section::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 100px;
            background: linear-gradient(to right bottom, transparent 49%, var(--light-color) 50%);
        }

        .hero-content {
            position: relative;
            z-index: 2;
        }

        .hero-animation {
            animation: fadeInUp 1s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .feature-card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border: none;
            border-radius: 15px;
            overflow: hidden;
        }

        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }

        .course-card {
            transition: all 0.3s ease;
            border: none;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }

        .course-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }

        .course-image {
            height: 200px;
            background: linear-gradient(45deg, #f093fb 0%, #f5576c 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 3rem;
        }

        .stats-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 80px 0;
        }

        .stat-item {
            text-align: center;
            padding: 20px;
        }

        .stat-number {
            font-size: 3rem;
            font-weight: bold;
            display: block;
        }

        .category-filter {
            margin-bottom: 30px;
        }

        .category-btn {
            margin: 5px;
            border-radius: 25px;
            padding: 8px 20px;
            border: 2px solid #667eea;
            background: transparent;
            color: #667eea;
            transition: all 0.3s ease;
        }

        .category-btn:hover,
        .category-btn.active {
            background: #667eea;
            color: white;
        }

        .navbar-brand {
            font-weight: bold;
            font-size: 1.5rem;
        }

        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            font-weight: 600;
        }

        .btn-outline-primary {
            border: 2px solid #667eea;
            color: #667eea;
            border-radius: 25px;
            padding: 12px 30px;
            font-weight: 600;
        }

        .btn-outline-primary:hover {
            background: #667eea;
            border-color: #667eea;
        }

        .section-title {
            text-align: center;
            margin-bottom: 50px;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 4px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .footer {
            background: #2c3e50;
            color: white;
            padding: 50px 0 20px;
        }

        .price-badge {
            position: absolute;
            top: 15px;
            right: 15px;
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: bold;
        }

        .price-badge.paid {
            background: linear-gradient(45deg, #ffc107, #fd7e14);
        }
    </style>
</head>
<body>
    <!-- شريط التنقل المحسن -->
    <nav class="navbar navbar-expand-lg navbar-enhanced" id="mainNavbar">
        <div class="container">
            <a class="navbar-brand-enhanced" href="index.php">
                <div class="logo-icon">
                    <i class="fas fa-graduation-cap"></i>
                </div>
                <span>منصة التعلم</span>
            </a>

            <button class="navbar-toggler border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav mx-auto">
                    <li class="nav-item">
                        <a class="nav-link fw-medium" href="#home">الرئيسية</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link fw-medium" href="#courses">الكورسات</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link fw-medium" href="#services">الخدمات</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link fw-medium" href="#about">من نحن</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link fw-medium" href="#contact">اتصل بنا</a>
                    </li>
                </ul>

                <div class="d-flex gap-2">
                    <a href="login.php" class="btn-enhanced btn-secondary-enhanced">
                        <i class="fas fa-sign-in-alt"></i>
                        <span>تسجيل الدخول</span>
                    </a>
                    <a href="join-request.php" class="btn-enhanced btn-success-enhanced">
                        <i class="fas fa-user-plus"></i>
                        <span>طلب انضمام</span>
                    </a>
                    <a href="register.php" class="btn-enhanced btn-primary-enhanced">
                        <i class="fas fa-user-plus"></i>
                        <span>إنشاء حساب</span>
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- القسم الرئيسي المحسن -->
    <section id="home" class="hero-section particles">
        <div class="container">
            <div class="row align-items-center min-vh-100">
                <div class="col-lg-6" data-aos="fade-right" data-aos-duration="1000">
                    <div class="hero-content-enhanced">
                        <h1 class="hero-title text-gradient animate-fade-in-up">
                            🚀 تعلم مهارات المستقبل مع أفضل المدربين
                        </h1>
                        <p class="hero-subtitle animate-fade-in-up" style="animation-delay: 0.2s;">
                            انضم إلى آلاف الطلاب واكتسب مهارات جديدة في مختلف المجالات مع مدربين محترفين ومحتوى تعليمي متميز. ابدأ رحلتك التعليمية اليوم وحقق أهدافك المهنية.
                        </p>
                        <div class="hero-buttons animate-fade-in-up" style="animation-delay: 0.4s;">
                            <a href="#courses" class="btn-enhanced btn-lg-enhanced" style="background: rgba(255,255,255,0.2); color: white; border: 2px solid rgba(255,255,255,0.3);">
                                <i class="fas fa-book"></i>
                                <span>تصفح الكورسات</span>
                            </a>
                            <a href="register.php" class="btn-enhanced btn-lg-enhanced" style="background: rgba(255,255,255,0.9); color: var(--primary-color); border: 2px solid white;">
                                <i class="fas fa-rocket"></i>
                                <span>ابدأ التعلم الآن</span>
                            </a>
                        </div>

                        <!-- إحصائيات سريعة -->
                        <div class="row mt-5 animate-fade-in-up" style="animation-delay: 0.6s;">
                            <div class="col-4 text-center">
                                <div class="text-white">
                                    <h3 class="fw-bold mb-1"><?php echo $total_courses; ?>+</h3>
                                    <small class="opacity-75">كورس</small>
                                </div>
                            </div>
                            <div class="col-4 text-center">
                                <div class="text-white">
                                    <h3 class="fw-bold mb-1"><?php echo $total_instructors; ?>+</h3>
                                    <small class="opacity-75">مدرب</small>
                                </div>
                            </div>
                            <div class="col-4 text-center">
                                <div class="text-white">
                                    <h3 class="fw-bold mb-1"><?php echo $total_students; ?>+</h3>
                                    <small class="opacity-75">طالب</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6" data-aos="fade-left" data-aos-duration="1000" data-aos-delay="200">
                    <div class="hero-image text-center">
                        <div class="position-relative">
                            <i class="fas fa-laptop-code hero-main-icon animate-float" style="font-size: clamp(8rem, 15vw, 15rem); opacity: 0.8; color: rgba(255,255,255,0.9);"></i>

                            <!-- عناصر تفاعلية إضافية -->
                            <div class="position-absolute" style="top: 20%; right: 10%; animation: float 4s ease-in-out infinite;">
                                <i class="fas fa-graduation-cap text-white" style="font-size: 3rem; opacity: 0.6;"></i>
                            </div>
                            <div class="position-absolute" style="bottom: 30%; left: 15%; animation: float 5s ease-in-out infinite reverse;">
                                <i class="fas fa-book text-white" style="font-size: 2.5rem; opacity: 0.5;"></i>
                            </div>
                            <div class="position-absolute" style="top: 60%; right: 20%; animation: float 3s ease-in-out infinite;">
                                <i class="fas fa-lightbulb text-white" style="font-size: 2rem; opacity: 0.4;"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- إحصائيات الموقع المحسنة -->
    <section class="stats-section-enhanced">
        <div class="container">
            <div class="row g-4">
                <div class="col-lg-3 col-md-6" data-aos="fade-up" data-aos-delay="100">
                    <div class="stat-card hover-lift">
                        <i class="stat-icon fas fa-book-open"></i>
                        <span class="stat-number counter" data-target="<?php echo $total_courses; ?>">0</span>
                        <div class="stat-label">كورس متاح</div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6" data-aos="fade-up" data-aos-delay="200">
                    <div class="stat-card hover-lift">
                        <i class="stat-icon fas fa-chalkboard-teacher"></i>
                        <span class="stat-number counter" data-target="<?php echo $total_instructors; ?>">0</span>
                        <div class="stat-label">مدرب محترف</div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6" data-aos="fade-up" data-aos-delay="300">
                    <div class="stat-card hover-lift">
                        <i class="stat-icon fas fa-user-graduate"></i>
                        <span class="stat-number counter" data-target="<?php echo $total_students; ?>">0</span>
                        <div class="stat-label">طالب مسجل</div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6" data-aos="fade-up" data-aos-delay="400">
                    <div class="stat-card hover-lift">
                        <i class="stat-icon fas fa-certificate"></i>
                        <span class="stat-number counter" data-target="<?php echo $total_enrollments; ?>">0</span>
                        <div class="stat-label">تسجيل في الكورسات</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- الخدمات المحسنة -->
    <section id="services" class="section-enhanced bg-light">
        <div class="container">
            <div class="section-title-enhanced" data-aos="fade-up">
                <h2>خدماتنا المتميزة</h2>
                <p>نقدم مجموعة شاملة من الخدمات التعليمية المتطورة لضمان أفضل تجربة تعلم</p>
            </div>

            <div class="row g-4">
                <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="100">
                    <div class="feature-card-enhanced">
                        <div class="feature-icon">
                            <i class="fas fa-video"></i>
                        </div>
                        <h5 class="feature-title">جلسات مباشرة تفاعلية</h5>
                        <p class="feature-description">تفاعل مباشر مع المدربين من خلال جلسات فيديو عالية الجودة مع إمكانية المشاركة والنقاش</p>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="200">
                    <div class="feature-card-enhanced">
                        <div class="feature-icon">
                            <i class="fas fa-certificate"></i>
                        </div>
                        <h5 class="feature-title">شهادات معتمدة</h5>
                        <p class="feature-description">احصل على شهادات معتمدة ومعترف بها دولياً عند إتمام الكورسات بنجاح</p>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="300">
                    <div class="feature-card-enhanced">
                        <div class="feature-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <h5 class="feature-title">مجتمع تعليمي نشط</h5>
                        <p class="feature-description">انضم لمجتمع من المتعلمين وتبادل الخبرات والمعرفة مع زملائك</p>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="400">
                    <div class="feature-card-enhanced">
                        <div class="feature-icon">
                            <i class="fas fa-mobile-alt"></i>
                        </div>
                        <h5 class="feature-title">تعلم في أي مكان</h5>
                        <p class="feature-description">منصة متجاوبة تعمل على جميع الأجهزة والهواتف الذكية مع تطبيق جوال</p>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="500">
                    <div class="feature-card-enhanced">
                        <div class="feature-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <h5 class="feature-title">مرونة كاملة في الوقت</h5>
                        <p class="feature-description">تعلم في الوقت المناسب لك مع إمكانية مراجعة المحتوى وتحميل المواد</p>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="600">
                    <div class="feature-card-enhanced">
                        <div class="feature-icon">
                            <i class="fas fa-headset"></i>
                        </div>
                        <h5 class="feature-title">دعم فني متقدم 24/7</h5>
                        <p class="feature-description">فريق دعم متخصص متاح على مدار الساعة لمساعدتك في رحلتك التعليمية</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- الكورسات المتاحة المحسنة -->
    <section id="courses" class="section-enhanced">
        <div class="container">
            <div class="section-title-enhanced" data-aos="fade-up">
                <h2>الكورسات المتاحة</h2>
                <p>اختر من مجموعة واسعة من الكورسات المتخصصة في مختلف المجالات</p>
            </div>

            <!-- فلتر التخصصات المحسن -->
            <div class="text-center mb-5" data-aos="fade-up" data-aos-delay="200">
                <div class="d-flex flex-wrap justify-content-center gap-2">
                    <button class="btn-enhanced btn-primary-enhanced category-btn active" data-category="all">
                        <i class="fas fa-th-large"></i>
                        <span>جميع التخصصات</span>
                    </button>
                    <?php foreach ($categories as $category): ?>
                        <button class="btn-enhanced btn-secondary-enhanced category-btn" data-category="<?php echo htmlspecialchars($category); ?>">
                            <i class="fas fa-tag"></i>
                            <span><?php echo htmlspecialchars($category); ?></span>
                        </button>
                    <?php endforeach; ?>
                </div>
            </div>

            <div class="row g-4" id="coursesContainer">
                <?php if (empty($courses)): ?>
                    <div class="col-12 text-center" data-aos="fade-up">
                        <div class="card-modern p-5">
                            <i class="fas fa-info-circle fa-3x text-primary mb-3"></i>
                            <h4>لا توجد كورسات متاحة حالياً</h4>
                            <p class="text-muted">سيتم إضافة كورسات جديدة قريباً</p>
                        </div>
                    </div>
                <?php else: ?>
                    <?php foreach ($courses as $index => $course): ?>
                        <div class="col-lg-4 col-md-6 course-item" data-category="<?php echo htmlspecialchars($course['category'] ?? 'عام'); ?>" data-aos="fade-up" data-aos-delay="<?php echo ($index % 3) * 100; ?>">
                            <div class="course-card">
                                <div class="course-card-image">
                                    <?php if ($course['image_path']): ?>
                                        <img src="<?php echo htmlspecialchars($course['image_path']); ?>"
                                             alt="<?php echo htmlspecialchars($course['title']); ?>">
                                    <?php else: ?>
                                        <div style="height: 200px; background: linear-gradient(45deg, var(--primary-color), var(--secondary-color)); display: flex; align-items: center; justify-content: center;">
                                            <i class="fas fa-graduation-cap text-white" style="font-size: 4rem; opacity: 0.8;"></i>
                                        </div>
                                    <?php endif; ?>

                                    <div class="course-card-overlay">
                                        <div class="course-card-play">
                                            <i class="fas fa-play"></i>
                                        </div>
                                    </div>

                                    <div class="price-badge <?php echo $course['course_type'] === 'paid' ? 'paid' : 'free'; ?>">
                                        <?php if ($course['course_type'] === 'paid'): ?>
                                            <?php echo number_format($course['price'], 0); ?> <?php echo $course['currency']; ?>
                                        <?php else: ?>
                                            مجاني
                                        <?php endif; ?>
                                    </div>
                                </div>

                                <div class="course-card-content">
                                    <h5 class="course-card-title"><?php echo htmlspecialchars($course['title']); ?></h5>
                                    <p class="course-card-description">
                                        <?php echo htmlspecialchars(substr($course['description'], 0, 120)) . '...'; ?>
                                    </p>

                                    <div class="course-card-meta">
                                        <div class="course-card-instructor">
                                            <i class="fas fa-user"></i>
                                            <span><?php echo htmlspecialchars($course['instructor_name']); ?></span>
                                        </div>
                                        <div class="course-card-stats">
                                            <span><i class="fas fa-users"></i> <?php echo $course['enrolled_students']; ?></span>
                                            <span><i class="fas fa-video"></i> <?php echo $course['total_sessions']; ?></span>
                                        </div>
                                    </div>

                                    <?php if ($course['course_type'] === 'paid'): ?>
                                        <div class="course-card-price">
                                            <?php echo number_format($course['price'], 0); ?> <?php echo $course['currency']; ?>
                                        </div>
                                    <?php else: ?>
                                        <div class="course-card-price free">
                                            مجاني
                                        </div>
                                    <?php endif; ?>
                                </div>

                                <div class="course-card-footer">
                                    <a href="course-details.php?id=<?php echo $course['id']; ?>" class="btn-enhanced btn-primary-enhanced w-100">
                                        <i class="fas fa-eye"></i>
                                        <span>عرض التفاصيل</span>
                                    </a>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>

            <div class="text-center mt-4">
                <a href="courses.php" class="btn btn-outline-primary btn-lg">
                    <i class="fas fa-th-large me-2"></i>
                    عرض جميع الكورسات
                </a>
            </div>
        </div>
    </section>

    <!-- من نحن -->
    <section id="about" class="py-5">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <h2 class="section-title text-start">من نحن</h2>
                    <p class="lead">
                        نحن منصة تعليمية رائدة تهدف إلى توفير تعليم عالي الجودة ومتاح للجميع. نؤمن بأن التعلم حق للجميع ونسعى لجعل المعرفة في متناول كل شخص.
                    </p>
                    <p>
                        مع فريق من أفضل المدربين والخبراء في مختلف المجالات، نقدم كورسات شاملة ومحدثة تواكب أحدث التطورات في السوق.
                    </p>
                    <div class="row mt-4">
                        <div class="col-6">
                            <div class="text-center">
                                <h4 class="text-primary">5+</h4>
                                <p class="mb-0">سنوات خبرة</p>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center">
                                <h4 class="text-success">98%</h4>
                                <p class="mb-0">نسبة رضا الطلاب</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="text-center">
                        <i class="fas fa-chalkboard-teacher" style="font-size: 10rem; color: #667eea; opacity: 0.7;"></i>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- اتصل بنا -->
    <section id="contact" class="py-5 bg-light">
        <div class="container">
            <h2 class="section-title">اتصل بنا</h2>
            <div class="row">
                <div class="col-lg-8 mx-auto">
                    <div class="row">
                        <div class="col-md-4 text-center mb-4">
                            <div class="card feature-card h-100">
                                <div class="card-body">
                                    <i class="fas fa-envelope fa-2x text-primary mb-3"></i>
                                    <h6>البريد الإلكتروني</h6>
                                    <p class="mb-0"><EMAIL></p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 text-center mb-4">
                            <div class="card feature-card h-100">
                                <div class="card-body">
                                    <i class="fas fa-phone fa-2x text-success mb-3"></i>
                                    <h6>الهاتف</h6>
                                    <p class="mb-0">+966 50 123 4567</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 text-center mb-4">
                            <div class="card feature-card h-100">
                                <div class="card-body">
                                    <i class="fas fa-map-marker-alt fa-2x text-info mb-3"></i>
                                    <h6>العنوان</h6>
                                    <p class="mb-0">الرياض، المملكة العربية السعودية</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- الفوتر -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 mb-4">
                    <h5>
                        <i class="fas fa-graduation-cap me-2"></i>
                        منصة التعلم
                    </h5>
                    <p>منصة تعليمية شاملة تهدف إلى توفير أفضل تجربة تعليمية للطلاب والمدربين.</p>
                    <div class="d-flex gap-3">
                        <a href="#" class="text-white"><i class="fab fa-facebook fa-lg"></i></a>
                        <a href="#" class="text-white"><i class="fab fa-twitter fa-lg"></i></a>
                        <a href="#" class="text-white"><i class="fab fa-linkedin fa-lg"></i></a>
                        <a href="#" class="text-white"><i class="fab fa-youtube fa-lg"></i></a>
                    </div>
                </div>
                <div class="col-lg-2 col-md-6 mb-4">
                    <h6>روابط سريعة</h6>
                    <ul class="list-unstyled">
                        <li><a href="#home" class="text-white-50">الرئيسية</a></li>
                        <li><a href="#courses" class="text-white-50">الكورسات</a></li>
                        <li><a href="#services" class="text-white-50">الخدمات</a></li>
                        <li><a href="#about" class="text-white-50">من نحن</a></li>
                    </ul>
                </div>
                <div class="col-lg-2 col-md-6 mb-4">
                    <h6>الحساب</h6>
                    <ul class="list-unstyled">
                        <li><a href="login.php" class="text-white-50">تسجيل الدخول</a></li>
                        <li><a href="join-request.php" class="text-white-50">طلب انضمام</a></li>
                        <li><a href="check-request-status.php" class="text-white-50">فحص حالة الطلب</a></li>
                        <li><a href="register.php" class="text-white-50">إنشاء حساب</a></li>
                        <li><a href="student/dashboard.php" class="text-white-50">لوحة الطالب</a></li>
                        <li><a href="instructor/dashboard.php" class="text-white-50">لوحة المدرب</a></li>
                    </ul>
                </div>
                <div class="col-lg-4 mb-4">
                    <h6>اشترك في النشرة الإخبارية</h6>
                    <p>احصل على آخر الأخبار والعروض</p>
                    <div class="input-group">
                        <input type="email" class="form-control" placeholder="بريدك الإلكتروني">
                        <button class="btn btn-primary" type="button">اشتراك</button>
                    </div>
                </div>
            </div>
            <hr class="my-4">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0">&copy; 2024 منصة التعلم. جميع الحقوق محفوظة.</p>
                </div>
                <div class="col-md-6 text-end">
                    <a href="#" class="text-white-50 me-3">سياسة الخصوصية</a>
                    <a href="#" class="text-white-50">شروط الاستخدام</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- شريط التقدم -->
    <div class="scroll-indicator">
        <div class="scroll-progress"></div>
    </div>

    <!-- زر العودة للأعلى -->
    <button class="back-to-top" id="backToTop" aria-label="العودة للأعلى">
        <i class="fas fa-arrow-up"></i>
    </button>

    <!-- JavaScript Libraries -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script src="assets/js/enhanced.js"></script>

    <script>
        // تهيئة AOS
        AOS.init({
            duration: 800,
            easing: 'ease-in-out',
            once: true,
            mirror: false
        });

        document.addEventListener('DOMContentLoaded', function() {
            // تأثير شريط التنقل عند التمرير
            const navbar = document.getElementById('mainNavbar');
            window.addEventListener('scroll', function() {
                if (window.scrollY > 100) {
                    navbar.classList.add('scrolled');
                } else {
                    navbar.classList.remove('scrolled');
                }
            });

            // عداد الأرقام المتحرك
            const counters = document.querySelectorAll('.counter');
            const animateCounters = () => {
                counters.forEach(counter => {
                    const target = parseInt(counter.getAttribute('data-target'));
                    const count = parseInt(counter.innerText);
                    const increment = target / 100;

                    if (count < target) {
                        counter.innerText = Math.ceil(count + increment);
                        setTimeout(animateCounters, 20);
                    } else {
                        counter.innerText = target;
                    }
                });
            };

            // تشغيل العداد عند الوصول للقسم
            const statsSection = document.querySelector('.stats-section-enhanced');
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        animateCounters();
                        observer.unobserve(entry.target);
                    }
                });
            });
            if (statsSection) observer.observe(statsSection);

            // فلتر الكورسات المحسن
            const categoryButtons = document.querySelectorAll('.category-btn');
            const courseItems = document.querySelectorAll('.course-item');

            categoryButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // إزالة الفئة النشطة من جميع الأزرار
                    categoryButtons.forEach(btn => {
                        btn.classList.remove('active');
                        btn.classList.remove('btn-primary-enhanced');
                        btn.classList.add('btn-secondary-enhanced');
                    });

                    // إضافة الفئة النشطة للزر المضغوط
                    this.classList.add('active');
                    this.classList.remove('btn-secondary-enhanced');
                    this.classList.add('btn-primary-enhanced');

                    const selectedCategory = this.getAttribute('data-category');

                    courseItems.forEach((item, index) => {
                        if (selectedCategory === 'all' || item.getAttribute('data-category') === selectedCategory) {
                            item.style.display = 'block';
                            item.style.animation = `fadeInUp 0.6s ease-out ${index * 0.1}s both`;
                        } else {
                            item.style.display = 'none';
                        }
                    });
                });
            });

            // تأثير التمرير السلس المحسن
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        const offsetTop = target.offsetTop - 80;
                        window.scrollTo({
                            top: offsetTop,
                            behavior: 'smooth'
                        });
                    }
                });
            });

            // تأثير الموجة للأزرار
            document.querySelectorAll('.btn-enhanced').forEach(button => {
                button.addEventListener('click', function(e) {
                    const ripple = document.createElement('span');
                    const rect = this.getBoundingClientRect();
                    const size = Math.max(rect.width, rect.height);
                    const x = e.clientX - rect.left - size / 2;
                    const y = e.clientY - rect.top - size / 2;

                    ripple.style.width = ripple.style.height = size + 'px';
                    ripple.style.left = x + 'px';
                    ripple.style.top = y + 'px';
                    ripple.classList.add('ripple-effect');

                    this.appendChild(ripple);

                    setTimeout(() => {
                        ripple.remove();
                    }, 600);
                });
            });

            // تحسين الأداء للرسوم المتحركة
            const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)');
            if (prefersReducedMotion.matches) {
                document.documentElement.style.setProperty('--transition-fast', '0s');
                document.documentElement.style.setProperty('--transition-normal', '0s');
                document.documentElement.style.setProperty('--transition-slow', '0s');
            }

            // تحسين تجربة المستخدم للوحة المفاتيح
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Tab') {
                    document.body.classList.add('keyboard-navigation');
                }
            });

            document.addEventListener('mousedown', function() {
                document.body.classList.remove('keyboard-navigation');
            });

            // تحميل الصور بشكل تدريجي
            const images = document.querySelectorAll('img[data-src]');
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        imageObserver.unobserve(img);
                    }
                });
            });

            images.forEach(img => imageObserver.observe(img));
        });

        // إضافة CSS للتأثيرات
        const style = document.createElement('style');
        style.textContent = `
            .ripple-effect {
                position: absolute;
                border-radius: 50%;
                background: rgba(255, 255, 255, 0.6);
                transform: scale(0);
                animation: ripple 0.6s linear;
                pointer-events: none;
            }

            @keyframes ripple {
                to {
                    transform: scale(4);
                    opacity: 0;
                }
            }

            .keyboard-navigation *:focus {
                outline: 2px solid var(--primary-color) !important;
                outline-offset: 2px !important;
            }

            .lazy {
                opacity: 0;
                transition: opacity 0.3s;
            }

            .lazy.loaded {
                opacity: 1;
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
