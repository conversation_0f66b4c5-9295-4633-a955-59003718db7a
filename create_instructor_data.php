<?php
/**
 * إنشاء بيانات تجريبية للمدرب
 * Create sample data for instructor
 */

require_once 'config/database.php';

echo "<!DOCTYPE html>";
echo "<html dir='rtl' lang='ar'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>إنشاء بيانات تجريبية للمدرب</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "</head>";
echo "<body>";
echo "<div class='container mt-4'>";
echo "<h2>📚 إنشاء بيانات تجريبية للمدرب</h2>";

try {
    // 1. التحقق من وجود المدرب
    echo "<h4>👨‍🏫 التحقق من المدرب</h4>";
    
    $stmt = $conn->prepare("SELECT * FROM users WHERE id = 2 AND role = 'instructor'");
    $stmt->execute();
    $instructor = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$instructor) {
        echo "<div class='alert alert-danger'>❌ المدرب غير موجود</div>";
        exit;
    }
    
    echo "<div class='alert alert-success'>✅ المدرب موجود: " . htmlspecialchars($instructor['name']) . "</div>";

    // 2. إنشاء كورسات للمدرب
    echo "<h4>📚 إنشاء كورسات</h4>";
    
    $courses_data = [
        ['أساسيات البرمجة', 'تعلم أساسيات البرمجة باستخدام لغة Python', '2025-01-01', '2025-06-30'],
        ['تطوير المواقع', 'تعلم تطوير المواقع باستخدام HTML, CSS, JavaScript', '2025-02-01', '2025-07-31'],
        ['قواعد البيانات', 'تعلم تصميم وإدارة قواعد البيانات', '2025-03-01', '2025-08-31']
    ];
    
    $course_ids = [];
    
    foreach ($courses_data as $course_data) {
        // التحقق من وجود الكورس
        $stmt = $conn->prepare("SELECT id FROM courses WHERE title = ? AND instructor_id = ?");
        $stmt->execute([$course_data[0], $instructor['id']]);
        $existing_course = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$existing_course) {
            $stmt = $conn->prepare("INSERT INTO courses (title, description, instructor_id, start_date, end_date, status, created_at) VALUES (?, ?, ?, ?, ?, 'active', NOW())");
            $stmt->execute([$course_data[0], $course_data[1], $instructor['id'], $course_data[2], $course_data[3]]);
            $course_ids[] = $conn->lastInsertId();
            echo "<div class='alert alert-success'>✅ تم إنشاء كورس: {$course_data[0]}</div>";
        } else {
            $course_ids[] = $existing_course['id'];
            echo "<div class='alert alert-info'>ℹ️ كورس موجود: {$course_data[0]}</div>";
        }
    }

    // 3. إنشاء طلاب
    echo "<h4>👥 إنشاء طلاب</h4>";
    
    $students_data = [
        ['أحمد محمد', '<EMAIL>', '0501234567'],
        ['فاطمة علي', '<EMAIL>', '0507654321'],
        ['محمد خالد', '<EMAIL>', '0509876543'],
        ['سارة أحمد', '<EMAIL>', '0502468135'],
        ['علي حسن', '<EMAIL>', '0508642097']
    ];
    
    $student_ids = [];
    
    foreach ($students_data as $student_data) {
        // التحقق من وجود الطالب
        $stmt = $conn->prepare("SELECT id FROM users WHERE email = ?");
        $stmt->execute([$student_data[1]]);
        $existing_student = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$existing_student) {
            $password = password_hash('student123', PASSWORD_DEFAULT);
            $stmt = $conn->prepare("INSERT INTO users (name, email, password, role, phone, status, created_at) VALUES (?, ?, ?, 'student', ?, 'active', NOW())");
            $stmt->execute([$student_data[0], $student_data[1], $password, $student_data[2]]);
            $student_ids[] = $conn->lastInsertId();
            echo "<div class='alert alert-success'>✅ تم إنشاء طالب: {$student_data[0]}</div>";
        } else {
            $student_ids[] = $existing_student['id'];
            echo "<div class='alert alert-info'>ℹ️ طالب موجود: {$student_data[0]}</div>";
        }
    }

    // 4. تسجيل الطلاب في الكورسات
    echo "<h4>📝 تسجيل الطلاب في الكورسات</h4>";
    
    foreach ($course_ids as $course_id) {
        foreach ($student_ids as $student_id) {
            // التحقق من التسجيل الموجود
            $stmt = $conn->prepare("SELECT id FROM course_enrollments WHERE course_id = ? AND student_id = ?");
            $stmt->execute([$course_id, $student_id]);
            
            if (!$stmt->fetch()) {
                $stmt = $conn->prepare("INSERT INTO course_enrollments (course_id, student_id, enrollment_date, status) VALUES (?, ?, NOW(), 'active')");
                $stmt->execute([$course_id, $student_id]);
                echo "<div class='alert alert-success'>✅ تم تسجيل طالب في كورس</div>";
            }
        }
    }

    // 5. إنشاء جلسات
    echo "<h4>🎥 إنشاء جلسات</h4>";
    
    $sessions_data = [
        ['مقدمة في البرمجة', 'جلسة تعريفية بأساسيات البرمجة', '2025-06-10', '10:00:00', '11:30:00'],
        ['المتغيرات والثوابت', 'تعلم استخدام المتغيرات في البرمجة', '2025-06-12', '10:00:00', '11:30:00'],
        ['الحلقات والشروط', 'تعلم استخدام الحلقات والشروط', '2025-06-14', '10:00:00', '11:30:00'],
        ['الدوال', 'تعلم إنشاء واستخدام الدوال', '2025-06-16', '10:00:00', '11:30:00'],
        ['المشروع النهائي', 'تطبيق عملي على ما تم تعلمه', '2025-06-18', '10:00:00', '12:00:00']
    ];
    
    foreach ($course_ids as $course_id) {
        foreach ($sessions_data as $session_data) {
            // التحقق من وجود الجلسة
            $stmt = $conn->prepare("SELECT id FROM sessions WHERE title = ? AND course_id = ?");
            $stmt->execute([$session_data[0], $course_id]);
            
            if (!$stmt->fetch()) {
                $stmt = $conn->prepare("INSERT INTO sessions (course_id, title, description, session_date, start_time, end_time, status, created_at) VALUES (?, ?, ?, ?, ?, ?, 'scheduled', NOW())");
                $stmt->execute([$course_id, $session_data[0], $session_data[1], $session_data[2], $session_data[3], $session_data[4]]);
                echo "<div class='alert alert-success'>✅ تم إنشاء جلسة: {$session_data[0]}</div>";
            }
        }
    }

    // 6. إنشاء درجات تجريبية
    echo "<h4>📊 إنشاء درجات تجريبية</h4>";
    
    $assignments = ['واجب 1', 'واجب 2', 'امتحان منتصف الفصل', 'مشروع نهائي'];
    
    foreach ($course_ids as $course_id) {
        foreach ($student_ids as $student_id) {
            foreach ($assignments as $assignment) {
                // التحقق من وجود الدرجة
                $stmt = $conn->prepare("SELECT id FROM student_grades WHERE course_id = ? AND student_id = ? AND assignment_name = ?");
                $stmt->execute([$course_id, $student_id, $assignment]);
                
                if (!$stmt->fetch()) {
                    $grade = rand(70, 100); // درجة عشوائية بين 70 و 100
                    $max_grade = 100;
                    
                    $stmt = $conn->prepare("INSERT INTO student_grades (course_id, student_id, assignment_name, grade, max_grade, graded_by, grade_date) VALUES (?, ?, ?, ?, ?, ?, NOW())");
                    $stmt->execute([$course_id, $student_id, $assignment, $grade, $max_grade, $instructor['id']]);
                    echo "<div class='alert alert-success'>✅ تم إضافة درجة: $assignment - $grade/$max_grade</div>";
                }
            }
        }
    }

    // 7. عرض الإحصائيات النهائية
    echo "<h4>📊 الإحصائيات النهائية</h4>";
    
    // إحصائيات الكورسات
    $stmt = $conn->prepare("SELECT COUNT(*) FROM courses WHERE instructor_id = ?");
    $stmt->execute([$instructor['id']]);
    $courses_count = $stmt->fetchColumn();
    
    // إحصائيات الطلاب
    $stmt = $conn->prepare("SELECT COUNT(DISTINCT ce.student_id) FROM course_enrollments ce JOIN courses c ON ce.course_id = c.id WHERE c.instructor_id = ?");
    $stmt->execute([$instructor['id']]);
    $students_count = $stmt->fetchColumn();
    
    // إحصائيات الجلسات
    $stmt = $conn->prepare("SELECT COUNT(*) FROM sessions s JOIN courses c ON s.course_id = c.id WHERE c.instructor_id = ?");
    $stmt->execute([$instructor['id']]);
    $sessions_count = $stmt->fetchColumn();
    
    // إحصائيات الدرجات
    $stmt = $conn->prepare("SELECT COUNT(*) FROM student_grades WHERE graded_by = ?");
    $stmt->execute([$instructor['id']]);
    $grades_count = $stmt->fetchColumn();
    
    echo "<div class='row'>";
    
    echo "<div class='col-md-3'>";
    echo "<div class='card bg-primary text-white'>";
    echo "<div class='card-body text-center'>";
    echo "<h3>$courses_count</h3>";
    echo "<p class='mb-0'>كورسات</p>";
    echo "</div></div></div>";
    
    echo "<div class='col-md-3'>";
    echo "<div class='card bg-success text-white'>";
    echo "<div class='card-body text-center'>";
    echo "<h3>$students_count</h3>";
    echo "<p class='mb-0'>طلاب</p>";
    echo "</div></div></div>";
    
    echo "<div class='col-md-3'>";
    echo "<div class='card bg-info text-white'>";
    echo "<div class='card-body text-center'>";
    echo "<h3>$sessions_count</h3>";
    echo "<p class='mb-0'>جلسات</p>";
    echo "</div></div></div>";
    
    echo "<div class='col-md-3'>";
    echo "<div class='card bg-warning text-white'>";
    echo "<div class='card-body text-center'>";
    echo "<h3>$grades_count</h3>";
    echo "<p class='mb-0'>درجات</p>";
    echo "</div></div></div>";
    
    echo "</div>";

    echo "<div class='alert alert-success mt-4'>";
    echo "<h5>🎉 تم إنشاء البيانات التجريبية بنجاح!</h5>";
    echo "<ul class='mb-0'>";
    echo "<li>✅ تم إنشاء $courses_count كورسات</li>";
    echo "<li>✅ تم إنشاء " . count($students_data) . " طلاب</li>";
    echo "<li>✅ تم تسجيل الطلاب في الكورسات</li>";
    echo "<li>✅ تم إنشاء $sessions_count جلسة</li>";
    echo "<li>✅ تم إضافة $grades_count درجة</li>";
    echo "</ul>";
    echo "</div>";

} catch (Exception $e) {
    echo "<div class='alert alert-danger'>❌ خطأ: " . htmlspecialchars($e->getMessage()) . "</div>";
}

echo "<div class='text-center mt-4'>";
echo "<a href='admin/instructor-details.php?id=2' class='btn btn-primary btn-lg me-2'>👨‍🏫 عرض تفاصيل المدرب</a>";
echo "<a href='admin/manage-instructors.php' class='btn btn-success btn-lg me-2'>📋 إدارة المدربين</a>";
echo "<a href='admin/dashboard.php' class='btn btn-info btn-lg'>🏠 لوحة التحكم</a>";
echo "</div>";

echo "<div class='mt-4'>";
echo "<div class='alert alert-info'>";
echo "<h6>💡 ملاحظات:</h6>";
echo "<ul class='mb-0'>";
echo "<li>تم إنشاء بيانات تجريبية شاملة للمدرب</li>";
echo "<li>يمكنك الآن عرض تفاصيل المدرب بدون أخطاء</li>";
echo "<li>جميع الجداول تحتوي على بيانات تجريبية</li>";
echo "<li>كلمة مرور الطلاب الجدد: student123</li>";
echo "</ul>";
echo "</div>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>
