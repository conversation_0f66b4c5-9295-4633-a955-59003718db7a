<?php
/**
 * إضافة المستخدمين التجريبيين
 * Add Demo Users Script
 */

// تفعيل عرض الأخطاء
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// تضمين ملف الاتصال بقاعدة البيانات
require_once 'config/database.php';

echo "<!DOCTYPE html>";
echo "<html dir='rtl' lang='ar'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>إضافة المستخدمين التجريبيين</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<style>";
echo "body { font-family: 'Tahoma', sans-serif; background-color: #f8f9fa; }";
echo ".container { max-width: 800px; margin-top: 2rem; }";
echo ".step { margin-bottom: 1rem; padding: 1rem; border-radius: 8px; }";
echo ".step.success { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; }";
echo ".step.error { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }";
echo ".step.info { background-color: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }";
echo "</style>";
echo "</head>";
echo "<body>";
echo "<div class='container'>";
echo "<h1 class='text-center mb-4'>👥 إضافة المستخدمين التجريبيين</h1>";

try {
    echo "<div class='step info'>";
    echo "<h5>🔍 فحص قاعدة البيانات</h5>";
    echo "</div>";
    
    // التحقق من وجود جدول المستخدمين
    $stmt = $conn->query("SHOW TABLES LIKE 'users'");
    if ($stmt->rowCount() == 0) {
        throw new Exception("جدول المستخدمين غير موجود!");
    }
    
    echo "<div class='step success'>";
    echo "<h6>✅ جدول المستخدمين موجود</h6>";
    echo "</div>";
    
    // فحص هيكل الجدول
    $stmt = $conn->query("DESCRIBE users");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<div class='step info'>";
    echo "<h6>📋 أعمدة جدول المستخدمين:</h6>";
    echo "<ul>";
    foreach ($columns as $column) {
        echo "<li>$column</li>";
    }
    echo "</ul>";
    echo "</div>";
    
    // التحقق من المستخدمين الموجودين
    $stmt = $conn->query("SELECT COUNT(*) FROM users");
    $userCount = $stmt->fetchColumn();
    
    echo "<div class='step info'>";
    echo "<h6>عدد المستخدمين الحاليين: $userCount</h6>";
    echo "</div>";
    
    // حذف المستخدمين التجريبيين الموجودين (إن وجدوا)
    $conn->exec("DELETE FROM users WHERE email IN ('<EMAIL>', '<EMAIL>', '<EMAIL>')");
    
    echo "<div class='step info'>";
    echo "<h6>🧹 تم تنظيف المستخدمين التجريبيين السابقين</h6>";
    echo "</div>";
    
    // إنشاء المستخدمين التجريبيين
    echo "<div class='step info'>";
    echo "<h5>👤 إنشاء المستخدمين التجريبيين</h5>";
    echo "</div>";
    
    // كلمة المرور المشفرة لـ "password"
    $hashedPassword = password_hash('password', PASSWORD_DEFAULT);
    
    // بيانات المستخدمين
    $users = [
        [
            'name' => 'مدير النظام',
            'email' => '<EMAIL>',
            'username' => 'admin',
            'password' => $hashedPassword,
            'role' => 'admin'
        ],
        [
            'name' => 'أحمد محمد - مدرب',
            'email' => '<EMAIL>',
            'username' => 'instructor_demo',
            'password' => $hashedPassword,
            'role' => 'instructor'
        ],
        [
            'name' => 'سارة أحمد - طالبة',
            'email' => '<EMAIL>',
            'username' => 'student_demo',
            'password' => $hashedPassword,
            'role' => 'student'
        ]
    ];
    
    // إدراج المستخدمين
    $insertSQL = "INSERT INTO users (name, email, username, password, role, status, created_at) VALUES (?, ?, ?, ?, ?, 'active', NOW())";
    $stmt = $conn->prepare($insertSQL);

    foreach ($users as $user) {
        try {
            $stmt->execute([
                $user['name'],
                $user['email'],
                $user['username'],
                $user['password'],
                $user['role']
            ]);
            
            echo "<div class='step success'>";
            echo "<h6>✅ تم إنشاء المستخدم: {$user['name']}</h6>";
            echo "<p><strong>البريد:</strong> {$user['email']}<br>";
            echo "<strong>الدور:</strong> {$user['role']}</p>";
            echo "</div>";
            
        } catch (PDOException $e) {
            echo "<div class='step error'>";
            echo "<h6>❌ خطأ في إنشاء المستخدم: {$user['name']}</h6>";
            echo "<p>الخطأ: " . $e->getMessage() . "</p>";
            echo "</div>";
        }
    }
    
    // التحقق من النتائج
    $stmt = $conn->query("SELECT id, name, email, role, status FROM users ORDER BY id");
    $allUsers = $stmt->fetchAll();
    
    echo "<div class='step success'>";
    echo "<h5>📊 المستخدمون في قاعدة البيانات:</h5>";
    echo "<table class='table table-striped'>";
    echo "<thead>";
    echo "<tr><th>ID</th><th>الاسم</th><th>البريد الإلكتروني</th><th>الدور</th><th>الحالة</th></tr>";
    echo "</thead>";
    echo "<tbody>";
    
    foreach ($allUsers as $user) {
        $badgeClass = $user['role'] == 'admin' ? 'bg-danger' : ($user['role'] == 'instructor' ? 'bg-info' : 'bg-success');
        echo "<tr>";
        echo "<td>{$user['id']}</td>";
        echo "<td>{$user['name']}</td>";
        echo "<td>{$user['email']}</td>";
        echo "<td><span class='badge $badgeClass'>{$user['role']}</span></td>";
        echo "<td><span class='badge bg-success'>{$user['status']}</span></td>";
        echo "</tr>";
    }
    
    echo "</tbody>";
    echo "</table>";
    echo "</div>";
    
    // اختبار تسجيل الدخول
    echo "<div class='step info'>";
    echo "<h5>🧪 اختبار تسجيل الدخول</h5>";
    echo "</div>";
    
    $testEmail = '<EMAIL>';
    $testPassword = 'password';
    
    $stmt = $conn->prepare("SELECT id, name, email, password, role FROM users WHERE email = ? AND status = 'active'");
    $stmt->execute([$testEmail]);
    $testUser = $stmt->fetch();
    
    if ($testUser) {
        if (password_verify($testPassword, $testUser['password'])) {
            echo "<div class='step success'>";
            echo "<h6>✅ اختبار تسجيل الدخول نجح!</h6>";
            echo "<p><strong>المستخدم:</strong> {$testUser['name']}</p>";
            echo "<p><strong>الدور:</strong> {$testUser['role']}</p>";
            echo "</div>";
        } else {
            echo "<div class='step error'>";
            echo "<h6>❌ كلمة المرور غير صحيحة!</h6>";
            echo "</div>";
        }
    } else {
        echo "<div class='step error'>";
        echo "<h6>❌ المستخدم غير موجود!</h6>";
        echo "</div>";
    }
    
    // إضافة كورس تجريبي
    echo "<div class='step info'>";
    echo "<h5>📚 إضافة كورس تجريبي</h5>";
    echo "</div>";
    
    // البحث عن المدرب
    $stmt = $conn->prepare("SELECT id FROM users WHERE role = 'instructor' LIMIT 1");
    $stmt->execute();
    $instructor = $stmt->fetch();
    
    if ($instructor) {
        $courseSQL = "INSERT INTO courses (title, slug, description, instructor_id, status, featured, created_at) VALUES (?, ?, ?, ?, 'active', TRUE, NOW())";
        $stmt = $conn->prepare($courseSQL);
        
        try {
            $stmt->execute([
                'مقدمة في تطوير المواقع',
                'intro-web-development',
                'دورة شاملة لتعلم أساسيات تطوير المواقع باستخدام HTML, CSS, و JavaScript',
                $instructor['id']
            ]);
            
            echo "<div class='step success'>";
            echo "<h6>✅ تم إنشاء كورس تجريبي</h6>";
            echo "</div>";
        } catch (PDOException $e) {
            echo "<div class='step info'>";
            echo "<h6>ℹ️ الكورس التجريبي موجود مسبقاً أو حدث خطأ</h6>";
            echo "</div>";
        }
    }
    
    echo "<div class='step success'>";
    echo "<h5>🎉 تم الانتهاء بنجاح!</h5>";
    echo "<p><strong>بيانات تسجيل الدخول:</strong></p>";
    echo "<div class='row'>";
    
    echo "<div class='col-md-4'>";
    echo "<div class='card border-danger'>";
    echo "<div class='card-header bg-danger text-white'><strong>مدير النظام</strong></div>";
    echo "<div class='card-body'>";
    echo "<p><strong>البريد:</strong> <EMAIL></p>";
    echo "<p><strong>كلمة المرور:</strong> password</p>";
    echo "</div>";
    echo "</div>";
    echo "</div>";
    
    echo "<div class='col-md-4'>";
    echo "<div class='card border-info'>";
    echo "<div class='card-header bg-info text-white'><strong>مدرب</strong></div>";
    echo "<div class='card-body'>";
    echo "<p><strong>البريد:</strong> <EMAIL></p>";
    echo "<p><strong>كلمة المرور:</strong> password</p>";
    echo "</div>";
    echo "</div>";
    echo "</div>";
    
    echo "<div class='col-md-4'>";
    echo "<div class='card border-success'>";
    echo "<div class='card-header bg-success text-white'><strong>طالب</strong></div>";
    echo "<div class='card-body'>";
    echo "<p><strong>البريد:</strong> <EMAIL></p>";
    echo "<p><strong>كلمة المرور:</strong> password</p>";
    echo "</div>";
    echo "</div>";
    echo "</div>";
    
    echo "</div>";
    
    echo "<div class='text-center mt-4'>";
    echo "<a href='login.php' class='btn btn-primary btn-lg me-2'>🚀 تجربة تسجيل الدخول</a>";
    echo "<a href='admin/dashboard.php' class='btn btn-success btn-lg'>📊 لوحة تحكم المدير</a>";
    echo "</div>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='step error'>";
    echo "<h5>❌ حدث خطأ</h5>";
    echo "<p><strong>الخطأ:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>الملف:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>السطر:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}

echo "</div>";
echo "<script src='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js'></script>";
echo "</body>";
echo "</html>";
?>
