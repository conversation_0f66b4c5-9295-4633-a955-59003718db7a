<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'config/database.php';
require_once 'includes/functions.php';

echo "<h2>تشخيص مشكلة التسجيل</h2>";

// اختبار الاتصال بقاعدة البيانات
try {
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM users");
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "<p>✅ الاتصال بقاعدة البيانات ناجح - عدد المستخدمين: " . $result['count'] . "</p>";
} catch (Exception $e) {
    echo "<p>❌ خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "</p>";
}

// اختبار دالة sanitize
try {
    $test_name = sanitize("أحمد محمد");
    echo "<p>✅ دالة sanitize تعمل: " . $test_name . "</p>";
} catch (Exception $e) {
    echo "<p>❌ خطأ في دالة sanitize: " . $e->getMessage() . "</p>";
}

// اختبار بنية جدول المستخدمين
try {
    $stmt = $conn->prepare("DESCRIBE users");
    $stmt->execute();
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>أعمدة جدول المستخدمين:</h3>";
    echo "<ul>";
    foreach ($columns as $column) {
        echo "<li>" . $column['Field'] . " (" . $column['Type'] . ")</li>";
    }
    echo "</ul>";
} catch (Exception $e) {
    echo "<p>❌ خطأ في فحص جدول المستخدمين: " . $e->getMessage() . "</p>";
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    echo "<h3>بيانات النموذج المرسلة:</h3>";
    echo "<pre>";
    print_r($_POST);
    echo "</pre>";
    
    $name = sanitize($_POST['name']);
    $email = filter_var($_POST['email'], FILTER_SANITIZE_EMAIL);
    $phone = sanitize($_POST['phone']);
    $password = $_POST['password'];
    $confirm_password = $_POST['confirm_password'];
    
    echo "<h3>البيانات بعد التنظيف:</h3>";
    echo "<p>الاسم: " . htmlspecialchars($name) . "</p>";
    echo "<p>البريد: " . htmlspecialchars($email) . "</p>";
    echo "<p>الهاتف: " . htmlspecialchars($phone) . "</p>";
    echo "<p>كلمة المرور: " . (strlen($password) > 0 ? "تم إدخالها (" . strlen($password) . " حرف)" : "فارغة") . "</p>";
    
    // التحقق من البيانات
    $errors = [];
    
    if (empty($name)) $errors[] = "الاسم فارغ";
    if (empty($email)) $errors[] = "البريد فارغ";
    if (empty($phone)) $errors[] = "الهاتف فارغ";
    if (empty($password)) $errors[] = "كلمة المرور فارغة";
    if ($password !== $confirm_password) $errors[] = "كلمة المرور غير متطابقة";
    if (strlen($password) < 8) $errors[] = "كلمة المرور قصيرة";
    if (!preg_match('/^[0-9]{10}$/', $phone)) $errors[] = "رقم الهاتف غير صالح";
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) $errors[] = "البريد الإلكتروني غير صالح";
    
    if (!empty($errors)) {
        echo "<h3>أخطاء في البيانات:</h3>";
        echo "<ul>";
        foreach ($errors as $error) {
            echo "<li style='color: red;'>" . $error . "</li>";
        }
        echo "</ul>";
    } else {
        echo "<h3>✅ جميع البيانات صحيحة</h3>";
        
        try {
            // فحص وجود البريد الإلكتروني
            $stmt = $conn->prepare("SELECT id FROM users WHERE email = ?");
            $stmt->execute([$email]);
            
            if ($stmt->rowCount() > 0) {
                echo "<p style='color: red;'>❌ البريد الإلكتروني مسجل مسبقاً</p>";
            } else {
                echo "<p>✅ البريد الإلكتروني متاح</p>";
                
                // محاولة إنشاء المستخدم
                $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
                $username = explode('@', $email)[0];
                
                echo "<p>كلمة المرور المشفرة: " . substr($hashedPassword, 0, 20) . "...</p>";
                echo "<p>اسم المستخدم: " . $username . "</p>";
                
                try {
                    $stmt = $conn->prepare("INSERT INTO users (name, email, phone, username, password, role, status, created_at) VALUES (?, ?, ?, ?, ?, 'student', 'pending', NOW())");
                    $result = $stmt->execute([$name, $email, $phone, $username, $hashedPassword]);
                    
                    if ($result) {
                        $user_id = $conn->lastInsertId();
                        echo "<p style='color: green;'>✅ تم إنشاء المستخدم بنجاح! ID: " . $user_id . "</p>";
                        
                        // محاولة إنشاء طلب انضمام
                        try {
                            $stmt = $conn->prepare("INSERT INTO join_requests (name, email, phone, course_id, status, created_at) VALUES (?, ?, ?, NULL, 'pending', NOW())");
                            $result2 = $stmt->execute([$name, $email, $phone]);
                            
                            if ($result2) {
                                echo "<p style='color: green;'>✅ تم إنشاء طلب الانضمام بنجاح!</p>";
                            } else {
                                echo "<p style='color: orange;'>⚠️ فشل في إنشاء طلب الانضمام</p>";
                            }
                        } catch (Exception $e) {
                            echo "<p style='color: orange;'>⚠️ خطأ في إنشاء طلب الانضمام: " . $e->getMessage() . "</p>";
                        }
                    } else {
                        echo "<p style='color: red;'>❌ فشل في إنشاء المستخدم</p>";
                    }
                } catch (Exception $e) {
                    echo "<p style='color: red;'>❌ خطأ في إنشاء المستخدم: " . $e->getMessage() . "</p>";
                    
                    // محاولة بدون username
                    try {
                        $stmt = $conn->prepare("INSERT INTO users (name, email, phone, password, role, status, created_at) VALUES (?, ?, ?, ?, 'student', 'pending', NOW())");
                        $result = $stmt->execute([$name, $email, $phone, $hashedPassword]);
                        
                        if ($result) {
                            echo "<p style='color: green;'>✅ تم إنشاء المستخدم بنجاح (بدون username)!</p>";
                        } else {
                            echo "<p style='color: red;'>❌ فشل في إنشاء المستخدم حتى بدون username</p>";
                        }
                    } catch (Exception $e2) {
                        echo "<p style='color: red;'>❌ خطأ نهائي: " . $e2->getMessage() . "</p>";
                    }
                }
            }
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "</p>";
        }
    }
}
?>

<h3>نموذج اختبار التسجيل:</h3>
<form method="POST" style="max-width: 500px; border: 1px solid #ccc; padding: 20px;">
    <p>
        <label>الاسم الكامل:</label><br>
        <input type="text" name="name" required style="width: 100%; padding: 5px;" value="أحمد محمد">
    </p>
    <p>
        <label>البريد الإلكتروني:</label><br>
        <input type="email" name="email" required style="width: 100%; padding: 5px;" value="<EMAIL>">
    </p>
    <p>
        <label>رقم الهاتف:</label><br>
        <input type="text" name="phone" required style="width: 100%; padding: 5px;" value="0501234567">
    </p>
    <p>
        <label>كلمة المرور:</label><br>
        <input type="password" name="password" required style="width: 100%; padding: 5px;" value="12345678">
    </p>
    <p>
        <label>تأكيد كلمة المرور:</label><br>
        <input type="password" name="confirm_password" required style="width: 100%; padding: 5px;" value="12345678">
    </p>
    <p>
        <input type="submit" value="اختبار التسجيل" style="padding: 10px 20px; background: #007bff; color: white; border: none; cursor: pointer;">
    </p>
</form>
