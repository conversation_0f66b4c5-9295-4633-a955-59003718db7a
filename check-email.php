<?php
/**
 * فحص توفر البريد الإلكتروني
 * Check Email Availability
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

require_once 'config/database.php';

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

// قراءة البيانات
$input = json_decode(file_get_contents('php://input'), true);

if (!isset($input['email']) || empty($input['email'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Email is required']);
    exit;
}

$email = filter_var($input['email'], FILTER_SANITIZE_EMAIL);

// التحقق من صحة البريد الإلكتروني
if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
    echo json_encode(['error' => 'Invalid email format', 'exists' => false]);
    exit;
}

try {
    // فحص وجود البريد الإلكتروني في قاعدة البيانات
    $stmt = $conn->prepare("SELECT id FROM users WHERE email = ? LIMIT 1");
    $stmt->execute([$email]);
    
    $exists = $stmt->rowCount() > 0;
    
    echo json_encode([
        'exists' => $exists,
        'message' => $exists ? 'البريد الإلكتروني مسجل مسبقاً' : 'البريد الإلكتروني متاح'
    ]);
    
} catch (PDOException $e) {
    error_log("Database error in check-email.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Database error', 'exists' => false]);
}
?>
