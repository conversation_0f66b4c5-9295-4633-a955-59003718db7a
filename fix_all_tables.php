<?php
/**
 * إصلاح شامل لجميع الجداول والأعمدة
 * Comprehensive fix for all tables and columns
 */

require_once 'config/database.php';

echo "<!DOCTYPE html>";
echo "<html dir='rtl' lang='ar'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>إصلاح شامل للجداول</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "</head>";
echo "<body>";
echo "<div class='container mt-4'>";
echo "<h2>🔧 إصلاح شامل لجميع الجداول</h2>";

try {
    // 1. إصلاح جدول join_requests
    echo "<h4>📋 إصلاح جدول join_requests</h4>";
    
    // التحقق من وجود الجدول
    $stmt = $conn->query("SHOW TABLES LIKE 'join_requests'");
    if ($stmt->rowCount() == 0) {
        // إنشاء الجدول
        $conn->exec("
            CREATE TABLE join_requests (
                id INT AUTO_INCREMENT PRIMARY KEY,
                course_id INT NOT NULL DEFAULT 1,
                student_name VARCHAR(255) NOT NULL DEFAULT '',
                student_email VARCHAR(255) NOT NULL DEFAULT '',
                student_phone VARCHAR(20) DEFAULT NULL,
                message TEXT DEFAULT NULL,
                status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
                request_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                processed_date TIMESTAMP NULL,
                processed_by INT DEFAULT NULL,
                rejection_reason TEXT DEFAULT NULL,
                
                INDEX idx_course_id (course_id),
                INDEX idx_status (status),
                INDEX idx_email (student_email)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        echo "<div class='alert alert-success'>✅ تم إنشاء جدول join_requests</div>";
    } else {
        // فحص وإضافة الأعمدة المفقودة
        $stmt = $conn->query("DESCRIBE join_requests");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $existing_columns = array_column($columns, 'Field');
        
        $required_columns = [
            'course_id' => 'INT NOT NULL DEFAULT 1',
            'student_name' => 'VARCHAR(255) NOT NULL DEFAULT ""',
            'student_email' => 'VARCHAR(255) NOT NULL DEFAULT ""',
            'student_phone' => 'VARCHAR(20) DEFAULT NULL',
            'message' => 'TEXT DEFAULT NULL',
            'request_date' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
            'processed_date' => 'TIMESTAMP NULL',
            'processed_by' => 'INT DEFAULT NULL',
            'rejection_reason' => 'TEXT DEFAULT NULL'
        ];
        
        foreach ($required_columns as $column_name => $column_definition) {
            if (!in_array($column_name, $existing_columns)) {
                $conn->exec("ALTER TABLE join_requests ADD COLUMN $column_name $column_definition");
                echo "<div class='alert alert-success'>✅ تم إضافة عمود: $column_name</div>";
            }
        }
        echo "<div class='alert alert-info'>✅ تم فحص جدول join_requests</div>";
    }

    // 2. إصلاح جدول sessions
    echo "<h4>🎥 إصلاح جدول sessions</h4>";
    
    $stmt = $conn->query("SHOW TABLES LIKE 'sessions'");
    if ($stmt->rowCount() == 0) {
        // إنشاء الجدول
        $conn->exec("
            CREATE TABLE sessions (
                id INT AUTO_INCREMENT PRIMARY KEY,
                course_id INT NOT NULL,
                title VARCHAR(255) NOT NULL,
                description TEXT,
                session_date DATE NOT NULL,
                start_time TIME NOT NULL,
                end_time TIME NOT NULL,
                zoom_link VARCHAR(500),
                meeting_id VARCHAR(100),
                passcode VARCHAR(50),
                status ENUM('scheduled', 'ongoing', 'completed', 'cancelled') DEFAULT 'scheduled',
                max_attendees INT DEFAULT 100,
                recording_url VARCHAR(500) DEFAULT NULL,
                session_file VARCHAR(500) DEFAULT NULL,
                file_name VARCHAR(255) DEFAULT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                
                FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
                INDEX idx_course_id (course_id),
                INDEX idx_session_date (session_date),
                INDEX idx_status (status)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        echo "<div class='alert alert-success'>✅ تم إنشاء جدول sessions</div>";
    } else {
        // فحص وإضافة الأعمدة المفقودة
        $stmt = $conn->query("DESCRIBE sessions");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $existing_columns = array_column($columns, 'Field');
        
        $required_columns = [
            'session_date' => 'DATE NOT NULL DEFAULT "2025-01-01"',
            'start_time' => 'TIME NOT NULL DEFAULT "10:00:00"',
            'end_time' => 'TIME NOT NULL DEFAULT "11:00:00"',
            'zoom_link' => 'VARCHAR(500) DEFAULT NULL',
            'meeting_id' => 'VARCHAR(100) DEFAULT NULL',
            'passcode' => 'VARCHAR(50) DEFAULT NULL',
            'session_file' => 'VARCHAR(500) DEFAULT NULL',
            'file_name' => 'VARCHAR(255) DEFAULT NULL'
        ];
        
        foreach ($required_columns as $column_name => $column_definition) {
            if (!in_array($column_name, $existing_columns)) {
                $conn->exec("ALTER TABLE sessions ADD COLUMN $column_name $column_definition");
                echo "<div class='alert alert-success'>✅ تم إضافة عمود: $column_name</div>";
            }
        }
        echo "<div class='alert alert-info'>✅ تم فحص جدول sessions</div>";
    }

    // 3. إنشاء جدول course_enrollments
    echo "<h4>📚 إنشاء جدول course_enrollments</h4>";
    
    $stmt = $conn->query("SHOW TABLES LIKE 'course_enrollments'");
    if ($stmt->rowCount() == 0) {
        $conn->exec("
            CREATE TABLE course_enrollments (
                id INT AUTO_INCREMENT PRIMARY KEY,
                course_id INT NOT NULL,
                student_id INT NOT NULL,
                enrollment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                status ENUM('active', 'completed', 'dropped', 'suspended') DEFAULT 'active',
                final_grade DECIMAL(5,2) DEFAULT NULL,
                completion_date TIMESTAMP NULL,
                notes TEXT DEFAULT NULL,
                
                FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
                FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
                UNIQUE KEY unique_enrollment (course_id, student_id),
                INDEX idx_course_id (course_id),
                INDEX idx_student_id (student_id),
                INDEX idx_status (status)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        echo "<div class='alert alert-success'>✅ تم إنشاء جدول course_enrollments</div>";
    } else {
        echo "<div class='alert alert-info'>✅ جدول course_enrollments موجود</div>";
    }

    // 4. إنشاء جدول student_grades
    echo "<h4>📝 إنشاء جدول student_grades</h4>";
    
    $stmt = $conn->query("SHOW TABLES LIKE 'student_grades'");
    if ($stmt->rowCount() == 0) {
        $conn->exec("
            CREATE TABLE student_grades (
                id INT AUTO_INCREMENT PRIMARY KEY,
                course_id INT NOT NULL,
                student_id INT NOT NULL,
                assignment_name VARCHAR(255) NOT NULL,
                grade DECIMAL(5,2) NOT NULL,
                max_grade DECIMAL(5,2) NOT NULL DEFAULT 100,
                grade_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                notes TEXT DEFAULT NULL,
                graded_by INT NOT NULL,
                
                FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
                FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
                FOREIGN KEY (graded_by) REFERENCES users(id) ON DELETE CASCADE,
                INDEX idx_course_student (course_id, student_id),
                INDEX idx_assignment (assignment_name)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        echo "<div class='alert alert-success'>✅ تم إنشاء جدول student_grades</div>";
    } else {
        echo "<div class='alert alert-info'>✅ جدول student_grades موجود</div>";
    }

    // 5. إنشاء جدول session_attendance
    echo "<h4>📊 إنشاء جدول session_attendance</h4>";
    
    $stmt = $conn->query("SHOW TABLES LIKE 'session_attendance'");
    if ($stmt->rowCount() == 0) {
        $conn->exec("
            CREATE TABLE session_attendance (
                id INT AUTO_INCREMENT PRIMARY KEY,
                session_id INT NOT NULL,
                student_id INT NOT NULL,
                attendance_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                leave_time TIMESTAMP NULL,
                duration_minutes INT DEFAULT 0,
                status ENUM('present', 'absent', 'late', 'left_early') DEFAULT 'present',
                notes TEXT DEFAULT NULL,
                
                FOREIGN KEY (session_id) REFERENCES sessions(id) ON DELETE CASCADE,
                FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
                UNIQUE KEY unique_attendance (session_id, student_id),
                INDEX idx_session_id (session_id),
                INDEX idx_student_id (student_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        echo "<div class='alert alert-success'>✅ تم إنشاء جدول session_attendance</div>";
    } else {
        echo "<div class='alert alert-info'>✅ جدول session_attendance موجود</div>";
    }

    // 6. إضافة بيانات تجريبية
    echo "<h4>📝 إضافة بيانات تجريبية</h4>";
    
    // طلبات انضمام تجريبية
    $stmt = $conn->query("SELECT COUNT(*) FROM join_requests");
    if ($stmt->fetchColumn() == 0) {
        $sample_requests = [
            [1, 'سارة أحمد', '<EMAIL>', '0501234567', 'أرغب في الانضمام لهذا الكورس'],
            [1, 'محمد علي', '<EMAIL>', '0507654321', 'مهتم بتعلم البرمجة'],
            [2, 'فاطمة خالد', '<EMAIL>', '0509876543', 'أريد تطوير مهاراتي']
        ];
        
        $stmt = $conn->prepare("INSERT INTO join_requests (course_id, student_name, student_email, student_phone, message) VALUES (?, ?, ?, ?, ?)");
        foreach ($sample_requests as $request) {
            $stmt->execute($request);
        }
        echo "<div class='alert alert-success'>✅ تم إضافة طلبات انضمام تجريبية</div>";
    }
    
    // جلسات تجريبية
    $stmt = $conn->query("SELECT COUNT(*) FROM sessions");
    if ($stmt->fetchColumn() == 0) {
        $sample_sessions = [
            [1, 'مقدمة في البرمجة', 'جلسة تعريفية', '2025-06-10', '10:00:00', '11:30:00'],
            [1, 'أساسيات HTML', 'تعلم HTML', '2025-06-12', '10:00:00', '11:30:00'],
            [2, 'أساسيات التصميم', 'مبادئ التصميم', '2025-06-11', '14:00:00', '15:30:00']
        ];
        
        $stmt = $conn->prepare("INSERT INTO sessions (course_id, title, description, session_date, start_time, end_time) VALUES (?, ?, ?, ?, ?, ?)");
        foreach ($sample_sessions as $session) {
            $stmt->execute($session);
        }
        echo "<div class='alert alert-success'>✅ تم إضافة جلسات تجريبية</div>";
    }

    // إنشاء مجلدات
    $directories = ['uploads/courses', 'uploads/sessions', 'uploads/assignments', 'uploads/materials'];
    foreach ($directories as $dir) {
        if (!file_exists($dir)) {
            mkdir($dir, 0777, true);
            echo "<div class='alert alert-success'>✅ تم إنشاء مجلد: $dir</div>";
        }
    }

    echo "<div class='alert alert-success mt-4'>";
    echo "<h5>🎉 تم الإصلاح بنجاح!</h5>";
    echo "<p>جميع الجداول والأعمدة جاهزة للاستخدام.</p>";
    echo "</div>";

} catch (Exception $e) {
    echo "<div class='alert alert-danger'>❌ خطأ: " . htmlspecialchars($e->getMessage()) . "</div>";
}

echo "<div class='mt-4'>";
echo "<a href='instructor/course-details.php?id=1' class='btn btn-primary btn-lg me-2'>📋 تفاصيل كورس</a>";
echo "<a href='instructor/courses.php' class='btn btn-success btn-lg me-2'>📚 الكورسات</a>";
echo "<a href='admin/manage_join_requests.php' class='btn btn-warning btn-lg me-2'>📝 طلبات الانضمام</a>";
echo "<a href='instructor/dashboard.php' class='btn btn-info btn-lg'>🏠 لوحة التحكم</a>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>
