<?php
require_once 'includes/session_config.php';
require_once 'config/database.php';
require_once 'includes/functions.php';

$error = '';
$success = '';
$course = null;
$invitation = null;

// جلب كود الدعوة من الرابط
$invitation_code = isset($_GET['code']) ? trim($_GET['code']) : '';

if (empty($invitation_code)) {
    $error = 'رابط الدعوة غير صحيح';
} else {
    // التحقق من صحة رابط الدعوة
    try {
        $stmt = $conn->prepare("
            SELECT ci.*, c.title, c.description, c.course_type, c.price, c.currency, c.image_path,
                   u.name as instructor_name, u.email as instructor_email
            FROM course_invitations ci
            JOIN courses c ON ci.course_id = c.id
            JOIN users u ON ci.instructor_id = u.id
            WHERE ci.invitation_code = ? AND ci.status = 'active'
        ");
        $stmt->execute([$invitation_code]);
        $invitation_data = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$invitation_data) {
            $error = 'رابط الدعوة غير صحيح أو منتهي الصلاحية';
        } else {
            $invitation = $invitation_data;
            $course = $invitation_data;
            
            // تحديث عدد مرات الاستخدام
            $stmt = $conn->prepare("UPDATE course_invitations SET current_uses = current_uses + 1 WHERE id = ?");
            $stmt->execute([$invitation['id']]);
        }
    } catch (PDOException $e) {
        $error = 'حدث خطأ في التحقق من رابط الدعوة';
    }
}

// معالجة طلب الانضمام
if ($_SERVER['REQUEST_METHOD'] === 'POST' && $course) {
    try {
        $student_name = trim($_POST['student_name']);
        $student_email = trim($_POST['student_email']);
        $student_phone = trim($_POST['student_phone']);
        $student_password = $_POST['student_password'];
        $message = trim($_POST['message']);
        
        // التحقق من البيانات
        if (empty($student_name) || empty($student_email) || empty($student_password)) {
            throw new Exception('يرجى ملء جميع الحقول المطلوبة');
        }
        
        if (!filter_var($student_email, FILTER_VALIDATE_EMAIL)) {
            throw new Exception('البريد الإلكتروني غير صحيح');
        }
        
        if (strlen($student_password) < 6) {
            throw new Exception('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
        }
        
        // التحقق من وجود المستخدم
        $stmt = $conn->prepare("SELECT id FROM users WHERE email = ?");
        $stmt->execute([$student_email]);
        $existing_user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $student_id = null;
        
        if ($existing_user) {
            $student_id = $existing_user['id'];
        } else {
            // إنشاء حساب جديد للطالب
            $hashed_password = password_hash($student_password, PASSWORD_DEFAULT);
            $stmt = $conn->prepare("
                INSERT INTO users (name, email, password, role, phone, status, created_at)
                VALUES (?, ?, ?, 'student', ?, 'active', NOW())
            ");
            $stmt->execute([$student_name, $student_email, $hashed_password, $student_phone]);
            $student_id = $conn->lastInsertId();
        }
        
        // التحقق من عدم وجود طلب سابق
        $stmt = $conn->prepare("SELECT id FROM join_requests WHERE course_id = ? AND student_id = ?");
        $stmt->execute([$course['course_id'], $student_id]);

        if ($stmt->fetch()) {
            throw new Exception('لديك طلب انضمام سابق لهذا الكورس');
        }

        // إرسال طلب الانضمام
        $stmt = $conn->prepare("
            INSERT INTO join_requests
            (course_id, student_id, message, status, requested_at)
            VALUES (?, ?, ?, 'pending', NOW())
        ");
        $stmt->execute([
            $course['course_id'],
            $student_id,
            $message
        ]);
        
        $success = 'تم إرسال طلب الانضمام بنجاح! سيتم مراجعة طلبك من قبل المدرب وإشعارك بالنتيجة.';
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الانضمام للكورس - <?php echo $course ? htmlspecialchars($course['title']) : 'نظام التعلم'; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .join-container {
            max-width: 800px;
            margin: 2rem auto;
            padding: 0 1rem;
        }
        .course-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .course-header {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        .course-image {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            object-fit: cover;
            border: 4px solid white;
            margin-bottom: 1rem;
        }
        .form-section {
            padding: 2rem;
        }
        .btn-join {
            background: linear-gradient(45deg, #007bff, #0056b3);
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        .btn-join:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,123,255,0.3);
        }
    </style>
</head>
<body>
    <div class="join-container">
        <?php if ($error): ?>
            <div class="alert alert-danger text-center">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <?php echo htmlspecialchars($error); ?>
            </div>
            <div class="text-center">
                <a href="login.php" class="btn btn-primary">تسجيل الدخول</a>
                <a href="index.php" class="btn btn-secondary">الصفحة الرئيسية</a>
            </div>
        <?php elseif ($success): ?>
            <div class="course-card">
                <div class="course-header">
                    <i class="fas fa-check-circle fa-3x mb-3"></i>
                    <h3>تم إرسال طلب الانضمام بنجاح!</h3>
                </div>
                <div class="form-section text-center">
                    <div class="alert alert-success">
                        <?php echo htmlspecialchars($success); ?>
                    </div>
                    <h5>ماذا بعد؟</h5>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-clock text-warning me-2"></i> سيراجع المدرب طلبك</li>
                        <li><i class="fas fa-envelope text-info me-2"></i> ستصلك رسالة بريد إلكتروني بالنتيجة</li>
                        <li><i class="fas fa-graduation-cap text-success me-2"></i> بعد القبول يمكنك الوصول للكورس</li>
                    </ul>
                    <div class="mt-4">
                        <a href="login.php" class="btn btn-primary me-2">تسجيل الدخول</a>
                        <a href="index.php" class="btn btn-secondary">الصفحة الرئيسية</a>
                    </div>
                </div>
            </div>
        <?php elseif ($course): ?>
            <div class="course-card">
                <!-- معلومات الكورس -->
                <div class="course-header">
                    <?php if ($course['image_path']): ?>
                        <img src="<?php echo htmlspecialchars($course['image_path']); ?>" 
                             alt="صورة الكورس" class="course-image">
                    <?php else: ?>
                        <i class="fas fa-graduation-cap fa-3x mb-3"></i>
                    <?php endif; ?>
                    <h2><?php echo htmlspecialchars($course['title']); ?></h2>
                    <p class="mb-2">
                        <i class="fas fa-user me-2"></i>
                        المدرب: <?php echo htmlspecialchars($course['instructor_name']); ?>
                    </p>
                    <?php if ($course['course_type'] === 'paid'): ?>
                        <div class="badge bg-warning fs-6">
                            <i class="fas fa-money-bill-wave me-1"></i>
                            <?php echo number_format($course['price'], 0); ?> <?php echo $course['currency']; ?>
                        </div>
                    <?php else: ?>
                        <div class="badge bg-success fs-6">
                            <i class="fas fa-gift me-1"></i>
                            مجاني
                        </div>
                    <?php endif; ?>
                </div>

                <!-- وصف الكورس -->
                <div class="form-section">
                    <h5><i class="fas fa-info-circle text-primary me-2"></i>وصف الكورس</h5>
                    <p class="text-muted"><?php echo htmlspecialchars($course['description']); ?></p>
                </div>

                <!-- نموذج طلب الانضمام -->
                <div class="form-section border-top">
                    <h5><i class="fas fa-user-plus text-success me-2"></i>طلب الانضمام للكورس</h5>
                    
                    <form method="POST" action="">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="student_name" class="form-label">الاسم الكامل *</label>
                                <input type="text" class="form-control" id="student_name" name="student_name" 
                                       value="<?php echo isset($_POST['student_name']) ? htmlspecialchars($_POST['student_name']) : ''; ?>" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="student_email" class="form-label">البريد الإلكتروني *</label>
                                <input type="email" class="form-control" id="student_email" name="student_email" 
                                       value="<?php echo isset($_POST['student_email']) ? htmlspecialchars($_POST['student_email']) : ''; ?>" required>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="student_phone" class="form-label">رقم الهاتف</label>
                                <input type="tel" class="form-control" id="student_phone" name="student_phone" 
                                       value="<?php echo isset($_POST['student_phone']) ? htmlspecialchars($_POST['student_phone']) : ''; ?>">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="student_password" class="form-label">كلمة المرور *</label>
                                <input type="password" class="form-control" id="student_password" name="student_password" 
                                       placeholder="6 أحرف على الأقل" required>
                                <small class="form-text text-muted">ستستخدم لتسجيل الدخول إذا لم يكن لديك حساب</small>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="message" class="form-label">رسالة للمدرب (اختياري)</label>
                            <textarea class="form-control" id="message" name="message" rows="3" 
                                      placeholder="اكتب سبب رغبتك في الانضمام للكورس..."><?php echo isset($_POST['message']) ? htmlspecialchars($_POST['message']) : ''; ?></textarea>
                        </div>
                        
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>ملاحظة:</strong> بعد إرسال الطلب، سيراجع المدرب طلبك وسيتم إشعارك بالنتيجة عبر البريد الإلكتروني.
                        </div>
                        
                        <div class="text-center">
                            <button type="submit" class="btn btn-join btn-primary btn-lg">
                                <i class="fas fa-paper-plane me-2"></i>
                                إرسال طلب الانضمام
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
