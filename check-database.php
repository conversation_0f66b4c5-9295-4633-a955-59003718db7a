<?php
require_once 'config/database.php';

echo "<h2>فحص قاعدة البيانات</h2>";

// فحص جدول المستخدمين
try {
    $stmt = $conn->prepare("DESCRIBE users");
    $stmt->execute();
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>بنية جدول المستخدمين:</h3>";
    echo "<table border='1'>";
    echo "<tr><th>العمود</th><th>النوع</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
} catch (Exception $e) {
    echo "<p>❌ خطأ في فحص جدول المستخدمين: " . $e->getMessage() . "</p>";
}

// فحص جدول join_requests
try {
    $stmt = $conn->prepare("DESCRIBE join_requests");
    $stmt->execute();
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>بنية جدول طلبات الانضمام:</h3>";
    echo "<table border='1'>";
    echo "<tr><th>العمود</th><th>النوع</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
} catch (Exception $e) {
    echo "<p>❌ خطأ في فحص جدول طلبات الانضمام: " . $e->getMessage() . "</p>";
}

// عرض المستخدمين الحاليين
try {
    $stmt = $conn->prepare("SELECT id, name, email, role, status, created_at FROM users ORDER BY created_at DESC LIMIT 10");
    $stmt->execute();
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>آخر 10 مستخدمين:</h3>";
    if (count($users) > 0) {
        echo "<table border='1'>";
        echo "<tr><th>ID</th><th>الاسم</th><th>البريد</th><th>الدور</th><th>الحالة</th><th>تاريخ التسجيل</th></tr>";
        foreach ($users as $user) {
            echo "<tr>";
            echo "<td>" . $user['id'] . "</td>";
            echo "<td>" . $user['name'] . "</td>";
            echo "<td>" . $user['email'] . "</td>";
            echo "<td>" . $user['role'] . "</td>";
            echo "<td>" . $user['status'] . "</td>";
            echo "<td>" . $user['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>لا يوجد مستخدمين</p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ خطأ في عرض المستخدمين: " . $e->getMessage() . "</p>";
}
?>
