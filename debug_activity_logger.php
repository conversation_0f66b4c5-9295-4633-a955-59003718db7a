<?php
/**
 * تشخيص مشاكل دالة logUserActivity
 * Debug logUserActivity function issues
 */

require_once 'includes/session_config.php';
require_once 'config/database.php';
require_once 'includes/functions.php';
require_once 'includes/activity_logger.php';

echo "<!DOCTYPE html>";
echo "<html dir='rtl' lang='ar'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>تشخيص دالة logUserActivity</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "</head>";
echo "<body>";
echo "<div class='container mt-4'>";
echo "<h2>🔍 تشخيص دالة logUserActivity</h2>";

// التحقق من الجلسة
echo "<h4>👤 معلومات الجلسة:</h4>";
if (isset($_SESSION['user_id'])) {
    echo "<div class='alert alert-info'>";
    echo "User ID: " . $_SESSION['user_id'] . "<br>";
    echo "Name: " . ($_SESSION['name'] ?? 'غير محدد') . "<br>";
    echo "Role: " . ($_SESSION['role'] ?? 'غير محدد') . "<br>";
    echo "</div>";
} else {
    echo "<div class='alert alert-warning'>لا توجد جلسة نشطة</div>";
}

// فحص جدول activity_logs
echo "<h4>📋 فحص جدول activity_logs:</h4>";
try {
    $stmt = $conn->query("SHOW TABLES LIKE 'activity_logs'");
    if ($stmt->rowCount() > 0) {
        echo "<div class='alert alert-success'>✅ جدول activity_logs موجود</div>";
        
        // فحص هيكل الجدول
        $stmt = $conn->query("DESCRIBE activity_logs");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<div class='table-responsive'>";
        echo "<table class='table table-sm table-bordered'>";
        echo "<thead><tr><th>العمود</th><th>النوع</th><th>Null</th><th>Key</th></tr></thead>";
        echo "<tbody>";
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>" . $column['Field'] . "</td>";
            echo "<td>" . $column['Type'] . "</td>";
            echo "<td>" . $column['Null'] . "</td>";
            echo "<td>" . $column['Key'] . "</td>";
            echo "</tr>";
        }
        echo "</tbody></table>";
        echo "</div>";
        
        // عدد السجلات
        $stmt = $conn->query("SELECT COUNT(*) FROM activity_logs");
        $count = $stmt->fetchColumn();
        echo "<div class='alert alert-info'>عدد سجلات الأنشطة: $count</div>";
        
        // آخر 5 أنشطة
        if ($count > 0) {
            $stmt = $conn->query("SELECT * FROM activity_logs ORDER BY created_at DESC LIMIT 5");
            $activities = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<h5>آخر 5 أنشطة:</h5>";
            echo "<div class='table-responsive'>";
            echo "<table class='table table-sm'>";
            echo "<thead><tr><th>ID</th><th>User ID</th><th>Action</th><th>Description</th><th>Date</th></tr></thead>";
            echo "<tbody>";
            foreach ($activities as $activity) {
                echo "<tr>";
                echo "<td>" . $activity['id'] . "</td>";
                echo "<td>" . ($activity['user_id'] ?? 'NULL') . "</td>";
                echo "<td>" . htmlspecialchars($activity['action']) . "</td>";
                echo "<td>" . htmlspecialchars($activity['description']) . "</td>";
                echo "<td>" . $activity['created_at'] . "</td>";
                echo "</tr>";
            }
            echo "</tbody></table>";
            echo "</div>";
        }
        
    } else {
        echo "<div class='alert alert-warning'>❌ جدول activity_logs غير موجود</div>";
        
        // إنشاء الجدول
        try {
            $conn->exec("
                CREATE TABLE activity_logs (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    user_id INT DEFAULT NULL,
                    action VARCHAR(255) NOT NULL,
                    description TEXT,
                    ip_address VARCHAR(45),
                    user_agent TEXT,
                    additional_data JSON DEFAULT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    
                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
                    INDEX idx_user_id (user_id),
                    INDEX idx_action (action),
                    INDEX idx_created_at (created_at)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ");
            echo "<div class='alert alert-success'>✅ تم إنشاء جدول activity_logs</div>";
        } catch (Exception $e) {
            echo "<div class='alert alert-danger'>❌ فشل في إنشاء جدول activity_logs: " . $e->getMessage() . "</div>";
        }
    }
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>❌ خطأ في فحص جدول activity_logs: " . $e->getMessage() . "</div>";
}

// اختبار الدوال خطوة بخطوة
echo "<h4>🧪 اختبار الدوال:</h4>";

// اختبار دالة logActivity الأساسية
echo "<h5>1. اختبار دالة logActivity الأساسية:</h5>";
try {
    $result = logActivity('test_action', 'test description', $_SESSION['user_id'] ?? null, ['test' => true]);
    if ($result) {
        echo "<div class='alert alert-success'>✅ دالة logActivity تعمل</div>";
    } else {
        echo "<div class='alert alert-danger'>❌ دالة logActivity ترجع false</div>";
    }
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>❌ خطأ في دالة logActivity: " . $e->getMessage() . "</div>";
}

// اختبار دالة logUserActivity
echo "<h5>2. اختبار دالة logUserActivity:</h5>";
try {
    $result = logUserActivity('test_user_action', 'test user description');
    if ($result) {
        echo "<div class='alert alert-success'>✅ دالة logUserActivity تعمل</div>";
    } else {
        echo "<div class='alert alert-danger'>❌ دالة logUserActivity ترجع false</div>";
    }
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>❌ خطأ في دالة logUserActivity: " . $e->getMessage() . "</div>";
}

// اختبار إدراج مباشر
echo "<h5>3. اختبار إدراج مباشر:</h5>";
try {
    $stmt = $conn->prepare("
        INSERT INTO activity_logs (user_id, action, description, ip_address, user_agent) 
        VALUES (?, ?, ?, ?, ?)
    ");
    
    $result = $stmt->execute([
        $_SESSION['user_id'] ?? null,
        'direct_test',
        'اختبار إدراج مباشر',
        $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
    ]);
    
    if ($result) {
        echo "<div class='alert alert-success'>✅ الإدراج المباشر يعمل</div>";
    } else {
        echo "<div class='alert alert-danger'>❌ الإدراج المباشر فشل</div>";
    }
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>❌ خطأ في الإدراج المباشر: " . $e->getMessage() . "</div>";
}

// فحص error log
echo "<h4>📝 فحص سجل الأخطاء:</h4>";
$error_log_path = ini_get('error_log');
if ($error_log_path && file_exists($error_log_path)) {
    $errors = file_get_contents($error_log_path);
    $recent_errors = array_slice(explode("\n", $errors), -10);
    
    echo "<div class='alert alert-info'>";
    echo "<strong>آخر 10 أخطاء:</strong><br>";
    foreach ($recent_errors as $error) {
        if (!empty(trim($error))) {
            echo "<small>" . htmlspecialchars($error) . "</small><br>";
        }
    }
    echo "</div>";
} else {
    echo "<div class='alert alert-warning'>لا يمكن الوصول لسجل الأخطاء</div>";
}

// إصلاح تلقائي
echo "<h4>🔧 إصلاح تلقائي:</h4>";
if (isset($_POST['fix_activity_logger'])) {
    try {
        // إعادة إنشاء الجدول
        $conn->exec("DROP TABLE IF EXISTS activity_logs");
        $conn->exec("
            CREATE TABLE activity_logs (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT DEFAULT NULL,
                action VARCHAR(255) NOT NULL,
                description TEXT,
                ip_address VARCHAR(45),
                user_agent TEXT,
                additional_data JSON DEFAULT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                
                INDEX idx_user_id (user_id),
                INDEX idx_action (action),
                INDEX idx_created_at (created_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        
        echo "<div class='alert alert-success'>✅ تم إعادة إنشاء جدول activity_logs</div>";
        
        // اختبار الدالة مرة أخرى
        $result = logUserActivity('إصلاح النظام', 'تم إصلاح جدول الأنشطة');
        if ($result) {
            echo "<div class='alert alert-success'>✅ دالة logUserActivity تعمل الآن!</div>";
        }
        
    } catch (Exception $e) {
        echo "<div class='alert alert-danger'>❌ فشل الإصلاح: " . $e->getMessage() . "</div>";
    }
}

echo "<form method='POST' class='mt-3'>";
echo "<button type='submit' name='fix_activity_logger' class='btn btn-warning'>🔧 إصلاح تلقائي</button>";
echo "</form>";

echo "<div class='mt-4'>";
echo "<a href='test_add_course.php' class='btn btn-primary btn-lg me-2'>🧪 العودة للاختبار</a>";
echo "<a href='instructor/add-course.php' class='btn btn-success btn-lg me-2'>➕ إضافة كورس</a>";
echo "<a href='admin/activity-logs.php' class='btn btn-info btn-lg'>📋 سجل الأنشطة</a>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>
