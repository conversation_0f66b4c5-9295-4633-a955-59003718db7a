<?php
require_once '../config/database.php';
require_once '../includes/functions.php';

if (!isLoggedIn() || !isAdmin()) {
    redirect('../login.php');
}

// Get all students
$stmt = $conn->query("
    SELECT id, name, email, phone, status, created_at,
           (SELECT COUNT(*) FROM sessions WHERE start_time <= NOW()) as attended_sessions
    FROM users 
    WHERE role = 'student'
    ORDER BY created_at DESC
");
$students = $stmt->fetchAll();
$pageTitle = 'لوحة التحكم';
require_once '../includes/header.php';
?>
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الطلاب - المدير</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5>قائمة الطلاب</h5>
                <div class="btn-group">
                    <button type="button" class="btn btn-outline-primary" onclick="filterStudents('all')">الكل</button>
                    <button type="button" class="btn btn-outline-warning" onclick="filterStudents('pending')">معلق</button>
                    <button type="button" class="btn btn-outline-success" onclick="filterStudents('approved')">مقبول</button>
                </div>
            </div>
            <div class="card-body">
                <?php if (count($students) > 0): ?>
                    <div class="table-responsive">
                        <table class="table" id="studentsTable">
                            <thead>
                                <tr>
                                    <th>الاسم</th>
                                    <th>البريد الإلكتروني</th>
                                    <th>رقم الهاتف</th>
                                    <th>الحالة</th>
                                    <th>تاريخ التسجيل</th>
                                    <th>عدد الجلسات</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($students as $student): ?>
                                    <tr data-status="<?php echo $student['status']; ?>">
                                        <td><?php echo htmlspecialchars($student['name']); ?></td>
                                        <td><?php echo htmlspecialchars($student['email']); ?></td>
                                        <td>
                                            <a href="<?php echo generateWhatsAppLink($student['phone'], 'مرحباً'); ?>" 
                                               target="_blank" 
                                               class="text-decoration-none">
                                                <?php echo htmlspecialchars($student['phone']); ?>
                                                <i class="fab fa-whatsapp text-success"></i>
                                            </a>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?php echo $student['status'] === 'approved' ? 'success' : 'warning'; ?>">
                                                <?php echo $student['status'] === 'approved' ? 'مقبول' : 'معلق'; ?>
                                            </span>
                                        </td>
                                        <td><?php echo date('Y-m-d', strtotime($student['created_at'])); ?></td>
                                        <td><?php echo $student['attended_sessions']; ?></td>
                                        <td>
                                            <?php if ($student['status'] === 'pending'): ?>
                                                <a href="approve_student.php?id=<?php echo $student['id']; ?>" 
                                                   class="btn btn-success btn-sm">
                                                    قبول
                                                </a>
                                                <a href="reject_student.php?id=<?php echo $student['id']; ?>" 
                                                   class="btn btn-danger btn-sm">
                                                    رفض
                                                </a>
                                            <?php else: ?>
                                                <button type="button" 
                                                        class="btn btn-info btn-sm" 
                                                        onclick="viewStudentSessions(<?php echo $student['id']; ?>)">
                                                    عرض الجلسات
                                                </button>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <p class="text-center">لا يوجد طلاب مسجلين</p>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function filterStudents(status) {
            const rows = document.querySelectorAll('#studentsTable tbody tr');
            rows.forEach(row => {
                if (status === 'all' || row.dataset.status === status) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        }

        function viewStudentSessions(studentId) {
            // You can implement this to show a modal with student's sessions
            alert('سيتم تنفيذ هذه الميزة قريباً');
        }
    </script>
</body>
</html>
