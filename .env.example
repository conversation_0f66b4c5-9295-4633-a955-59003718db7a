# ملف متغيرات البيئة النموذجي
# Example Environment Variables File
# ==================================
# انسخ هذا الملف إلى .env وقم بتعديل القيم حسب بيئتك
# Copy this file to .env and modify the values according to your environment

# إعدادات التطبيق الأساسية
# Basic Application Settings
APP_ENV=development
APP_DEBUG=true
APP_URL=http://localhost/Zoom

# إعدادات قاعدة البيانات
# Database Settings
DB_HOST=localhost
DB_NAME=zoom_learning_system
DB_USER=root
DB_PASS=

# إعدادات الموقع
# Site Settings
SITE_NAME="منصة التعلم الإلكتروني"
SITE_URL="http://localhost/Zoom"
SITE_EMAIL="<EMAIL>"
SITE_PHONE="+966-50-000-0000"

# إعدادات الأمان
# Security Settings
ENCRYPTION_KEY="your-32-character-secret-key-here"
JWT_SECRET="your-jwt-secret-key-here"

# إعدادات Zoom API
# Zoom API Settings
ZOOM_API_KEY="YOUR_ZOOM_API_KEY"
ZOOM_API_SECRET="YOUR_ZOOM_API_SECRET"
ZOOM_WEBHOOK_SECRET="your_zoom_webhook_secret"

# إعدادات Stripe للدفع
# Stripe Payment Settings
STRIPE_PUBLISHABLE_KEY="pk_test_your_stripe_publishable_key"
STRIPE_SECRET_KEY="sk_test_your_stripe_secret_key"
STRIPE_WEBHOOK_SECRET="whsec_your_webhook_secret"

# إعدادات PayPal للدفع
# PayPal Payment Settings
PAYPAL_CLIENT_ID="your_paypal_client_id"
PAYPAL_CLIENT_SECRET="your_paypal_client_secret"

# إعدادات البريد الإلكتروني
# Email Settings
MAIL_DRIVER=smtp
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_ENCRYPTION=tls
SMTP_USERNAME="<EMAIL>"
SMTP_PASSWORD="your-app-password"
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="منصة التعلم الإلكتروني"

# إعدادات التحليلات
# Analytics Settings
GOOGLE_ANALYTICS_ID=""
FACEBOOK_PIXEL_ID=""

# إعدادات وسائل التواصل الاجتماعي
# Social Media Settings
SOCIAL_FACEBOOK=""
SOCIAL_TWITTER=""
SOCIAL_LINKEDIN=""
SOCIAL_YOUTUBE=""
SOCIAL_INSTAGRAM=""

# إعدادات Firebase للإشعارات
# Firebase Push Notifications Settings
FIREBASE_SERVER_KEY=""
FIREBASE_SENDER_ID=""

# إعدادات CDN
# CDN Settings
CDN_URL=""

# إعدادات التخزين السحابي
# Cloud Storage Settings
AWS_ACCESS_KEY_ID=""
AWS_SECRET_ACCESS_KEY=""
AWS_DEFAULT_REGION=""
AWS_BUCKET=""

# إعدادات Redis (اختيارية)
# Redis Settings (Optional)
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

# إعدادات Memcached (اختيارية)
# Memcached Settings (Optional)
MEMCACHED_HOST=127.0.0.1
MEMCACHED_PORT=11211

# إعدادات SMS (اختيارية)
# SMS Settings (Optional)
SMS_PROVIDER=""
SMS_API_KEY=""
SMS_SENDER_ID=""

# إعدادات النسخ الاحتياطي
# Backup Settings
BACKUP_ENABLED=true
BACKUP_SCHEDULE=daily

# إعدادات المراقبة والسجلات
# Monitoring and Logging Settings
LOG_LEVEL=debug
SENTRY_DSN=""

# إعدادات الأداء
# Performance Settings
ENABLE_CACHING=true
ENABLE_COMPRESSION=true
ENABLE_MINIFICATION=false

# إعدادات التطوير
# Development Settings
TELESCOPE_ENABLED=false
DEBUGBAR_ENABLED=false
