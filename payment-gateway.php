<?php
require_once 'config/database.php';
require_once 'includes/functions.php';
require_once 'includes/session_config.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn() || !isStudent()) {
    header('Location: login.php');
    exit;
}

$course_id = isset($_GET['course_id']) ? (int)$_GET['course_id'] : 0;
$error = '';
$success = '';

// جلب معلومات الكورس
try {
    $stmt = $conn->prepare("
        SELECT c.*, u.name as instructor_name
        FROM courses c
        JOIN users u ON c.instructor_id = u.id
        WHERE c.id = ? AND c.status = 'active' AND c.course_type = 'paid'
    ");
    $stmt->execute([$course_id]);
    $course = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$course) {
        header('Location: courses.php');
        exit;
    }
} catch (PDOException $e) {
    header('Location: courses.php');
    exit;
}

// التحقق من عدم وجود تسجيل سابق
try {
    $stmt = $conn->prepare("
        SELECT * FROM course_enrollments 
        WHERE course_id = ? AND student_id = ?
    ");
    $stmt->execute([$course_id, $_SESSION['user_id']]);
    $existing_enrollment = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($existing_enrollment) {
        if ($existing_enrollment['status'] === 'active') {
            header('Location: student/course-content.php?course_id=' . $course_id);
            exit;
        }
    }
} catch (PDOException $e) {
    // تجاهل الخطأ
}

// معالجة الدفع
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['process_payment'])) {
    $payment_method = $_POST['payment_method'] ?? 'credit_card';
    $cardholder_name = trim($_POST['cardholder_name'] ?? '');
    $card_number = trim($_POST['card_number'] ?? '');
    $expiry_month = $_POST['expiry_month'] ?? '';
    $expiry_year = $_POST['expiry_year'] ?? '';
    $cvv = trim($_POST['cvv'] ?? '');
    
    // التحقق من البيانات
    if (empty($cardholder_name) || empty($card_number) || empty($expiry_month) || empty($expiry_year) || empty($cvv)) {
        $error = 'يرجى ملء جميع بيانات البطاقة';
    } elseif (strlen($card_number) < 16) {
        $error = 'رقم البطاقة غير صحيح';
    } elseif (strlen($cvv) < 3) {
        $error = 'رمز الأمان غير صحيح';
    } else {
        try {
            // محاكاة عملية الدفع (في التطبيق الحقيقي، ستتم معالجة الدفع مع بوابة دفع حقيقية)
            $transaction_id = 'TXN_' . time() . '_' . $_SESSION['user_id'] . '_' . $course_id;
            
            // إنشاء سجل الدفع
            $stmt = $conn->prepare("
                INSERT INTO course_payments (student_id, course_id, amount, currency, payment_method, payment_status, transaction_id, payment_date)
                VALUES (?, ?, ?, ?, ?, 'completed', ?, NOW())
            ");
            $stmt->execute([
                $_SESSION['user_id'],
                $course_id,
                $course['price'],
                $course['currency'],
                $payment_method,
                $transaction_id
            ]);
            $payment_id = $conn->lastInsertId();
            
            // إنشاء التسجيل في الكورس
            $stmt = $conn->prepare("
                INSERT INTO course_enrollments (student_id, course_id, enrollment_date, status, payment_id)
                VALUES (?, ?, NOW(), 'active', ?)
            ");
            $stmt->execute([$_SESSION['user_id'], $course_id, $payment_id]);
            
            $success = 'تم الدفع بنجاح! مرحباً بك في الكورس';
            
            // إعادة توجيه لصفحة الكورس
            header('refresh:3;url=student/course-content.php?course_id=' . $course_id);
            
        } catch (PDOException $e) {
            $error = 'حدث خطأ أثناء معالجة الدفع. يرجى المحاولة مرة أخرى';
        }
    }
}

$pageTitle = 'إتمام الدفع - ' . $course['title'];
?>

<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($pageTitle); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .payment-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            margin-top: 30px;
        }
        
        .payment-header {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .course-summary {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
        }
        
        .payment-form {
            padding: 40px;
        }
        
        .card-input {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 15px;
            font-size: 1.1rem;
            transition: all 0.3s ease;
        }
        
        .card-input:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
            background: white;
        }
        
        .payment-method {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 15px;
        }
        
        .payment-method:hover {
            border-color: #667eea;
        }
        
        .payment-method.selected {
            border-color: #667eea;
            background: #f8f9fa;
        }
        
        .btn-pay {
            background: linear-gradient(45deg, #28a745, #20c997);
            border: none;
            border-radius: 25px;
            padding: 15px 40px;
            font-size: 1.2rem;
            font-weight: bold;
            color: white;
            width: 100%;
            transition: all 0.3s ease;
        }
        
        .btn-pay:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(40, 167, 69, 0.3);
            color: white;
        }
        
        .security-badges {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 20px;
            opacity: 0.7;
        }
        
        .security-badge {
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 0.9rem;
            color: #6c757d;
        }
        
        .price-breakdown {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-graduation-cap text-primary me-2"></i>
                منصة التعلم
            </a>
            
            <div class="d-flex">
                <a href="course-details.php?id=<?php echo $course_id; ?>" class="btn btn-outline-primary">
                    <i class="fas fa-arrow-right me-1"></i>
                    العودة للكورس
                </a>
            </div>
        </div>
    </nav>

    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <?php if ($error): ?>
                    <div class="alert alert-danger mt-4">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <?php echo htmlspecialchars($error); ?>
                    </div>
                <?php endif; ?>
                
                <?php if ($success): ?>
                    <div class="alert alert-success mt-4">
                        <i class="fas fa-check-circle me-2"></i>
                        <?php echo htmlspecialchars($success); ?>
                        <div class="mt-2">
                            <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                            جاري التوجيه للكورس...
                        </div>
                    </div>
                <?php endif; ?>

                <div class="payment-container">
                    <!-- رأس الدفع -->
                    <div class="payment-header">
                        <i class="fas fa-credit-card fa-3x mb-3"></i>
                        <h2>إتمام الدفع</h2>
                        <p class="mb-0">دفع آمن ومحمي بتشفير SSL</p>
                    </div>

                    <div class="payment-form">
                        <!-- ملخص الكورس -->
                        <div class="course-summary">
                            <div class="row align-items-center">
                                <div class="col-md-8">
                                    <h5 class="mb-1"><?php echo htmlspecialchars($course['title']); ?></h5>
                                    <p class="text-muted mb-0">المدرب: <?php echo htmlspecialchars($course['instructor_name']); ?></p>
                                </div>
                                <div class="col-md-4 text-end">
                                    <h4 class="text-success mb-0">
                                        <?php echo number_format($course['price'], 0); ?> 
                                        <?php echo $course['currency']; ?>
                                    </h4>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- نموذج الدفع -->
                            <div class="col-md-8">
                                <form method="POST" id="paymentForm">
                                    <!-- طرق الدفع -->
                                    <h5 class="mb-3">اختر طريقة الدفع</h5>
                                    
                                    <div class="payment-method selected" data-method="credit_card">
                                        <div class="d-flex align-items-center">
                                            <input type="radio" name="payment_method" value="credit_card" checked class="me-3">
                                            <i class="fas fa-credit-card fa-2x text-primary me-3"></i>
                                            <div>
                                                <h6 class="mb-0">بطاقة ائتمان</h6>
                                                <small class="text-muted">Visa, MasterCard, American Express</small>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="payment-method" data-method="bank_transfer">
                                        <div class="d-flex align-items-center">
                                            <input type="radio" name="payment_method" value="bank_transfer" class="me-3">
                                            <i class="fas fa-university fa-2x text-success me-3"></i>
                                            <div>
                                                <h6 class="mb-0">تحويل بنكي</h6>
                                                <small class="text-muted">تحويل مباشر من البنك</small>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- بيانات البطاقة -->
                                    <div id="cardDetails" class="mt-4">
                                        <h5 class="mb-3">بيانات البطاقة</h5>
                                        
                                        <div class="mb-3">
                                            <label class="form-label">اسم حامل البطاقة</label>
                                            <input type="text" name="cardholder_name" class="form-control card-input" 
                                                   placeholder="الاسم كما هو مكتوب على البطاقة" required>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label class="form-label">رقم البطاقة</label>
                                            <input type="text" name="card_number" class="form-control card-input" 
                                                   placeholder="1234 5678 9012 3456" maxlength="19" required>
                                        </div>
                                        
                                        <div class="row">
                                            <div class="col-md-8">
                                                <label class="form-label">تاريخ الانتهاء</label>
                                                <div class="row">
                                                    <div class="col-6">
                                                        <select name="expiry_month" class="form-select card-input" required>
                                                            <option value="">الشهر</option>
                                                            <?php for ($i = 1; $i <= 12; $i++): ?>
                                                                <option value="<?php echo sprintf('%02d', $i); ?>">
                                                                    <?php echo sprintf('%02d', $i); ?>
                                                                </option>
                                                            <?php endfor; ?>
                                                        </select>
                                                    </div>
                                                    <div class="col-6">
                                                        <select name="expiry_year" class="form-select card-input" required>
                                                            <option value="">السنة</option>
                                                            <?php for ($i = date('Y'); $i <= date('Y') + 10; $i++): ?>
                                                                <option value="<?php echo $i; ?>"><?php echo $i; ?></option>
                                                            <?php endfor; ?>
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <label class="form-label">رمز الأمان</label>
                                                <input type="text" name="cvv" class="form-control card-input" 
                                                       placeholder="123" maxlength="4" required>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- زر الدفع -->
                                    <div class="mt-4">
                                        <button type="submit" name="process_payment" class="btn btn-pay">
                                            <i class="fas fa-lock me-2"></i>
                                            ادفع <?php echo number_format($course['price'], 0); ?> <?php echo $course['currency']; ?> بأمان
                                        </button>
                                    </div>
                                </form>
                            </div>

                            <!-- ملخص الطلب -->
                            <div class="col-md-4">
                                <div class="price-breakdown">
                                    <h6 class="mb-3">ملخص الطلب</h6>
                                    
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>سعر الكورس:</span>
                                        <span><?php echo number_format($course['price'], 0); ?> <?php echo $course['currency']; ?></span>
                                    </div>
                                    
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>الضرائب:</span>
                                        <span>0 <?php echo $course['currency']; ?></span>
                                    </div>
                                    
                                    <hr>
                                    
                                    <div class="d-flex justify-content-between">
                                        <strong>المجموع:</strong>
                                        <strong class="text-success">
                                            <?php echo number_format($course['price'], 0); ?> <?php echo $course['currency']; ?>
                                        </strong>
                                    </div>
                                    
                                    <div class="mt-3 pt-3 border-top">
                                        <small class="text-muted">
                                            <i class="fas fa-shield-alt me-1"></i>
                                            ضمان استرداد المال خلال 30 يوم
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- شارات الأمان -->
                        <div class="security-badges">
                            <div class="security-badge">
                                <i class="fas fa-lock"></i>
                                <span>تشفير SSL</span>
                            </div>
                            <div class="security-badge">
                                <i class="fas fa-shield-alt"></i>
                                <span>دفع آمن</span>
                            </div>
                            <div class="security-badge">
                                <i class="fas fa-credit-card"></i>
                                <span>جميع البطاقات</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تنسيق رقم البطاقة
        document.querySelector('input[name="card_number"]').addEventListener('input', function(e) {
            let value = e.target.value.replace(/\s/g, '').replace(/[^0-9]/gi, '');
            let formattedValue = value.match(/.{1,4}/g)?.join(' ') || value;
            e.target.value = formattedValue;
        });

        // تحديد طريقة الدفع
        document.querySelectorAll('.payment-method').forEach(method => {
            method.addEventListener('click', function() {
                document.querySelectorAll('.payment-method').forEach(m => m.classList.remove('selected'));
                this.classList.add('selected');
                this.querySelector('input[type="radio"]').checked = true;
                
                const cardDetails = document.getElementById('cardDetails');
                if (this.dataset.method === 'credit_card') {
                    cardDetails.style.display = 'block';
                } else {
                    cardDetails.style.display = 'none';
                }
            });
        });

        // تأكيد الدفع
        document.getElementById('paymentForm').addEventListener('submit', function(e) {
            const submitBtn = this.querySelector('button[type="submit"]');
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري المعالجة...';
            submitBtn.disabled = true;
        });
    </script>
</body>
</html>
