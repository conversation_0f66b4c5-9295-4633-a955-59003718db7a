<?php
require_once 'config/database.php';
require_once 'includes/functions.php';

if (!isLoggedIn()) {
    redirect('login.php');
}

if (isAdmin()) {
    redirect('admin/dashboard.php');
}

// Get user info
$stmt = $conn->prepare("SELECT * FROM users WHERE id = ?");
$stmt->execute([$_SESSION['user_id']]);
$user = $stmt->fetch();

// Get sessions
$stmt = $conn->query("SELECT * FROM sessions WHERE start_time >= NOW() ORDER BY start_time ASC");
$sessions = $stmt->fetchAll();

$pageTitle = 'لوحة التحكم - الطالب';
require_once 'includes/header.php';
?>

<div class="container py-4">
    <div class="row">
        <div class="col-md-12">
            <?php if ($user['status'] === 'pending'): ?>
                <div class="alert alert-warning">
                    <h4><i class="fas fa-clock"></i> حسابك قيد المراجعة</h4>
                    <p>يرجى انتظار موافقة المدير للوصول إلى الجلسات.</p>
                </div>
            <?php else: ?>
                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0"><i class="fas fa-video me-2"></i> الجلسات المتاحة</h5>
                    </div>
                    <div class="card-body">
                        <?php if (count($sessions) > 0): ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>الموضوع</th>
                                            <th>التاريخ</th>
                                            <th>الوقت</th>
                                            <th>الرابط</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($sessions as $session): ?>
                                            <tr>
                                                <td><?php echo htmlspecialchars($session['topic']); ?></td>
                                                <td><?php echo date('Y-m-d', strtotime($session['start_time'])); ?></td>
                                                <td><?php echo date('H:i', strtotime($session['start_time'])); ?></td>
                                                <td>
                                                    <a href="<?php echo htmlspecialchars($session['zoom_link']); ?>" target="_blank" class="btn btn-primary btn-sm">
                                                        <i class="fas fa-video"></i> دخول
                                                    </a>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="alert alert-info mb-0">
                                <i class="fas fa-info-circle"></i> لا توجد جلسات متاحة حالياً.
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>
        <div class="row">
            <div class="col-md-12">
                <?php if ($user['status'] === 'pending'): ?>
                    <div class="alert alert-warning">
                        <h4>حسابك قيد المراجعة</h4>
                        <p>يرجى انتظار موافقة المدير للوصول إلى الجلسات.</p>
                    </div>
                <?php else: ?>
                    <div class="card">
                        <div class="card-header">
                            <h5>الجلسات المتاحة</h5>
                        </div>
                        <div class="card-body">
                            <?php if (count($sessions) > 0): ?>
                                <div class="table-responsive">
                                    <table class="table">
                                        <thead>
                                            <tr>
                                                <th>الموضوع</th>
                                                <th>التاريخ</th>
                                                <th>الوقت</th>
                                                <th>الرابط</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($sessions as $session): ?>
                                                <tr>
                                                    <td><?php echo htmlspecialchars($session['topic']); ?></td>
                                                    <td><?php echo date('Y-m-d', strtotime($session['start_time'])); ?></td>
                                                    <td><?php echo date('H:i', strtotime($session['start_time'])); ?></td>
                                                    <td>
                                                        <a href="<?php echo htmlspecialchars($session['zoom_link']); ?>" 
                                                           target="_blank" 
                                                           class="btn btn-primary btn-sm">
                                                            دخول الجلسة
                                                        </a>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php else: ?>
                                <p class="text-center">لا توجد جلسات متاحة حالياً</p>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
