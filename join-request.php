<?php
require_once 'config/database.php';
require_once 'includes/functions.php';

$error = '';
$success = '';

// جلب الكورسات المتاحة
try {
    $stmt = $conn->prepare("SELECT id, title FROM courses WHERE status = 'active' ORDER BY title");
    $stmt->execute();
    $courses = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $courses = [];
    error_log("Error fetching courses: " . $e->getMessage());
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = sanitize($_POST['name']);
    $email = filter_var($_POST['email'], FILTER_SANITIZE_EMAIL);
    $phone = sanitize($_POST['phone']);
    $course_id = isset($_POST['course_id']) ? (int)$_POST['course_id'] : null;
    $message = sanitize($_POST['message'] ?? '');
    
    // التحقق من البيانات
    if (empty($name) || empty($email) || empty($phone)) {
        $error = 'جميع الحقول مطلوبة';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = 'البريد الإلكتروني غير صالح';
    } elseif (!preg_match('/^[0-9]{10}$/', $phone)) {
        $error = 'رقم الهاتف غير صالح (10 أرقام)';
    } else {
        try {
            // التحقق من وجود طلب سابق
            $stmt = $conn->prepare("SELECT id FROM join_requests WHERE email = ? AND status = 'pending'");
            $stmt->execute([$email]);
            
            if ($stmt->rowCount() > 0) {
                $error = 'لديك طلب انضمام معلق بالفعل';
            } else {
                // إدراج طلب الانضمام
                $stmt = $conn->prepare("INSERT INTO join_requests (name, email, phone, course_id, message, status, created_at) VALUES (?, ?, ?, ?, ?, 'pending', NOW())");
                $result = $stmt->execute([$name, $email, $phone, $course_id, $message]);
                
                if ($result) {
                    $success = 'تم إرسال طلب الانضمام بنجاح. سيتم مراجعته من قبل الإدارة';
                    
                    // مسح البيانات بعد الإرسال الناجح
                    $_POST = [];
                } else {
                    $error = 'فشل في إرسال الطلب';
                }
            }
        } catch (Exception $e) {
            $error = 'حدث خطأ في إرسال الطلب: ' . $e->getMessage();
            error_log("Join request error: " . $e->getMessage());
        }
    }
}
?>
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>طلب انضمام - منصة التعلم</title>
    
    <!-- Bootstrap و Font Awesome -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/main.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Cairo', sans-serif;
        }
        
        .join-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            max-width: 600px;
            margin: 50px auto;
        }
        
        .join-header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .join-body {
            padding: 40px;
        }
        
        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            margin-bottom: 20px;
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .btn-submit {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            border-radius: 50px;
            padding: 15px 40px;
            color: white;
            font-weight: bold;
            transition: all 0.3s;
        }
        
        .btn-submit:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="join-card">
            <div class="join-header">
                <h2><i class="fas fa-user-plus me-3"></i>طلب انضمام للمنصة</h2>
                <p class="mb-0">املأ البيانات التالية لإرسال طلب الانضمام</p>
            </div>
            
            <div class="join-body">
                <?php if ($error): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <?php echo htmlspecialchars($error); ?>
                    </div>
                <?php endif; ?>
                
                <?php if ($success): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i>
                        <?php echo htmlspecialchars($success); ?>
                    </div>
                <?php endif; ?>
                
                <form method="POST" action="">
                    <div class="mb-3">
                        <label for="name" class="form-label">
                            <i class="fas fa-user me-2"></i>الاسم الكامل
                        </label>
                        <input type="text" class="form-control" id="name" name="name" 
                               value="<?php echo isset($_POST['name']) ? htmlspecialchars($_POST['name']) : ''; ?>" 
                               required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="email" class="form-label">
                            <i class="fas fa-envelope me-2"></i>البريد الإلكتروني
                        </label>
                        <input type="email" class="form-control" id="email" name="email" 
                               value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>" 
                               required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="phone" class="form-label">
                            <i class="fas fa-phone me-2"></i>رقم الهاتف
                        </label>
                        <input type="tel" class="form-control" id="phone" name="phone" 
                               value="<?php echo isset($_POST['phone']) ? htmlspecialchars($_POST['phone']) : ''; ?>" 
                               pattern="[0-9]{10}" placeholder="مثال: 0501234567" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="course_id" class="form-label">
                            <i class="fas fa-book me-2"></i>الكورس المطلوب (اختياري)
                        </label>
                        <select class="form-control" id="course_id" name="course_id">
                            <option value="">اختر كورس (اختياري)</option>
                            <?php foreach ($courses as $course): ?>
                                <option value="<?php echo $course['id']; ?>" 
                                        <?php echo (isset($_POST['course_id']) && $_POST['course_id'] == $course['id']) ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($course['title']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="message" class="form-label">
                            <i class="fas fa-comment me-2"></i>رسالة إضافية (اختياري)
                        </label>
                        <textarea class="form-control" id="message" name="message" rows="4" 
                                  placeholder="أخبرنا عن اهتماماتك أو أي معلومات إضافية..."><?php echo isset($_POST['message']) ? htmlspecialchars($_POST['message']) : ''; ?></textarea>
                    </div>
                    
                    <div class="text-center">
                        <button type="submit" class="btn btn-submit">
                            <i class="fas fa-paper-plane me-2"></i>إرسال الطلب
                        </button>
                    </div>
                </form>
                
                <div class="text-center mt-4">
                    <p class="text-muted">لديك حساب بالفعل؟</p>
                    <a href="login.php" class="btn btn-outline-primary">
                        <i class="fas fa-sign-in-alt me-1"></i>تسجيل الدخول
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
