<?php
/**
 * إصلاح هيكل قاعدة البيانات
 * Fix Database Structure
 */

require_once 'config/database.php';

echo "<!DOCTYPE html>";
echo "<html dir='rtl' lang='ar'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>إصلاح هيكل قاعدة البيانات</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "</head>";
echo "<body>";
echo "<div class='container mt-4'>";
echo "<h2>🔧 إصلاح هيكل قاعدة البيانات</h2>";

try {
    // 1. إصلاح جدول courses
    echo "<h4>📚 إصلاح جدول courses...</h4>";
    
    // التحقق من وجود عمود image_path
    $stmt = $conn->query("SHOW COLUMNS FROM courses LIKE 'image_path'");
    if ($stmt->rowCount() == 0) {
        $conn->exec("ALTER TABLE courses ADD COLUMN image_path VARCHAR(255) DEFAULT NULL");
        echo "<div class='alert alert-success'>✅ تم إضافة عمود image_path</div>";
    } else {
        echo "<div class='alert alert-info'>ℹ️ عمود image_path موجود مسبقاً</div>";
    }
    
    // التحقق من وجود عمود category_id
    $stmt = $conn->query("SHOW COLUMNS FROM courses LIKE 'category_id'");
    if ($stmt->rowCount() == 0) {
        $conn->exec("ALTER TABLE courses ADD COLUMN category_id INT DEFAULT NULL");
        echo "<div class='alert alert-success'>✅ تم إضافة عمود category_id</div>";
    } else {
        echo "<div class='alert alert-info'>ℹ️ عمود category_id موجود مسبقاً</div>";
    }

    // 2. إنشاء جدول categories
    echo "<h4>📋 إنشاء جدول categories...</h4>";
    $conn->exec("
        CREATE TABLE IF NOT EXISTS categories (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            description TEXT,
            icon VARCHAR(100) DEFAULT NULL,
            color VARCHAR(7) DEFAULT '#007bff',
            status ENUM('active', 'inactive') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            
            INDEX idx_name (name),
            INDEX idx_status (status)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "<div class='alert alert-success'>✅ تم إنشاء جدول categories</div>";

    // إضافة تصنيفات أساسية
    $stmt = $conn->query("SELECT COUNT(*) FROM categories");
    if ($stmt->fetchColumn() == 0) {
        $categories = [
            ['البرمجة وتطوير المواقع', 'تعلم لغات البرمجة وتطوير تطبيقات الويب', 'fas fa-code', '#007bff'],
            ['التصميم الجرافيكي', 'تصميم الشعارات والمواد الإعلانية', 'fas fa-paint-brush', '#28a745'],
            ['التسويق الرقمي', 'استراتيجيات التسويق عبر الإنترنت', 'fas fa-bullhorn', '#dc3545'],
            ['إدارة الأعمال', 'مهارات إدارة المشاريع والفرق', 'fas fa-briefcase', '#ffc107'],
            ['اللغات', 'تعلم اللغات المختلفة', 'fas fa-language', '#17a2b8']
        ];

        $insertCategory = $conn->prepare("INSERT INTO categories (name, description, icon, color) VALUES (?, ?, ?, ?)");
        foreach ($categories as $category) {
            $insertCategory->execute($category);
        }
        echo "<div class='alert alert-success'>✅ تم إضافة " . count($categories) . " تصنيف</div>";
    }

    // 3. إنشاء جدول sessions
    echo "<h4>🎥 إنشاء جدول sessions...</h4>";
    $conn->exec("
        CREATE TABLE IF NOT EXISTS sessions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            course_id INT NOT NULL,
            title VARCHAR(255) NOT NULL,
            description TEXT,
            session_date DATE NOT NULL,
            start_time TIME NOT NULL,
            end_time TIME NOT NULL,
            zoom_link VARCHAR(500),
            meeting_id VARCHAR(100),
            passcode VARCHAR(50),
            status ENUM('scheduled', 'ongoing', 'completed', 'cancelled') DEFAULT 'scheduled',
            max_attendees INT DEFAULT 100,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            
            FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
            INDEX idx_course_id (course_id),
            INDEX idx_session_date (session_date),
            INDEX idx_status (status)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "<div class='alert alert-success'>✅ تم إنشاء جدول sessions</div>";

    // 4. إنشاء جدول join_requests
    echo "<h4>📝 إنشاء جدول join_requests...</h4>";
    $conn->exec("
        CREATE TABLE IF NOT EXISTS join_requests (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            email VARCHAR(255) NOT NULL,
            phone VARCHAR(20),
            course_interest TEXT,
            message TEXT,
            status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
            assigned_course_id INT DEFAULT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            
            INDEX idx_status (status),
            INDEX idx_email (email),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "<div class='alert alert-success'>✅ تم إنشاء جدول join_requests</div>";

    // 5. إضافة بيانات تجريبية للجلسات
    echo "<h4>🎯 إضافة بيانات تجريبية...</h4>";
    
    // الحصول على الكورسات
    $stmt = $conn->query("SELECT id, title FROM courses LIMIT 3");
    $courses = $stmt->fetchAll();
    
    if (!empty($courses)) {
        // إضافة جلسات تجريبية
        $insertSession = $conn->prepare("
            INSERT INTO sessions (course_id, title, description, session_date, start_time, end_time, zoom_link, meeting_id, status) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        $sessions = [
            ['الجلسة الأولى: مقدمة', 'جلسة تعريفية بالكورس', date('Y-m-d', strtotime('+1 day')), '10:00:00', '11:30:00', 'https://zoom.us/j/123456789', '123456789', 'scheduled'],
            ['الجلسة الثانية: الأساسيات', 'تعلم الأساسيات', date('Y-m-d', strtotime('+3 days')), '14:00:00', '15:30:00', 'https://zoom.us/j/987654321', '987654321', 'scheduled'],
            ['الجلسة الثالثة: التطبيق العملي', 'تطبيق عملي', date('Y-m-d', strtotime('+5 days')), '16:00:00', '17:30:00', 'https://zoom.us/j/456789123', '456789123', 'scheduled']
        ];
        
        foreach ($courses as $course) {
            foreach ($sessions as $session) {
                $insertSession->execute([
                    $course['id'],
                    $session[0] . ' - ' . $course['title'],
                    $session[1],
                    $session[2],
                    $session[3],
                    $session[4],
                    $session[5],
                    $session[6],
                    $session[7]
                ]);
            }
        }
        echo "<div class='alert alert-success'>✅ تم إضافة " . (count($courses) * count($sessions)) . " جلسة تجريبية</div>";
    }

    // 6. إضافة طلبات انضمام تجريبية
    $stmt = $conn->query("SELECT COUNT(*) FROM join_requests");
    if ($stmt->fetchColumn() == 0) {
        $joinRequests = [
            ['علي أحمد السالم', '<EMAIL>', '0501112233', 'تطوير تطبيقات الويب', 'أرغب في تعلم تطوير المواقع الإلكترونية', 'pending'],
            ['مريم عبدالله', '<EMAIL>', '0502223344', 'التصميم الجرافيكي', 'أحب التصميم وأريد تطوير مهاراتي', 'pending'],
            ['يوسف محمد', '<EMAIL>', '0503334455', 'التسويق الرقمي', 'أعمل في التسويق وأريد تطوير خبراتي', 'approved'],
            ['هند سالم', '<EMAIL>', '0504445566', 'إدارة الأعمال', 'أريد تعلم إدارة المشاريع بطريقة احترافية', 'pending'],
            ['عمر خالد', '<EMAIL>', '0505556677', 'اللغات', 'أحتاج تحسين لغتي الإنجليزية للعمل', 'approved']
        ];

        $insertJoinRequest = $conn->prepare("
            INSERT INTO join_requests (name, email, phone, course_interest, message, status) 
            VALUES (?, ?, ?, ?, ?, ?)
        ");

        foreach ($joinRequests as $request) {
            $insertJoinRequest->execute($request);
        }
        echo "<div class='alert alert-success'>✅ تم إضافة " . count($joinRequests) . " طلب انضمام</div>";
    }

    // 7. إنشاء مجلد uploads
    echo "<h4>📁 إنشاء مجلدات الملفات...</h4>";
    $uploadDirs = ['uploads', 'uploads/courses', 'uploads/users'];
    foreach ($uploadDirs as $dir) {
        if (!file_exists($dir)) {
            mkdir($dir, 0777, true);
            echo "<div class='alert alert-success'>✅ تم إنشاء مجلد: $dir</div>";
        } else {
            echo "<div class='alert alert-info'>ℹ️ مجلد موجود: $dir</div>";
        }
    }

    // إحصائيات نهائية
    echo "<h4>📊 الإحصائيات النهائية:</h4>";
    
    $stats = [];
    $stmt = $conn->query("SELECT COUNT(*) FROM courses");
    $stats['courses'] = $stmt->fetchColumn();
    
    $stmt = $conn->query("SELECT COUNT(*) FROM sessions");
    $stats['sessions'] = $stmt->fetchColumn();
    
    $stmt = $conn->query("SELECT COUNT(*) FROM join_requests");
    $stats['join_requests'] = $stmt->fetchColumn();
    
    $stmt = $conn->query("SELECT COUNT(*) FROM categories");
    $stats['categories'] = $stmt->fetchColumn();

    echo "<div class='row'>";
    echo "<div class='col-md-3'><div class='card bg-primary text-white'><div class='card-body text-center'><h3>{$stats['courses']}</h3><p>الكورسات</p></div></div></div>";
    echo "<div class='col-md-3'><div class='card bg-success text-white'><div class='card-body text-center'><h3>{$stats['sessions']}</h3><p>الجلسات</p></div></div></div>";
    echo "<div class='col-md-3'><div class='card bg-warning text-white'><div class='card-body text-center'><h3>{$stats['join_requests']}</h3><p>طلبات الانضمام</p></div></div></div>";
    echo "<div class='col-md-3'><div class='card bg-info text-white'><div class='card-body text-center'><h3>{$stats['categories']}</h3><p>التصنيفات</p></div></div></div>";
    echo "</div>";

    echo "<div class='mt-4'>";
    echo "<a href='instructor/add-course.php' class='btn btn-primary btn-lg me-2'>➕ إضافة كورس</a>";
    echo "<a href='admin/manage_join_requests.php' class='btn btn-warning btn-lg me-2'>📝 طلبات الانضمام</a>";
    echo "<a href='admin/manage-sessions.php' class='btn btn-success btn-lg me-2'>🎥 إدارة الجلسات</a>";
    echo "<a href='admin/activity-logs.php' class='btn btn-info btn-lg'>📋 سجل الأنشطة</a>";
    echo "</div>";

} catch (Exception $e) {
    echo "<div class='alert alert-danger'>";
    echo "<h5>❌ حدث خطأ</h5>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "</div>";
echo "</body>";
echo "</html>";
?>
