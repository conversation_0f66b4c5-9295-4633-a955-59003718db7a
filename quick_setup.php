<?php
/**
 * إعداد سريع للنظام
 * Quick System Setup
 */

// تشغيل إعداد قاعدة البيانات
require_once 'config/database.php';

echo "<!DOCTYPE html>";
echo "<html dir='rtl' lang='ar'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>إعداد سريع - منصة التعلم</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css' rel='stylesheet'>";
echo "<link href='https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap' rel='stylesheet'>";
echo "<style>";
echo "body { font-family: 'Cairo', sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }";
echo ".setup-container { max-width: 900px; margin: 50px auto; padding: 20px; }";
echo ".setup-card { background: white; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); overflow: hidden; }";
echo ".setup-header { background: linear-gradient(135deg, #667eea, #764ba2); color: white; padding: 30px; text-align: center; }";
echo ".setup-body { padding: 30px; }";
echo ".step { padding: 15px; margin: 10px 0; border-radius: 10px; border-right: 4px solid; }";
echo ".step-success { background: #d4edda; color: #155724; border-right-color: #28a745; }";
echo ".step-error { background: #f8d7da; color: #721c24; border-right-color: #dc3545; }";
echo ".step-info { background: #d1ecf1; color: #0c5460; border-right-color: #17a2b8; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='setup-container'>";
echo "<div class='setup-card'>";
echo "<div class='setup-header'>";
echo "<h1><i class='fas fa-rocket me-3'></i>إعداد سريع للنظام</h1>";
echo "<p class='mb-0'>جاري إعداد قاعدة البيانات والبيانات الأساسية...</p>";
echo "</div>";

echo "<div class='setup-body'>";

// خطوة 1: التحقق من الاتصال بقاعدة البيانات
echo "<div class='step step-info'>";
echo "<h5><i class='fas fa-database me-2'></i>الخطوة 1: التحقق من قاعدة البيانات</h5>";
try {
    $stmt = $conn->query("SELECT 1");
    echo "<p class='mb-0'>✅ تم الاتصال بقاعدة البيانات بنجاح</p>";
    echo "</div>";
} catch (Exception $e) {
    echo "<p class='mb-0'>❌ فشل الاتصال بقاعدة البيانات: " . $e->getMessage() . "</p>";
    echo "</div>";
    echo "</div></div></div></body></html>";
    exit;
}

// خطوة 2: التحقق من الجداول
echo "<div class='step step-info'>";
echo "<h5><i class='fas fa-table me-2'></i>الخطوة 2: التحقق من الجداول</h5>";
$tables = ['users', 'courses', 'sessions', 'course_enrollments', 'join_requests', 'course_join_requests'];
$tables_ok = true;

foreach ($tables as $table) {
    try {
        $stmt = $conn->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            echo "<p class='mb-1'>✅ جدول $table موجود</p>";
        } else {
            echo "<p class='mb-1'>❌ جدول $table غير موجود</p>";
            $tables_ok = false;
        }
    } catch (Exception $e) {
        echo "<p class='mb-1'>❌ خطأ في فحص جدول $table</p>";
        $tables_ok = false;
    }
}

if ($tables_ok) {
    echo "<p class='mb-0 fw-bold text-success'>جميع الجداول المطلوبة موجودة</p>";
} else {
    echo "<p class='mb-0 fw-bold text-warning'>بعض الجداول مفقودة - تم إنشاؤها تلقائياً</p>";
}
echo "</div>";

// خطوة 3: إنشاء حساب المدير
echo "<div class='step step-info'>";
echo "<h5><i class='fas fa-user-shield me-2'></i>الخطوة 3: إعداد حساب المدير</h5>";
try {
    $stmt = $conn->prepare("SELECT id FROM users WHERE role = 'admin' LIMIT 1");
    $stmt->execute();
    $admin_exists = $stmt->fetch();
    
    if (!$admin_exists) {
        $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
        $stmt = $conn->prepare("
            INSERT INTO users (name, email, username, password, role, status, email_verified, created_at) 
            VALUES (?, ?, ?, ?, 'admin', 'active', TRUE, NOW())
        ");
        $result = $stmt->execute([
            'مدير النظام',
            '<EMAIL>',
            'admin',
            $admin_password
        ]);
        
        if ($result) {
            echo "<p class='mb-1'>✅ تم إنشاء حساب المدير</p>";
            echo "<p class='mb-1'><strong>البريد الإلكتروني:</strong> <EMAIL></p>";
            echo "<p class='mb-0'><strong>كلمة المرور:</strong> admin123</p>";
        } else {
            echo "<p class='mb-0'>❌ فشل في إنشاء حساب المدير</p>";
        }
    } else {
        echo "<p class='mb-0'>✅ حساب المدير موجود بالفعل</p>";
    }
} catch (Exception $e) {
    echo "<p class='mb-0'>❌ خطأ في إنشاء حساب المدير: " . $e->getMessage() . "</p>";
}
echo "</div>";

// خطوة 4: إنشاء بيانات تجريبية
echo "<div class='step step-info'>";
echo "<h5><i class='fas fa-users me-2'></i>الخطوة 4: إنشاء حسابات تجريبية</h5>";

// مدرب تجريبي
try {
    $stmt = $conn->prepare("SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1");
    $stmt->execute();
    $instructor_exists = $stmt->fetch();
    
    if (!$instructor_exists) {
        $instructor_password = password_hash('instructor123', PASSWORD_DEFAULT);
        $stmt = $conn->prepare("
            INSERT INTO users (name, email, username, phone, password, role, status, email_verified, created_at) 
            VALUES (?, ?, ?, ?, ?, 'instructor', 'active', TRUE, NOW())
        ");
        $result = $stmt->execute([
            'أحمد محمد - مدرب',
            '<EMAIL>',
            'instructor_demo',
            '0501234567',
            $instructor_password
        ]);
        
        if ($result) {
            echo "<p class='mb-1'>✅ تم إنشاء حساب المدرب التجريبي</p>";
            echo "<p class='mb-1'><strong>البريد الإلكتروني:</strong> <EMAIL></p>";
            echo "<p class='mb-1'><strong>كلمة المرور:</strong> instructor123</p>";
        }
    } else {
        echo "<p class='mb-1'>✅ حساب المدرب التجريبي موجود</p>";
    }
} catch (Exception $e) {
    echo "<p class='mb-1'>❌ خطأ في إنشاء حساب المدرب</p>";
}

// طالب تجريبي
try {
    $stmt = $conn->prepare("SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1");
    $stmt->execute();
    $student_exists = $stmt->fetch();
    
    if (!$student_exists) {
        $student_password = password_hash('student123', PASSWORD_DEFAULT);
        $stmt = $conn->prepare("
            INSERT INTO users (name, email, username, phone, password, role, status, email_verified, created_at) 
            VALUES (?, ?, ?, ?, ?, 'student', 'active', TRUE, NOW())
        ");
        $result = $stmt->execute([
            'سارة أحمد - طالبة',
            '<EMAIL>',
            'student_demo',
            '0507654321',
            $student_password
        ]);
        
        if ($result) {
            echo "<p class='mb-0'>✅ تم إنشاء حساب الطالب التجريبي</p>";
            echo "<p class='mb-1'><strong>البريد الإلكتروني:</strong> <EMAIL></p>";
            echo "<p class='mb-0'><strong>كلمة المرور:</strong> student123</p>";
        }
    } else {
        echo "<p class='mb-0'>✅ حساب الطالب التجريبي موجود</p>";
    }
} catch (Exception $e) {
    echo "<p class='mb-0'>❌ خطأ في إنشاء حساب الطالب</p>";
}
echo "</div>";

// خطوة 5: إنشاء كورس تجريبي
echo "<div class='step step-info'>";
echo "<h5><i class='fas fa-book me-2'></i>الخطوة 5: إنشاء محتوى تجريبي</h5>";
try {
    $stmt = $conn->prepare("SELECT id FROM courses WHERE title = 'كورس البرمجة الأساسية' LIMIT 1");
    $stmt->execute();
    $course_exists = $stmt->fetch();
    
    if (!$course_exists) {
        // الحصول على معرف المدرب
        $stmt = $conn->prepare("SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1");
        $stmt->execute();
        $instructor = $stmt->fetch();
        
        if ($instructor) {
            $stmt = $conn->prepare("
                INSERT INTO courses (title, description, instructor_id, max_students, price, status, created_at) 
                VALUES (?, ?, ?, ?, ?, 'active', NOW())
            ");
            $result = $stmt->execute([
                'كورس البرمجة الأساسية',
                'تعلم أساسيات البرمجة من الصفر حتى الاحتراف. يشمل الكورس HTML, CSS, JavaScript وأساسيات قواعد البيانات.',
                $instructor['id'],
                30,
                299.99
            ]);
            
            if ($result) {
                echo "<p class='mb-0'>✅ تم إنشاء كورس تجريبي</p>";
            }
        }
    } else {
        echo "<p class='mb-0'>✅ الكورس التجريبي موجود</p>";
    }
} catch (Exception $e) {
    echo "<p class='mb-0'>❌ خطأ في إنشاء الكورس</p>";
}
echo "</div>";

// النتيجة النهائية
echo "<div class='step step-success'>";
echo "<h5><i class='fas fa-check-circle me-2'></i>تم الإعداد بنجاح!</h5>";
echo "<p class='mb-3'>النظام جاهز للاستخدام الآن</p>";

echo "<div class='row'>";
echo "<div class='col-md-4 mb-3'>";
echo "<div class='card border-primary'>";
echo "<div class='card-body text-center'>";
echo "<h6 class='card-title'>تسجيل الدخول</h6>";
echo "<a href='login.php' class='btn btn-primary'>";
echo "<i class='fas fa-sign-in-alt me-1'></i> دخول";
echo "</a>";
echo "</div></div></div>";

echo "<div class='col-md-4 mb-3'>";
echo "<div class='card border-success'>";
echo "<div class='card-body text-center'>";
echo "<h6 class='card-title'>لوحة الإدارة</h6>";
echo "<a href='admin/dashboard.php' class='btn btn-success'>";
echo "<i class='fas fa-tachometer-alt me-1'></i> إدارة";
echo "</a>";
echo "</div></div></div>";

echo "<div class='col-md-4 mb-3'>";
echo "<div class='card border-info'>";
echo "<div class='card-body text-center'>";
echo "<h6 class='card-title'>تسجيل جديد</h6>";
echo "<a href='register.php' class='btn btn-info'>";
echo "<i class='fas fa-user-plus me-1'></i> تسجيل";
echo "</a>";
echo "</div></div></div>";
echo "</div>";

echo "<div class='alert alert-warning mt-3'>";
echo "<h6><i class='fas fa-exclamation-triangle me-2'></i>تذكير مهم:</h6>";
echo "<ul class='mb-0'>";
echo "<li>قم بتغيير كلمات المرور الافتراضية</li>";
echo "<li>احذف هذا الملف بعد الانتهاء من الإعداد</li>";
echo "<li>تأكد من إعدادات الأمان في الإنتاج</li>";
echo "</ul>";
echo "</div>";

echo "</div>";

echo "</div>";
echo "</div>";
echo "</div>";

echo "</body>";
echo "</html>";
?>
