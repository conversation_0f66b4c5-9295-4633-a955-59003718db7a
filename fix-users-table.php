<?php
require_once 'config/database.php';

echo "<h2>إصلاح جدول المستخدمين</h2>";

try {
    // إضافة عمود phone إذا لم يكن موجود
    $conn->exec("ALTER TABLE users ADD COLUMN IF NOT EXISTS phone VARCHAR(20) NULL");
    echo "<p>✅ تم إضافة عمود phone</p>";
    
    // تعديل عمود username ليكون اختياري
    $conn->exec("ALTER TABLE users MODIFY COLUMN username VARCHAR(50) NULL");
    echo "<p>✅ تم تعديل عمود username ليكون اختياري</p>";
    
    // إضافة حالة pending إذا لم تكن موجودة
    $conn->exec("ALTER TABLE users MODIFY COLUMN status ENUM('active', 'inactive', 'pending') DEFAULT 'active'");
    echo "<p>✅ تم إضافة حالة pending</p>";
    
    // إنشاء جدول join_requests إذا لم يكن موجود
    $conn->exec("CREATE TABLE IF NOT EXISTS join_requests (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        email VARCHAR(255) NOT NULL,
        phone VARCHAR(20) NULL,
        course_id INT NULL,
        message TEXT NULL,
        status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
        processed_by INT NULL,
        processed_at TIMESTAMP NULL,
        rejection_reason TEXT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE SET NULL,
        FOREIGN KEY (processed_by) REFERENCES users(id) ON DELETE SET NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    echo "<p>✅ تم إنشاء جدول join_requests</p>";
    
    // إنشاء جدول session_files إذا لم يكن موجود
    $conn->exec("CREATE TABLE IF NOT EXISTS session_files (
        id INT AUTO_INCREMENT PRIMARY KEY,
        session_id INT NOT NULL,
        file_name VARCHAR(255) NOT NULL,
        file_path VARCHAR(500) NOT NULL,
        file_size INT NOT NULL,
        file_type VARCHAR(100) NOT NULL,
        uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (session_id) REFERENCES sessions(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    echo "<p>✅ تم إنشاء جدول session_files</p>";
    
    echo "<h3>✅ تم إصلاح جميع الجداول بنجاح!</h3>";
    
    // عرض بنية الجدول الحالية
    $stmt = $conn->prepare("DESCRIBE users");
    $stmt->execute();
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>بنية جدول المستخدمين الحالية:</h3>";
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>العمود</th><th>النوع</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ: " . $e->getMessage() . "</p>";
}
?>

<p><a href="register.php">اختبار صفحة التسجيل</a></p>
<p><a href="simple-register.php">اختبار صفحة التسجيل المبسطة</a></p>
