<?php
require_once 'config/database.php';

echo "<h2>إضافة بيانات تجريبية للطلاب</h2>";

try {
    // التحقق من وجود جدول course_enrollments
    $stmt = $conn->query("SHOW TABLES LIKE 'course_enrollments'");
    if ($stmt->rowCount() == 0) {
        echo "<p>إنشاء جدول course_enrollments...</p>";
        $conn->exec("
            CREATE TABLE course_enrollments (
                id INT AUTO_INCREMENT PRIMARY KEY,
                course_id INT NOT NULL,
                student_id INT NOT NULL,
                enrolled_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                status ENUM('active', 'completed', 'dropped', 'suspended') DEFAULT 'active',
                progress_percentage DECIMAL(5,2) DEFAULT 0.00,
                final_grade DECIMAL(5,2) DEFAULT NULL,
                completion_date TIMESTAMP NULL,
                notes TEXT DEFAULT NULL,
                INDEX idx_course_id (course_id),
                INDEX idx_student_id (student_id),
                INDEX idx_status (status),
                UNIQUE KEY unique_enrollment (course_id, student_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        echo "<p style='color: green;'>✅ تم إنشاء جدول course_enrollments</p>";
    }

    // التحقق من وجود جدول student_grades
    $stmt = $conn->query("SHOW TABLES LIKE 'student_grades'");
    if ($stmt->rowCount() == 0) {
        echo "<p>إنشاء جدول student_grades...</p>";
        $conn->exec("
            CREATE TABLE student_grades (
                id INT AUTO_INCREMENT PRIMARY KEY,
                student_id INT NOT NULL,
                course_id INT NOT NULL,
                assignment_name VARCHAR(255) NOT NULL,
                grade DECIMAL(5,2) NOT NULL,
                max_grade DECIMAL(5,2) DEFAULT 100.00,
                graded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                notes TEXT DEFAULT NULL,
                INDEX idx_student_id (student_id),
                INDEX idx_course_id (course_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        echo "<p style='color: green;'>✅ تم إنشاء جدول student_grades</p>";
    }

    // التحقق من وجود جدول session_attendees
    $stmt = $conn->query("SHOW TABLES LIKE 'session_attendees'");
    if ($stmt->rowCount() == 0) {
        echo "<p>إنشاء جدول session_attendees...</p>";
        $conn->exec("
            CREATE TABLE session_attendees (
                id INT AUTO_INCREMENT PRIMARY KEY,
                session_id INT NOT NULL,
                user_id INT NOT NULL,
                join_time TIMESTAMP NULL,
                leave_time TIMESTAMP NULL,
                attendance_status ENUM('present', 'absent', 'late') DEFAULT 'present',
                INDEX idx_session_id (session_id),
                INDEX idx_user_id (user_id),
                UNIQUE KEY unique_attendance (session_id, user_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        echo "<p style='color: green;'>✅ تم إنشاء جدول session_attendees</p>";
    }

    // جلب الكورسات والمدربين
    $stmt = $conn->query("SELECT id, title, instructor_id FROM courses WHERE status = 'active' LIMIT 5");
    $courses = $stmt->fetchAll(PDO::FETCH_ASSOC);

    if (empty($courses)) {
        echo "<p style='color: red;'>❌ لا توجد كورسات نشطة. يرجى إضافة كورسات أولاً.</p>";
        exit;
    }

    // جلب الطلاب (المستخدمين من نوع student)
    $stmt = $conn->query("SELECT id, name, email FROM users WHERE role = 'student' LIMIT 10");
    $students = $stmt->fetchAll(PDO::FETCH_ASSOC);

    if (empty($students)) {
        echo "<p>إنشاء طلاب تجريبيين...</p>";
        
        // إنشاء طلاب تجريبيين
        $sample_students = [
            ['name' => 'أحمد محمد علي', 'email' => '<EMAIL>', 'phone' => '01234567890'],
            ['name' => 'فاطمة أحمد حسن', 'email' => '<EMAIL>', 'phone' => '01234567891'],
            ['name' => 'محمد عبدالله سالم', 'email' => '<EMAIL>', 'phone' => '01234567892'],
            ['name' => 'عائشة محمود طه', 'email' => '<EMAIL>', 'phone' => '01234567893'],
            ['name' => 'يوسف إبراهيم خالد', 'email' => '<EMAIL>', 'phone' => '01234567894'],
            ['name' => 'زينب عمر فاروق', 'email' => '<EMAIL>', 'phone' => '01234567895'],
            ['name' => 'عمر حسام الدين', 'email' => '<EMAIL>', 'phone' => '01234567896'],
            ['name' => 'نور الهدى سعيد', 'email' => '<EMAIL>', 'phone' => '01234567897'],
            ['name' => 'كريم أشرف محمد', 'email' => '<EMAIL>', 'phone' => '01234567898'],
            ['name' => 'سارة أحمد عبدالرحمن', 'email' => '<EMAIL>', 'phone' => '01234567899']
        ];

        foreach ($sample_students as $student) {
            $stmt = $conn->prepare("
                INSERT INTO users (name, email, phone, password, role, status, created_at) 
                VALUES (?, ?, ?, ?, 'student', 'active', NOW())
            ");
            $stmt->execute([
                $student['name'],
                $student['email'],
                $student['phone'],
                password_hash('123456', PASSWORD_DEFAULT)
            ]);
        }

        echo "<p style='color: green;'>✅ تم إنشاء 10 طلاب تجريبيين</p>";

        // إعادة جلب الطلاب
        $stmt = $conn->query("SELECT id, name, email FROM users WHERE role = 'student' LIMIT 10");
        $students = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    echo "<p>إضافة تسجيلات الطلاب في الكورسات...</p>";

    // تسجيل الطلاب في الكورسات
    $enrollment_count = 0;
    foreach ($courses as $course) {
        // تسجيل 3-7 طلاب عشوائيين في كل كورس
        $students_to_enroll = array_rand($students, rand(3, min(7, count($students))));
        if (!is_array($students_to_enroll)) {
            $students_to_enroll = [$students_to_enroll];
        }

        foreach ($students_to_enroll as $student_index) {
            $student = $students[$student_index];
            
            // التحقق من عدم وجود تسجيل سابق
            $stmt = $conn->prepare("SELECT id FROM course_enrollments WHERE course_id = ? AND student_id = ?");
            $stmt->execute([$course['id'], $student['id']]);
            
            if (!$stmt->fetch()) {
                $progress = rand(10, 95);
                $status = ['active', 'active', 'active', 'completed', 'dropped'][rand(0, 4)];
                
                $stmt = $conn->prepare("
                    INSERT INTO course_enrollments 
                    (course_id, student_id, enrolled_at, status, progress_percentage) 
                    VALUES (?, ?, DATE_SUB(NOW(), INTERVAL ? DAY), ?, ?)
                ");
                $stmt->execute([
                    $course['id'],
                    $student['id'],
                    rand(1, 30), // تاريخ تسجيل عشوائي خلال آخر 30 يوم
                    $status,
                    $progress
                ]);
                $enrollment_count++;

                // إضافة درجات عشوائية
                if (rand(0, 1)) {
                    $assignments = ['الواجب الأول', 'الاختبار النصفي', 'المشروع النهائي', 'المشاركة'];
                    foreach ($assignments as $assignment) {
                        if (rand(0, 1)) {
                            $grade = rand(60, 100);
                            $stmt = $conn->prepare("
                                INSERT INTO student_grades 
                                (student_id, course_id, assignment_name, grade, graded_at) 
                                VALUES (?, ?, ?, ?, DATE_SUB(NOW(), INTERVAL ? DAY))
                            ");
                            $stmt->execute([
                                $student['id'],
                                $course['id'],
                                $assignment,
                                $grade,
                                rand(1, 20)
                            ]);
                        }
                    }
                }
            }
        }
    }

    echo "<p style='color: green;'>✅ تم تسجيل {$enrollment_count} طالب في الكورسات</p>";

    // إضافة حضور الجلسات
    $stmt = $conn->query("SELECT id, course_id FROM sessions LIMIT 20");
    $sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);

    if (!empty($sessions)) {
        echo "<p>إضافة سجلات الحضور...</p>";
        $attendance_count = 0;

        foreach ($sessions as $session) {
            // جلب الطلاب المسجلين في هذا الكورس
            $stmt = $conn->prepare("
                SELECT student_id FROM course_enrollments 
                WHERE course_id = ? AND status = 'active'
            ");
            $stmt->execute([$session['course_id']]);
            $enrolled_students = $stmt->fetchAll(PDO::FETCH_COLUMN);

            foreach ($enrolled_students as $student_id) {
                // حضور عشوائي (70% احتمال الحضور)
                if (rand(1, 100) <= 70) {
                    $stmt = $conn->prepare("
                        INSERT IGNORE INTO session_attendees 
                        (session_id, user_id, join_time, attendance_status) 
                        VALUES (?, ?, DATE_SUB(NOW(), INTERVAL ? DAY), 'present')
                    ");
                    $stmt->execute([
                        $session['id'],
                        $student_id,
                        rand(1, 15)
                    ]);
                    $attendance_count++;
                }
            }
        }

        echo "<p style='color: green;'>✅ تم إضافة {$attendance_count} سجل حضور</p>";
    }

    echo "<h3 style='color: green;'>🎉 تم إضافة جميع البيانات التجريبية بنجاح!</h3>";
    echo "<p><a href='instructor/students.php' class='btn btn-primary'>عرض صفحة إدارة الطلاب</a></p>";

} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "</p>";
}
?>
