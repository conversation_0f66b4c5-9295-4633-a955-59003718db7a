<?php
header('Content-Type: text/html; charset=utf-8');
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<div style='direction: rtl; font-family: Arial; padding: 20px;'>";
echo "<h1>تثبيت نظام التعلم عن بعد</h1>";

try {
    // الاتصال بالسيرفر
    $conn = new PDO("mysql:host=localhost", "root", "");
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $conn->exec("SET NAMES utf8mb4");
    
    echo "<div style='color: green; margin: 10px 0;'>✓ تم الاتصال بالسيرفر بنجاح</div>";
    
    // إنشاء قاعدة البيانات
    $conn->exec("DROP DATABASE IF EXISTS zoom_db");
    $conn->exec("CREATE DATABASE zoom_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    $conn->exec("USE zoom_db");
    
    echo "<div style='color: green; margin: 10px 0;'>✓ تم إنشاء قاعدة البيانات بنجاح</div>";
    
    // إنشاء جداول قاعدة البيانات
    $tables = [
        "CREATE TABLE IF NOT EXISTS blocked_ips (
            id INT PRIMARY KEY AUTO_INCREMENT,
            ip VARCHAR(45) NOT NULL,
            blocked_until DATETIME NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )",
        
        "CREATE TABLE IF NOT EXISTS login_attempts (
            id INT PRIMARY KEY AUTO_INCREMENT,
            email VARCHAR(255) NOT NULL,
            ip_address VARCHAR(45) NOT NULL,
            success BOOLEAN DEFAULT FALSE,
            attempt_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )",
        
        "CREATE TABLE IF NOT EXISTS users (
            id INT PRIMARY KEY AUTO_INCREMENT,
            name VARCHAR(255) NOT NULL,
            email VARCHAR(255) NOT NULL UNIQUE,
            username VARCHAR(50) NOT NULL UNIQUE,
            password VARCHAR(255) NOT NULL,
            role ENUM('admin', 'instructor', 'student') NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_login TIMESTAMP NULL,
            status ENUM('active', 'inactive') DEFAULT 'active'
        )",
        
        "CREATE TABLE IF NOT EXISTS courses (
            id INT PRIMARY KEY AUTO_INCREMENT,
            title VARCHAR(255) NOT NULL,
            description TEXT,
            instructor_id INT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            status ENUM('active', 'inactive') DEFAULT 'active',
            FOREIGN KEY (instructor_id) REFERENCES users(id) ON DELETE SET NULL
        )",
        
        "CREATE TABLE IF NOT EXISTS sessions (
            id INT PRIMARY KEY AUTO_INCREMENT,
            course_id INT,
            title VARCHAR(255) NOT NULL,
            description TEXT,
            start_time DATETIME NOT NULL,
            duration INT NOT NULL,
            zoom_meeting_id VARCHAR(255),
            zoom_meeting_password VARCHAR(255),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            status ENUM('scheduled', 'completed', 'cancelled') DEFAULT 'scheduled',
            FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE
        )",
        
        "CREATE TABLE IF NOT EXISTS course_enrollments (
            id INT PRIMARY KEY AUTO_INCREMENT,
            course_id INT,
            student_id INT,
            enrolled_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            status ENUM('active', 'completed', 'dropped') DEFAULT 'active',
            FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
            FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE
        )",
        
        "CREATE TABLE IF NOT EXISTS session_attendees (
            id INT PRIMARY KEY AUTO_INCREMENT,
            session_id INT,
            user_id INT,
            join_time TIMESTAMP NULL,
            leave_time TIMESTAMP NULL,
            FOREIGN KEY (session_id) REFERENCES sessions(id) ON DELETE CASCADE,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        )",
        
        "CREATE TABLE IF NOT EXISTS activity_logs (
            id INT PRIMARY KEY AUTO_INCREMENT,
            user_id INT,
            action VARCHAR(255) NOT NULL,
            details TEXT,
            ip_address VARCHAR(45),
            user_agent TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
        )",
        
        "CREATE TABLE IF NOT EXISTS notifications (
            id INT PRIMARY KEY AUTO_INCREMENT,
            user_id INT,
            type ENUM('email', 'whatsapp') NOT NULL,
            content TEXT NOT NULL,
            status ENUM('sent', 'failed') DEFAULT 'sent',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        )"
    ];
    
    foreach ($tables as $sql) {
        $conn->exec($sql);
    }
    
    echo "<div style='color: green; margin: 10px 0;'>✓ تم إنشاء جميع الجداول بنجاح</div>";
    
    // إنشاء حساب المشرف الافتراضي
    $adminPassword = password_hash('admin123', PASSWORD_DEFAULT);
    $conn->exec("INSERT INTO users (name, email, username, password, role, status) 
                 VALUES ('المشرف', '<EMAIL>', 'admin', '$adminPassword', 'admin', 'active')");
    
    echo "<div style='color: green; margin: 10px 0;'>✓ تم إنشاء حساب المشرف بنجاح</div>";
    echo "<div style='background: #f0f0f0; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
    echo "<strong>بيانات تسجيل الدخول للمشرف:</strong><br>";
    echo "اسم المستخدم: admin<br>";
    echo "كلمة المرور: admin123";
    echo "</div>";
    
    echo "<div style='margin-top: 20px;'>";
    echo "<a href='admin/login.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>انتقل إلى صفحة تسجيل الدخول</a>";
    echo "</div>";
    
} catch(PDOException $e) {
    echo "<div style='color: red; margin: 10px 0;'>❌ حدث خطأ: " . $e->getMessage() . "</div>";
}

echo "</div>";
?>
