# 🎓 نظام التعلم عن بعد - Learning Management System

## 📋 نظرة عامة

نظام إدارة التعلم عن بعد هو منصة تعليمية متطورة مصممة لإدارة الدورات والجلسات التعليمية عبر الإنترنت. يدعم النظام ثلاثة أنواع من المستخدمين: المدير، المدرب، والطالب، مع تكامل كامل مع منصة Zoom للجلسات المباشرة.

## ✨ الميزات الرئيسية

### 🔐 إدارة المستخدمين
- نظام تسجيل دخول آمن مع حماية من الهجمات
- دعم تسجيل الدخول عبر Google OAuth
- إدارة الأدوار والصلاحيات (مدير، مدرب، طالب)
- نظام إعادة تعيين كلمة المرور
- تتبع نشاط المستخدمين

### 📚 إدارة الكورسات
- إنشاء وإدارة الكورسات التعليمية
- تصنيف الكورسات حسب الفئات
- نظام التسجيل في الكورسات
- تتبع تقدم الطلاب
- إصدار الشهادات

### 🎥 إدارة الجلسات
- جدولة الجلسات التعليمية
- تكامل مع Zoom للجلسات المباشرة
- تسجيل الحضور والغياب
- تسجيل الجلسات للمراجعة اللاحقة
- إرسال تذكيرات تلقائية

### 📊 التقارير والإحصائيات
- لوحة تحكم شاملة مع الإحصائيات
- تقارير الحضور والأداء
- إحصائيات المستخدمين والكورسات
- تتبع النشاط في الوقت الفعلي

### 🔒 الأمان والحماية
- حماية من هجمات CSRF و XSS
- تشفير البيانات الحساسة
- نظام حظر IP المشبوهة
- تسجيل شامل للأنشطة الأمنية
- headers أمان متقدمة

## 🛠️ التقنيات المستخدمة

### Backend
- **PHP 8.0+** - لغة البرمجة الأساسية
- **MySQL 8.0+** - قاعدة البيانات
- **PDO** - للتفاعل مع قاعدة البيانات
- **Composer** - إدارة المكتبات

### Frontend
- **HTML5 & CSS3** - هيكل وتصميم الصفحات
- **Bootstrap 5.3** - إطار عمل CSS
- **JavaScript ES6+** - التفاعل والديناميكية
- **jQuery 3.7** - مكتبة JavaScript
- **Font Awesome 6.4** - الأيقونات
- **DataTables** - جداول تفاعلية
- **SweetAlert2** - رسائل تفاعلية

### المكتبات الخارجية
- **PHPMailer** - إرسال البريد الإلكتروني
- **Firebase JWT** - التوكنات الآمنة
- **Guzzle HTTP** - طلبات HTTP
- **Google OAuth** - تسجيل الدخول عبر Google

## 📦 متطلبات التشغيل

### متطلبات الخادم
- **PHP 8.0** أو أحدث
- **MySQL 8.0** أو أحدث
- **Apache/Nginx** خادم ويب
- **Composer** لإدارة المكتبات
- **OpenSSL** للتشفير
- **cURL** للطلبات الخارجية

### الإضافات المطلوبة لـ PHP
```
- pdo_mysql
- openssl
- curl
- gd
- mbstring
- json
- session
```

## 🚀 التثبيت والإعداد

### 1. تحميل المشروع
```bash
git clone https://github.com/your-repo/learning-management-system.git
cd learning-management-system
```

### 2. تثبيت المكتبات
```bash
composer install
```

### 3. إعداد قاعدة البيانات
1. إنشاء قاعدة بيانات جديدة
2. تحديث إعدادات الاتصال في `config/database.php`
3. تشغيل ملف الإعداد:
```bash
php update_database_enhanced.php
```

### 4. إعداد الخادم
- تأكد من أن مجلد `uploads/` قابل للكتابة
- إعداد Virtual Host للمشروع
- تفعيل mod_rewrite في Apache

### 5. إعداد المتغيرات البيئية
```php
// config/database.php
define('DB_HOST', 'localhost');
define('DB_USER', 'your_username');
define('DB_PASS', 'your_password');
define('DB_NAME', 'zoom_learning_system');
```

## 👥 بيانات تسجيل الدخول الافتراضية

بعد التثبيت، يمكنك استخدام الحسابات التالية:

### المدير
- **البريد الإلكتروني:** <EMAIL>
- **كلمة المرور:** password

### المدرب التجريبي
- **البريد الإلكتروني:** <EMAIL>
- **كلمة المرور:** password

### الطالب التجريبي
- **البريد الإلكتروني:** <EMAIL>
- **كلمة المرور:** password

## 📁 هيكل المشروع

```
├── admin/                  # ملفات لوحة تحكم المدير
├── instructor/             # ملفات لوحة تحكم المدرب
├── student/               # ملفات لوحة تحكم الطالب
├── assets/                # الملفات الثابتة
│   ├── css/              # ملفات CSS
│   ├── js/               # ملفات JavaScript
│   └── images/           # الصور
├── config/               # ملفات التكوين
├── includes/             # ملفات مشتركة
├── database/             # ملفات قاعدة البيانات
├── uploads/              # ملفات المرفوعة
└── vendor/               # مكتبات Composer
```

## 🔧 التخصيص والتطوير

### إضافة ميزات جديدة
1. إنشاء ملفات PHP في المجلد المناسب
2. تحديث قاعدة البيانات إذا لزم الأمر
3. إضافة الأنماط في `assets/css/`
4. إضافة JavaScript في `assets/js/`

### تخصيص التصميم
- تعديل متغيرات CSS في `assets/css/main.css`
- تخصيص الألوان والخطوط
- إضافة مكونات جديدة في `assets/css/components.css`

## 🔒 الأمان

### إعدادات الأمان
- تغيير كلمات المرور الافتراضية
- تحديث مفاتيح التشفير في `config/security_enhanced.php`
- تفعيل HTTPS في الإنتاج
- مراجعة إعدادات قاعدة البيانات

### أفضل الممارسات
- تحديث النظام بانتظام
- مراقبة سجلات الأنشطة
- عمل نسخ احتياطية دورية
- تقييد صلاحيات قاعدة البيانات

## 📞 الدعم والمساعدة

### التوثيق
- [دليل المستخدم](docs/user-guide.md)
- [دليل المطور](docs/developer-guide.md)
- [API Documentation](docs/api.md)

### الإبلاغ عن المشاكل
- إنشاء Issue في GitHub
- إرسال بريد إلكتروني للدعم
- مراجعة الأسئلة الشائعة

## 📄 الترخيص

هذا المشروع مرخص تحت [MIT License](LICENSE).

## 🤝 المساهمة

نرحب بالمساهمات! يرجى قراءة [دليل المساهمة](CONTRIBUTING.md) قبل البدء.

## 📈 خارطة الطريق

### الإصدار القادم (v2.1)
- [ ] تطبيق الهاتف المحمول
- [ ] تكامل مع منصات أخرى (Teams, Meet)
- [ ] نظام الدفع المتقدم
- [ ] تحليلات متقدمة
- [ ] دعم اللغات المتعددة

### المستقبل
- [ ] الذكاء الاصطناعي للتوصيات
- [ ] الواقع الافتراضي للتعلم
- [ ] منصة المحتوى التفاعلي
- [ ] نظام التقييم الذكي

---

**تم التطوير بواسطة:** فريق التطوير المتخصص  
**الإصدار:** 2.0  
**تاريخ آخر تحديث:** 2024
