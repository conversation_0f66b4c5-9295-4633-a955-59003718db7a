<?php
session_start();
require_once 'config/database.php';
require_once 'includes/meals_system.php';
require_once 'includes/functions.php';

// الحصول على فئات الوجبات
$mealCategories = getMealCategories();

// الحصول على الفلاتر من URL
$filters = [
    'category_id' => $_GET['category'] ?? '',
    'meal_type' => $_GET['type'] ?? '',
    'diet_type' => $_GET['diet'] ?? '',
    'difficulty' => $_GET['difficulty'] ?? '',
    'max_time' => $_GET['max_time'] ?? '',
    'search' => $_GET['search'] ?? '',
    'sort_by' => $_GET['sort'] ?? 'rating',
    'page' => $_GET['page'] ?? 1,
    'limit' => 12
];

// الحصول على الوجبات
$meals = getMeals($filters);

// عنوان الصفحة
$pageTitle = 'الوجبات الصحية';
if (!empty($filters['search'])) {
    $pageTitle = 'نتائج البحث: ' . htmlspecialchars($filters['search']);
}
?>

<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - منصة التعلم الإلكتروني</title>
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="assets/css/visitor_styles.css" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }
        
        .meals-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 4rem 0 2rem;
            margin-bottom: 2rem;
        }
        
        .filter-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            padding: 1.5rem;
            margin-bottom: 2rem;
            border: none;
        }
        
        .meal-card {
            background: white;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border: none;
            height: 100%;
        }
        
        .meal-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }
        
        .meal-image {
            height: 250px;
            overflow: hidden;
            position: relative;
        }
        
        .meal-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }
        
        .meal-card:hover .meal-image img {
            transform: scale(1.1);
        }
        
        .meal-badges {
            position: absolute;
            top: 1rem;
            right: 1rem;
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }
        
        .meal-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            color: white;
        }
        
        .badge-difficulty-easy { background: #28a745; }
        .badge-difficulty-medium { background: #ffc107; color: #000; }
        .badge-difficulty-hard { background: #dc3545; }
        
        .badge-diet {
            background: rgba(0,0,0,0.7);
        }
        
        .meal-info {
            padding: 1.5rem;
        }
        
        .meal-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: #2d3748;
        }
        
        .meal-description {
            color: #718096;
            font-size: 0.9rem;
            margin-bottom: 1rem;
            line-height: 1.5;
        }
        
        .meal-stats {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            font-size: 0.85rem;
            color: #a0aec0;
        }
        
        .meal-stat {
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }
        
        .nutrition-info {
            background: #f8fafc;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        
        .nutrition-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 0.5rem;
            font-size: 0.8rem;
        }
        
        .nutrition-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .nutrition-label {
            color: #718096;
        }
        
        .nutrition-value {
            font-weight: 600;
            color: #2d3748;
        }
        
        .meal-actions {
            display: flex;
            gap: 0.5rem;
        }
        
        .btn-view-meal {
            flex: 1;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 0.75rem;
            border-radius: 10px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-view-meal:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
            color: white;
        }
        
        .btn-favorite {
            background: #f8fafc;
            color: #718096;
            border: 2px solid #e2e8f0;
            padding: 0.75rem;
            border-radius: 10px;
            transition: all 0.3s ease;
        }
        
        .btn-favorite:hover,
        .btn-favorite.active {
            background: #fed7d7;
            color: #e53e3e;
            border-color: #feb2b2;
        }
        
        .filter-section {
            margin-bottom: 1.5rem;
        }
        
        .filter-title {
            font-weight: 600;
            margin-bottom: 0.75rem;
            color: #2d3748;
        }
        
        .filter-options {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
        }
        
        .filter-option {
            padding: 0.5rem 1rem;
            border: 2px solid #e2e8f0;
            border-radius: 20px;
            background: white;
            color: #718096;
            text-decoration: none;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }
        
        .filter-option:hover,
        .filter-option.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
            text-decoration: none;
        }
        
        .search-box {
            position: relative;
            margin-bottom: 1.5rem;
        }
        
        .search-input {
            width: 100%;
            padding: 1rem 1rem 1rem 3rem;
            border: 2px solid #e2e8f0;
            border-radius: 15px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        
        .search-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .search-icon {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: #a0aec0;
        }
        
        .no-results {
            text-align: center;
            padding: 3rem;
            color: #718096;
        }
        
        .no-results i {
            font-size: 4rem;
            margin-bottom: 1rem;
            color: #e2e8f0;
        }
        
        @media (max-width: 768px) {
            .meals-header {
                padding: 2rem 0 1rem;
            }
            
            .filter-options {
                flex-direction: column;
            }
            
            .filter-option {
                text-align: center;
            }
            
            .nutrition-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>

<body>
    <!-- Header -->
    <div class="meals-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="display-4 fw-bold mb-3"><?php echo $pageTitle; ?></h1>
                    <p class="lead mb-0">اكتشف وصفات صحية ولذيذة لجميع الوجبات</p>
                </div>
                <div class="col-md-4 text-end">
                    <div class="d-flex justify-content-end gap-3">
                        <div class="text-center">
                            <div class="h3 mb-0"><?php echo count($meals); ?></div>
                            <small>وجبة متاحة</small>
                        </div>
                        <div class="text-center">
                            <div class="h3 mb-0"><?php echo count($mealCategories); ?></div>
                            <small>فئة</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="row">
            <!-- Sidebar Filters -->
            <div class="col-lg-3">
                <div class="filter-card sticky-top">
                    <!-- Search -->
                    <div class="search-box">
                        <form method="GET" action="">
                            <div class="position-relative">
                                <input type="text" 
                                       name="search" 
                                       class="search-input" 
                                       placeholder="ابحث عن وجبة..."
                                       value="<?php echo htmlspecialchars($filters['search']); ?>">
                                <i class="fas fa-search search-icon"></i>
                            </div>
                            
                            <!-- Hidden filters to maintain state -->
                            <?php foreach (['category', 'type', 'diet', 'difficulty', 'max_time', 'sort'] as $param): ?>
                                <?php if (!empty($_GET[$param])): ?>
                                    <input type="hidden" name="<?php echo $param; ?>" value="<?php echo htmlspecialchars($_GET[$param]); ?>">
                                <?php endif; ?>
                            <?php endforeach; ?>
                        </form>
                    </div>

                    <!-- Categories -->
                    <div class="filter-section">
                        <div class="filter-title">الفئات</div>
                        <div class="filter-options">
                            <a href="?<?php echo http_build_query(array_merge($_GET, ['category' => ''])); ?>" 
                               class="filter-option <?php echo empty($filters['category_id']) ? 'active' : ''; ?>">
                                الكل
                            </a>
                            <?php foreach ($mealCategories as $category): ?>
                                <a href="?<?php echo http_build_query(array_merge($_GET, ['category' => $category['id']])); ?>" 
                                   class="filter-option <?php echo $filters['category_id'] == $category['id'] ? 'active' : ''; ?>">
                                    <i class="<?php echo $category['icon']; ?> me-1"></i>
                                    <?php echo htmlspecialchars($category['name']); ?>
                                    <span class="badge bg-secondary ms-1"><?php echo $category['meals_count']; ?></span>
                                </a>
                            <?php endforeach; ?>
                        </div>
                    </div>

                    <!-- Meal Type -->
                    <div class="filter-section">
                        <div class="filter-title">نوع الوجبة</div>
                        <div class="filter-options">
                            <?php 
                            $mealTypes = [
                                '' => 'الكل',
                                'breakfast' => 'إفطار',
                                'lunch' => 'غداء', 
                                'dinner' => 'عشاء',
                                'snack' => 'وجبة خفيفة',
                                'dessert' => 'حلوى'
                            ];
                            ?>
                            <?php foreach ($mealTypes as $value => $label): ?>
                                <a href="?<?php echo http_build_query(array_merge($_GET, ['type' => $value])); ?>" 
                                   class="filter-option <?php echo $filters['meal_type'] == $value ? 'active' : ''; ?>">
                                    <?php echo $label; ?>
                                </a>
                            <?php endforeach; ?>
                        </div>
                    </div>

                    <!-- Diet Type -->
                    <div class="filter-section">
                        <div class="filter-title">النظام الغذائي</div>
                        <div class="filter-options">
                            <?php 
                            $dietTypes = [
                                '' => 'الكل',
                                'vegetarian' => 'نباتي',
                                'vegan' => 'نباتي صرف',
                                'gluten_free' => 'خالي من الجلوتين',
                                'dairy_free' => 'خالي من الألبان',
                                'keto' => 'كيتو',
                                'low_carb' => 'قليل الكربوهيدرات',
                                'high_protein' => 'عالي البروتين'
                            ];
                            ?>
                            <?php foreach ($dietTypes as $value => $label): ?>
                                <a href="?<?php echo http_build_query(array_merge($_GET, ['diet' => $value])); ?>" 
                                   class="filter-option <?php echo $filters['diet_type'] == $value ? 'active' : ''; ?>">
                                    <?php echo $label; ?>
                                </a>
                            <?php endforeach; ?>
                        </div>
                    </div>

                    <!-- Difficulty -->
                    <div class="filter-section">
                        <div class="filter-title">مستوى الصعوبة</div>
                        <div class="filter-options">
                            <?php 
                            $difficulties = [
                                '' => 'الكل',
                                'easy' => 'سهل',
                                'medium' => 'متوسط',
                                'hard' => 'صعب'
                            ];
                            ?>
                            <?php foreach ($difficulties as $value => $label): ?>
                                <a href="?<?php echo http_build_query(array_merge($_GET, ['difficulty' => $value])); ?>" 
                                   class="filter-option <?php echo $filters['difficulty'] == $value ? 'active' : ''; ?>">
                                    <?php echo $label; ?>
                                </a>
                            <?php endforeach; ?>
                        </div>
                    </div>

                    <!-- Sort Options -->
                    <div class="filter-section">
                        <div class="filter-title">ترتيب حسب</div>
                        <div class="filter-options">
                            <?php 
                            $sortOptions = [
                                'rating' => 'الأعلى تقييماً',
                                'name' => 'الاسم',
                                'time' => 'وقت التحضير',
                                'calories' => 'السعرات الحرارية',
                                'newest' => 'الأحدث'
                            ];
                            ?>
                            <?php foreach ($sortOptions as $value => $label): ?>
                                <a href="?<?php echo http_build_query(array_merge($_GET, ['sort' => $value])); ?>" 
                                   class="filter-option <?php echo $filters['sort_by'] == $value ? 'active' : ''; ?>">
                                    <?php echo $label; ?>
                                </a>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-lg-9">
                <?php if (empty($meals)): ?>
                    <div class="no-results">
                        <i class="fas fa-search"></i>
                        <h3>لا توجد نتائج</h3>
                        <p>لم نجد أي وجبات تطابق معايير البحث الخاصة بك</p>
                        <a href="meals.php" class="btn btn-primary">عرض جميع الوجبات</a>
                    </div>
                <?php else: ?>
                    <div class="row g-4">
                        <?php foreach ($meals as $meal): ?>
                            <div class="col-lg-4 col-md-6">
                                <div class="meal-card">
                                    <div class="meal-image">
                                        <img src="<?php echo $meal['image'] ?: 'https://images.unsplash.com/photo-1546069901-ba9599a7e63c?w=400&h=250&fit=crop'; ?>" 
                                             alt="<?php echo htmlspecialchars($meal['name']); ?>">
                                        
                                        <div class="meal-badges">
                                            <span class="meal-badge badge-difficulty-<?php echo $meal['difficulty']; ?>">
                                                <?php 
                                                $difficultyLabels = ['easy' => 'سهل', 'medium' => 'متوسط', 'hard' => 'صعب'];
                                                echo $difficultyLabels[$meal['difficulty']];
                                                ?>
                                            </span>
                                            
                                            <?php if ($meal['diet_type']): ?>
                                                <?php $dietTypes = explode(',', $meal['diet_type']); ?>
                                                <?php foreach (array_slice($dietTypes, 0, 2) as $diet): ?>
                                                    <span class="meal-badge badge-diet">
                                                        <?php 
                                                        $dietLabels = [
                                                            'vegetarian' => 'نباتي',
                                                            'vegan' => 'نباتي صرف',
                                                            'gluten_free' => 'خالي جلوتين',
                                                            'keto' => 'كيتو'
                                                        ];
                                                        echo $dietLabels[$diet] ?? $diet;
                                                        ?>
                                                    </span>
                                                <?php endforeach; ?>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    
                                    <div class="meal-info">
                                        <h5 class="meal-title"><?php echo htmlspecialchars($meal['name']); ?></h5>
                                        <p class="meal-description">
                                            <?php echo truncateText($meal['description'], 80); ?>
                                        </p>
                                        
                                        <div class="meal-stats">
                                            <div class="meal-stat">
                                                <i class="fas fa-clock"></i>
                                                <span><?php echo $meal['total_time']; ?> دقيقة</span>
                                            </div>
                                            <div class="meal-stat">
                                                <i class="fas fa-users"></i>
                                                <span><?php echo $meal['servings']; ?> حصة</span>
                                            </div>
                                            <div class="meal-stat">
                                                <i class="fas fa-fire"></i>
                                                <span><?php echo number_format($meal['calories']); ?> سعرة</span>
                                            </div>
                                        </div>
                                        
                                        <div class="nutrition-info">
                                            <div class="nutrition-grid">
                                                <div class="nutrition-item">
                                                    <span class="nutrition-label">بروتين</span>
                                                    <span class="nutrition-value"><?php echo number_format($meal['protein'], 1); ?>g</span>
                                                </div>
                                                <div class="nutrition-item">
                                                    <span class="nutrition-label">كربوهيدرات</span>
                                                    <span class="nutrition-value"><?php echo number_format($meal['carbs'], 1); ?>g</span>
                                                </div>
                                                <div class="nutrition-item">
                                                    <span class="nutrition-label">دهون</span>
                                                    <span class="nutrition-value"><?php echo number_format($meal['fat'], 1); ?>g</span>
                                                </div>
                                                <div class="nutrition-item">
                                                    <span class="nutrition-label">ألياف</span>
                                                    <span class="nutrition-value"><?php echo number_format($meal['fiber'], 1); ?>g</span>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="d-flex justify-content-between align-items-center mb-3">
                                            <?php echo getStarRating($meal['rating'], $meal['review_count']); ?>
                                            <small class="text-muted">بواسطة <?php echo htmlspecialchars($meal['creator_name']); ?></small>
                                        </div>
                                        
                                        <div class="meal-actions">
                                            <a href="meal-details.php?id=<?php echo $meal['id']; ?>" class="btn-view-meal">
                                                <i class="fas fa-eye me-2"></i>
                                                عرض الوصفة
                                            </a>
                                            <button class="btn-favorite" onclick="toggleFavorite(<?php echo $meal['id']; ?>)">
                                                <i class="far fa-heart"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    
                    <!-- Pagination -->
                    <div class="d-flex justify-content-center mt-5">
                        <nav>
                            <ul class="pagination">
                                <li class="page-item">
                                    <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => max(1, $filters['page'] - 1)])); ?>">
                                        السابق
                                    </a>
                                </li>
                                <li class="page-item active">
                                    <span class="page-link"><?php echo $filters['page']; ?></span>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $filters['page'] + 1])); ?>">
                                        التالي
                                    </a>
                                </li>
                            </ul>
                        </nav>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function toggleFavorite(mealId) {
            const btn = event.target.closest('.btn-favorite');
            const icon = btn.querySelector('i');
            
            // Toggle visual state
            if (btn.classList.contains('active')) {
                btn.classList.remove('active');
                icon.className = 'far fa-heart';
            } else {
                btn.classList.add('active');
                icon.className = 'fas fa-heart';
            }
            
            // Here you would typically send an AJAX request to save the favorite
            // fetch('/api/toggle-favorite', { method: 'POST', body: JSON.stringify({meal_id: mealId}) })
        }
        
        // Auto-submit search form on input
        document.querySelector('.search-input').addEventListener('input', function() {
            clearTimeout(this.searchTimeout);
            this.searchTimeout = setTimeout(() => {
                this.closest('form').submit();
            }, 500);
        });
    </script>
</body>
</html>
