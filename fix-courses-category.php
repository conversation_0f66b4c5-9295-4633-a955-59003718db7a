<?php
require_once 'config/database.php';

echo "<h2>إصلاح عمود التصنيف في جدول الكورسات</h2>";

try {
    // التحقق من هيكل جدول courses
    $stmt = $conn->query("DESCRIBE courses");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<h3>الأعمدة الموجودة حالياً في جدول courses:</h3>";
    echo "<ul>";
    foreach ($columns as $column) {
        echo "<li>$column</li>";
    }
    echo "</ul>";
    
    // التحقق من وجود عمود category_id
    if (!in_array('category_id', $columns)) {
        echo "<p>إضافة عمود category_id...</p>";
        $conn->exec("ALTER TABLE courses ADD COLUMN category_id INT NULL AFTER instructor_id");
        echo "<p style='color: green;'>✅ تم إضافة عمود category_id</p>";
        
        // إضافة فهرس للعمود
        $conn->exec("ALTER TABLE courses ADD INDEX idx_category_id (category_id)");
        echo "<p style='color: green;'>✅ تم إضافة فهرس للعمود</p>";
    } else {
        echo "<p style='color: blue;'>ℹ️ عمود category_id موجود بالفعل</p>";
    }
    
    // التحقق من الأعمدة الأخرى المطلوبة
    $required_columns = [
        'thumbnail' => 'VARCHAR(500) NULL',
        'objectives' => 'TEXT NULL',
        'requirements' => 'TEXT NULL',
        'duration_hours' => 'INT DEFAULT 0',
        'level' => "ENUM('beginner', 'intermediate', 'advanced') DEFAULT 'beginner'",
        'price' => 'DECIMAL(10,2) DEFAULT 0',
        'course_type' => "ENUM('free', 'paid') DEFAULT 'free'",
        'max_students' => 'INT NULL',
        'certificate' => 'TINYINT(1) DEFAULT 0',
        'language' => "VARCHAR(50) DEFAULT 'العربية'",
        'status' => "ENUM('draft', 'active', 'inactive', 'completed') DEFAULT 'active'"
    ];
    
    echo "<h3>إضافة الأعمدة المفقودة:</h3>";
    
    foreach ($required_columns as $column_name => $column_definition) {
        if (!in_array($column_name, $columns)) {
            try {
                $conn->exec("ALTER TABLE courses ADD COLUMN $column_name $column_definition");
                echo "<p style='color: green;'>✅ تم إضافة العمود: $column_name</p>";
            } catch (PDOException $e) {
                echo "<p style='color: red;'>❌ خطأ في إضافة العمود $column_name: " . $e->getMessage() . "</p>";
            }
        } else {
            echo "<p style='color: blue;'>ℹ️ العمود $column_name موجود بالفعل</p>";
        }
    }
    
    // التأكد من وجود جدول categories
    $stmt = $conn->query("SHOW TABLES LIKE 'categories'");
    if ($stmt->rowCount() == 0) {
        echo "<p>إنشاء جدول categories...</p>";
        $conn->exec("CREATE TABLE categories (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            description TEXT,
            icon VARCHAR(100),
            color VARCHAR(20),
            status ENUM('active', 'inactive') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_status (status)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
        echo "<p style='color: green;'>✅ تم إنشاء جدول categories</p>";
        
        // إضافة تصنيفات افتراضية
        $default_categories = [
            ['البرمجة', 'كورسات البرمجة وتطوير البرمجيات', 'fas fa-code', '#007bff'],
            ['التصميم', 'كورسات التصميم الجرافيكي وتصميم المواقع', 'fas fa-paint-brush', '#28a745'],
            ['التسويق', 'كورسات التسويق الرقمي والتسويق الإلكتروني', 'fas fa-bullhorn', '#ffc107'],
            ['الأعمال', 'كورسات إدارة الأعمال وريادة الأعمال', 'fas fa-briefcase', '#dc3545'],
            ['اللغات', 'كورسات تعلم اللغات المختلفة', 'fas fa-language', '#6f42c1']
        ];
        
        foreach ($default_categories as $category) {
            $stmt = $conn->prepare("INSERT INTO categories (name, description, icon, color) VALUES (?, ?, ?, ?)");
            $stmt->execute($category);
        }
        
        echo "<p style='color: green;'>✅ تم إضافة " . count($default_categories) . " تصنيف افتراضي</p>";
    }
    
    // تحديث الكورسات الموجودة
    echo "<h3>تحديث الكورسات الموجودة:</h3>";
    
    // تعيين تصنيف افتراضي للكورسات التي لا تحتوي على تصنيف
    $stmt = $conn->query("SELECT COUNT(*) FROM courses WHERE category_id IS NULL");
    $courses_without_category = $stmt->fetchColumn();
    
    if ($courses_without_category > 0) {
        // الحصول على تصنيف "البرمجة"
        $stmt = $conn->query("SELECT id FROM categories WHERE name = 'البرمجة' LIMIT 1");
        $programming_category = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($programming_category) {
            $conn->exec("UPDATE courses SET category_id = " . $programming_category['id'] . " WHERE category_id IS NULL");
            echo "<p style='color: green;'>✅ تم تحديث $courses_without_category كورس بتصنيف البرمجة</p>";
        }
    }
    
    // تحديث بيانات أخرى
    $stmt = $conn->query("SELECT COUNT(*) FROM courses WHERE level IS NULL");
    $courses_without_level = $stmt->fetchColumn();
    
    if ($courses_without_level > 0) {
        $conn->exec("UPDATE courses SET level = 'beginner' WHERE level IS NULL");
        echo "<p style='color: green;'>✅ تم تحديث $courses_without_level كورس بمستوى مبتدئ</p>";
    }
    
    $stmt = $conn->query("SELECT COUNT(*) FROM courses WHERE duration_hours IS NULL OR duration_hours = 0");
    $courses_without_duration = $stmt->fetchColumn();
    
    if ($courses_without_duration > 0) {
        $conn->exec("UPDATE courses SET duration_hours = 20 WHERE duration_hours IS NULL OR duration_hours = 0");
        echo "<p style='color: green;'>✅ تم تحديث $courses_without_duration كورس بمدة 20 ساعة</p>";
    }
    
    // عرض هيكل الجدول المحدث
    echo "<h3>هيكل جدول courses بعد التحديث:</h3>";
    $stmt = $conn->query("DESCRIBE courses");
    $updated_columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>اسم العمود</th><th>النوع</th><th>NULL</th><th>Default</th></tr>";
    foreach ($updated_columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . ($column['Default'] ?? 'NULL') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // عرض بعض الكورسات مع التصنيفات
    echo "<h3>الكورسات مع التصنيفات:</h3>";
    $stmt = $conn->query("
        SELECT c.id, c.title, c.category_id, cat.name as category_name, c.level, c.price, c.duration_hours
        FROM courses c
        LEFT JOIN categories cat ON c.category_id = cat.id
        LIMIT 5
    ");
    $courses = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($courses)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>العنوان</th><th>التصنيف</th><th>المستوى</th><th>السعر</th><th>المدة</th></tr>";
        foreach ($courses as $course) {
            echo "<tr>";
            echo "<td>" . $course['id'] . "</td>";
            echo "<td>" . htmlspecialchars($course['title']) . "</td>";
            echo "<td>" . htmlspecialchars($course['category_name'] ?? 'غير محدد') . "</td>";
            echo "<td>" . htmlspecialchars($course['level'] ?? 'غير محدد') . "</td>";
            echo "<td>" . ($course['price'] ?? '0') . " ر.س</td>";
            echo "<td>" . ($course['duration_hours'] ?? '0') . " ساعة</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<h3 style='color: green;'>✅ تم إصلاح جدول الكورسات بنجاح!</h3>";
    echo "<p><a href='student/browse-courses.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>اختبار صفحة تصفح الكورسات</a></p>";
    
} catch (PDOException $e) {
    echo "<h3 style='color: red;'>❌ خطأ: " . $e->getMessage() . "</h3>";
}
?>
