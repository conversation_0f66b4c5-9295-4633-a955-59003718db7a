<?php
require_once 'config/database.php';

echo "<!DOCTYPE html>";
echo "<html dir='rtl' lang='ar'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>فحص جدول الأنشطة</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "</head>";
echo "<body>";
echo "<div class='container mt-4'>";
echo "<h2>🔍 فحص جدول activity_logs</h2>";

try {
    // التحقق من وجود الجدول
    $stmt = $conn->query("SHOW TABLES LIKE 'activity_logs'");
    if ($stmt->rowCount() > 0) {
        echo "<div class='alert alert-success'>✅ جدول activity_logs موجود</div>";
        
        // عرض هيكل الجدول
        $stmt = $conn->query("DESCRIBE activity_logs");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h4>هيكل الجدول:</h4>";
        echo "<table class='table table-bordered'>";
        echo "<thead><tr><th>اسم العمود</th><th>النوع</th><th>Null</th><th>Key</th><th>Default</th></tr></thead>";
        echo "<tbody>";
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Default']) . "</td>";
            echo "</tr>";
        }
        echo "</tbody></table>";
        
        // عدد السجلات
        $stmt = $conn->query("SELECT COUNT(*) as count FROM activity_logs");
        $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        echo "<div class='alert alert-info'>عدد السجلات في الجدول: $count</div>";
        
    } else {
        echo "<div class='alert alert-warning'>⚠️ جدول activity_logs غير موجود</div>";
        echo "<p>سيتم إنشاؤه تلقائياً عند أول استدعاء لدالة logUserActivity</p>";
    }
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>❌ خطأ: " . htmlspecialchars($e->getMessage()) . "</div>";
}

// اختبار دالة logUserActivity
echo "<hr>";
echo "<h4>🧪 اختبار دالة logUserActivity:</h4>";

try {
    require_once 'includes/functions.php';
    
    // اختبار الدالة
    logUserActivity(1, 'اختبار', 'هذا اختبار لدالة تسجيل الأنشطة');
    echo "<div class='alert alert-success'>✅ تم تشغيل دالة logUserActivity بنجاح</div>";
    
    // التحقق من إدراج السجل
    $stmt = $conn->prepare("SELECT * FROM activity_logs WHERE action = 'اختبار' ORDER BY created_at DESC LIMIT 1");
    $stmt->execute();
    $record = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($record) {
        echo "<div class='alert alert-success'>✅ تم إدراج السجل بنجاح</div>";
        echo "<pre>" . print_r($record, true) . "</pre>";
    } else {
        echo "<div class='alert alert-warning'>⚠️ لم يتم العثور على السجل المدرج</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>❌ خطأ في اختبار الدالة: " . htmlspecialchars($e->getMessage()) . "</div>";
}

echo "</div>";
echo "</body>";
echo "</html>";
?>
