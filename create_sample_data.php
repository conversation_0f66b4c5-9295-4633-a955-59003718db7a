<?php
require_once 'config/database.php';

echo "<h2>إنشاء بيانات تجريبية</h2>";

try {
    // التحقق من وجود مدرب
    $stmt = $conn->query("SELECT id FROM users WHERE role = 'instructor' LIMIT 1");
    $instructor = $stmt->fetch();
    
    if (!$instructor) {
        echo "لا يوجد مدربين في النظام. سأقوم بإنشاء مدرب تجريبي...<br>";
        
        // إنشاء مدرب تجريبي
        $stmt = $conn->prepare("INSERT INTO users (name, email, password, role, status) VALUES (?, ?, ?, 'instructor', 'active')");
        $stmt->execute(['مدرب تجريبي', '<EMAIL>', password_hash('123456', PASSWORD_DEFAULT)]);
        $instructor_id = $conn->lastInsertId();
        echo "✓ تم إنشاء مدرب تجريبي بـ ID: $instructor_id<br>";
    } else {
        $instructor_id = $instructor['id'];
        echo "✓ تم العثور على مدرب بـ ID: $instructor_id<br>";
    }
    
    // التحقق من وجود كورس
    $stmt = $conn->prepare("SELECT id FROM courses WHERE instructor_id = ? LIMIT 1");
    $stmt->execute([$instructor_id]);
    $course = $stmt->fetch();
    
    if (!$course) {
        echo "لا يوجد كورسات للمدرب. سأقوم بإنشاء كورس تجريبي...<br>";
        
        // إنشاء كورس تجريبي
        $stmt = $conn->prepare("INSERT INTO courses (title, description, instructor_id, status) VALUES (?, ?, ?, 'active')");
        $stmt->execute(['كورس البرمجة الأساسية', 'كورس تعليمي لأساسيات البرمجة', $instructor_id]);
        $course_id = $conn->lastInsertId();
        echo "✓ تم إنشاء كورس تجريبي بـ ID: $course_id<br>";
    } else {
        $course_id = $course['id'];
        echo "✓ تم العثور على كورس بـ ID: $course_id<br>";
    }
    
    // التحقق من وجود اختبارات
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM quizzes q INNER JOIN courses c ON q.course_id = c.id WHERE c.instructor_id = ?");
    $stmt->execute([$instructor_id]);
    $quiz_count = $stmt->fetch()['count'];
    
    if ($quiz_count == 0) {
        echo "لا توجد اختبارات للمدرب. سأقوم بإنشاء اختبارات تجريبية...<br>";
        
        // إنشاء اختبارات تجريبية
        $quizzes = [
            [
                'title' => 'اختبار أساسيات البرمجة',
                'description' => 'اختبار تقييمي لفهم أساسيات البرمجة',
                'time_limit' => 30,
                'max_attempts' => 2,
                'passing_grade' => 70
            ],
            [
                'title' => 'اختبار المتغيرات والثوابت',
                'description' => 'اختبار حول المتغيرات والثوابت في البرمجة',
                'time_limit' => 20,
                'max_attempts' => 3,
                'passing_grade' => 60
            ],
            [
                'title' => 'اختبار الحلقات والشروط',
                'description' => 'اختبار حول استخدام الحلقات والشروط',
                'time_limit' => 45,
                'max_attempts' => 1,
                'passing_grade' => 80
            ]
        ];
        
        foreach ($quizzes as $quiz_data) {
            $stmt = $conn->prepare("
                INSERT INTO quizzes (course_id, title, description, time_limit, passing_score,
                                   shuffle_questions, show_results, status, created_by)
                VALUES (?, ?, ?, ?, ?, 0, 1, 'draft', ?)
            ");
            $stmt->execute([
                $course_id,
                $quiz_data['title'],
                $quiz_data['description'],
                $quiz_data['time_limit'],
                $quiz_data['passing_grade'],
                $instructor_id
            ]);
            
            $quiz_id = $conn->lastInsertId();
            echo "✓ تم إنشاء اختبار: {$quiz_data['title']} بـ ID: $quiz_id<br>";
            
            // إضافة أسئلة تجريبية
            $questions = [
                [
                    'text' => 'ما هو المتغير في البرمجة؟',
                    'type' => 'multiple_choice',
                    'points' => 2,
                    'options' => [
                        ['text' => 'مكان لتخزين البيانات', 'correct' => true],
                        ['text' => 'نوع من الدوال', 'correct' => false],
                        ['text' => 'أمر للطباعة', 'correct' => false],
                        ['text' => 'نوع من الحلقات', 'correct' => false]
                    ]
                ],
                [
                    'text' => 'البرمجة هي عملية كتابة التعليمات للحاسوب',
                    'type' => 'true_false',
                    'points' => 1,
                    'correct_answer' => 'true'
                ]
            ];
            
            foreach ($questions as $q_index => $question) {
                $stmt = $conn->prepare("
                    INSERT INTO quiz_questions (quiz_id, question_text, question_type, points, question_order) 
                    VALUES (?, ?, ?, ?, ?)
                ");
                $stmt->execute([
                    $quiz_id,
                    $question['text'],
                    $question['type'],
                    $question['points'],
                    $q_index + 1
                ]);
                
                $question_id = $conn->lastInsertId();
                
                if ($question['type'] == 'multiple_choice') {
                    foreach ($question['options'] as $option) {
                        $stmt = $conn->prepare("
                            INSERT INTO quiz_question_options (question_id, option_text, is_correct) 
                            VALUES (?, ?, ?)
                        ");
                        $stmt->execute([
                            $question_id,
                            $option['text'],
                            $option['correct'] ? 1 : 0
                        ]);
                    }
                } elseif ($question['type'] == 'true_false') {
                    $stmt = $conn->prepare("
                        INSERT INTO quiz_question_options (question_id, option_text, is_correct) 
                        VALUES (?, ?, ?)
                    ");
                    $stmt->execute([$question_id, 'صح', $question['correct_answer'] == 'true' ? 1 : 0]);
                    $stmt->execute([$question_id, 'خطأ', $question['correct_answer'] == 'false' ? 1 : 0]);
                }
            }
        }
        
        echo "<br>✓ تم إنشاء جميع البيانات التجريبية بنجاح!<br>";
    } else {
        echo "✓ يوجد $quiz_count اختبار للمدرب<br>";
    }
    
    echo "<br><h3>ملخص البيانات:</h3>";
    
    // عرض الإحصائيات النهائية
    $stmt = $conn->query("SELECT COUNT(*) as count FROM users WHERE role = 'instructor'");
    $instructors = $stmt->fetch()['count'];
    echo "عدد المدربين: $instructors<br>";
    
    $stmt = $conn->query("SELECT COUNT(*) as count FROM courses");
    $courses = $stmt->fetch()['count'];
    echo "عدد الكورسات: $courses<br>";
    
    $stmt = $conn->query("SELECT COUNT(*) as count FROM quizzes");
    $quizzes = $stmt->fetch()['count'];
    echo "عدد الاختبارات: $quizzes<br>";
    
    $stmt = $conn->query("SELECT COUNT(*) as count FROM quiz_questions");
    $questions = $stmt->fetch()['count'];
    echo "عدد الأسئلة: $questions<br>";
    
    echo "<br><a href='instructor/quizzes.php' class='btn btn-primary'>الذهاب لصفحة الاختبارات</a>";
    
} catch (Exception $e) {
    echo "خطأ: " . $e->getMessage();
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
.btn { padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; }
</style>
