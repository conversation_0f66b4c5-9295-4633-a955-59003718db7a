<?php
/**
 * فحص تفاصيل الكورس رقم 15
 * Debug course details for course ID 15
 */

require_once 'config/database.php';

echo "<!DOCTYPE html>";
echo "<html dir='rtl' lang='ar'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>فحص تفاصيل الكورس</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "</head>";
echo "<body>";
echo "<div class='container mt-4'>";
echo "<h2>🔍 فحص تفاصيل الكورس رقم 15</h2>";

$course_id = 15;

try {
    // 1. فحص وجود الكورس
    echo "<h4>📚 فحص وجود الكورس</h4>";
    
    $stmt = $conn->prepare("SELECT * FROM courses WHERE id = ?");
    $stmt->execute([$course_id]);
    $course_basic = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$course_basic) {
        echo "<div class='alert alert-danger'>❌ الكورس رقم $course_id غير موجود</div>";
        
        // عرض الكورسات المتاحة
        $stmt = $conn->query("SELECT id, title FROM courses ORDER BY id");
        $available_courses = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h5>الكورسات المتاحة:</h5>";
        echo "<ul>";
        foreach ($available_courses as $course) {
            echo "<li><a href='course-details.php?id={$course['id']}'>{$course['id']} - " . htmlspecialchars($course['title']) . "</a></li>";
        }
        echo "</ul>";
        
    } else {
        echo "<div class='alert alert-success'>✅ الكورس موجود</div>";
        
        // عرض البيانات الأساسية
        echo "<div class='card mb-3'>";
        echo "<div class='card-header'><h5>البيانات الأساسية</h5></div>";
        echo "<div class='card-body'>";
        echo "<table class='table'>";
        foreach ($course_basic as $key => $value) {
            echo "<tr><td><strong>$key</strong></td><td>" . htmlspecialchars($value ?? 'NULL') . "</td></tr>";
        }
        echo "</table>";
        echo "</div></div>";
        
        // 2. فحص بيانات المدرب
        echo "<h4>👨‍🏫 فحص بيانات المدرب</h4>";
        
        $stmt = $conn->prepare("SELECT * FROM users WHERE id = ? AND role = 'instructor'");
        $stmt->execute([$course_basic['instructor_id']]);
        $instructor = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$instructor) {
            echo "<div class='alert alert-warning'>⚠️ المدرب غير موجود أو ليس له دور مدرب</div>";
            
            // البحث عن مدرب بديل
            $stmt = $conn->query("SELECT id, name, email FROM users WHERE role = 'instructor' LIMIT 1");
            $alt_instructor = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($alt_instructor) {
                echo "<div class='alert alert-info'>ℹ️ سيتم تعيين مدرب بديل</div>";
                
                $stmt = $conn->prepare("UPDATE courses SET instructor_id = ? WHERE id = ?");
                $stmt->execute([$alt_instructor['id'], $course_id]);
                
                echo "<div class='alert alert-success'>✅ تم تعيين المدرب: " . htmlspecialchars($alt_instructor['name']) . "</div>";
            }
        } else {
            echo "<div class='alert alert-success'>✅ بيانات المدرب متاحة</div>";
            echo "<p><strong>الاسم:</strong> " . htmlspecialchars($instructor['name']) . "</p>";
            echo "<p><strong>البريد:</strong> " . htmlspecialchars($instructor['email']) . "</p>";
        }
        
        // 3. فحص الجلسات
        echo "<h4>🎥 فحص جلسات الكورس</h4>";
        
        $stmt = $conn->prepare("SELECT * FROM sessions WHERE course_id = ?");
        $stmt->execute([$course_id]);
        $sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<div class='alert alert-info'>ℹ️ عدد الجلسات: " . count($sessions) . "</div>";
        
        if (empty($sessions)) {
            echo "<div class='alert alert-warning'>⚠️ لا توجد جلسات لهذا الكورس</div>";
            
            // إنشاء جلسات تجريبية
            $sample_sessions = [
                ['الجلسة الأولى - مقدمة', 'مقدمة شاملة عن الكورس والأهداف'],
                ['الجلسة الثانية - الأساسيات', 'شرح المفاهيم الأساسية'],
                ['الجلسة الثالثة - التطبيق العملي', 'تطبيق عملي على المفاهيم']
            ];
            
            foreach ($sample_sessions as $index => $session_data) {
                $future_date = date('Y-m-d', strtotime('+' . ($index + 1) . ' weeks'));
                $start_time = sprintf('%02d:00:00', 10 + $index);
                $end_time = sprintf('%02d:30:00', 11 + $index);
                
                $stmt = $conn->prepare("
                    INSERT INTO sessions (course_id, title, description, session_date, start_time, end_time, status, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, 'scheduled', NOW())
                ");
                $stmt->execute([$course_id, $session_data[0], $session_data[1], $future_date, $start_time, $end_time]);
            }
            
            echo "<div class='alert alert-success'>✅ تم إنشاء 3 جلسات تجريبية</div>";
        } else {
            echo "<div class='table-responsive'>";
            echo "<table class='table table-sm'>";
            echo "<thead><tr><th>العنوان</th><th>التاريخ</th><th>الوقت</th><th>الحالة</th></tr></thead>";
            echo "<tbody>";
            foreach ($sessions as $session) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($session['title']) . "</td>";
                echo "<td>" . ($session['session_date'] ?? 'غير محدد') . "</td>";
                echo "<td>" . ($session['start_time'] ?? 'غير محدد') . "</td>";
                echo "<td>" . ($session['status'] ?? 'غير محدد') . "</td>";
                echo "</tr>";
            }
            echo "</tbody></table>";
            echo "</div>";
        }
        
        // 4. فحص التسجيلات
        echo "<h4>👥 فحص تسجيلات الطلاب</h4>";
        
        $stmt = $conn->prepare("
            SELECT ce.*, u.name as student_name 
            FROM course_enrollments ce
            JOIN users u ON ce.student_id = u.id
            WHERE ce.course_id = ?
        ");
        $stmt->execute([$course_id]);
        $enrollments = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<div class='alert alert-info'>ℹ️ عدد الطلاب المسجلين: " . count($enrollments) . "</div>";
        
        if (!empty($enrollments)) {
            echo "<div class='table-responsive'>";
            echo "<table class='table table-sm'>";
            echo "<thead><tr><th>الطالب</th><th>تاريخ التسجيل</th><th>الحالة</th></tr></thead>";
            echo "<tbody>";
            foreach ($enrollments as $enrollment) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($enrollment['student_name']) . "</td>";
                echo "<td>" . date('Y-m-d', strtotime($enrollment['enrollment_date'])) . "</td>";
                echo "<td><span class='badge bg-" . ($enrollment['status'] === 'active' ? 'success' : 'secondary') . "'>" . $enrollment['status'] . "</span></td>";
                echo "</tr>";
            }
            echo "</tbody></table>";
            echo "</div>";
        }
        
        // 5. فحص المراجعات
        echo "<h4>⭐ فحص مراجعات الكورس</h4>";
        
        $stmt = $conn->prepare("
            SELECT cr.*, u.name as student_name 
            FROM course_reviews cr
            JOIN users u ON cr.student_id = u.id
            WHERE cr.course_id = ?
        ");
        $stmt->execute([$course_id]);
        $reviews = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<div class='alert alert-info'>ℹ️ عدد المراجعات: " . count($reviews) . "</div>";
        
        if (empty($reviews)) {
            echo "<div class='alert alert-warning'>⚠️ لا توجد مراجعات لهذا الكورس</div>";
            
            // إضافة مراجعات تجريبية
            $sample_reviews = [
                ['rating' => 5, 'comment' => 'كورس ممتاز ومفيد جداً'],
                ['rating' => 4, 'comment' => 'محتوى جيد وشرح واضح'],
                ['rating' => 5, 'comment' => 'أنصح به بشدة']
            ];
            
            // جلب طلاب للمراجعات
            $stmt = $conn->query("SELECT id FROM users WHERE role = 'student' LIMIT 3");
            $students = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            if (!empty($students)) {
                foreach ($sample_reviews as $index => $review) {
                    if (isset($students[$index])) {
                        $stmt = $conn->prepare("
                            INSERT INTO course_reviews (course_id, student_id, rating, comment, created_at)
                            VALUES (?, ?, ?, ?, NOW())
                            ON DUPLICATE KEY UPDATE rating = VALUES(rating), comment = VALUES(comment)
                        ");
                        $stmt->execute([$course_id, $students[$index], $review['rating'], $review['comment']]);
                    }
                }
                echo "<div class='alert alert-success'>✅ تم إضافة مراجعات تجريبية</div>";
            }
        } else {
            foreach ($reviews as $review) {
                echo "<div class='card mb-2'>";
                echo "<div class='card-body'>";
                echo "<div class='d-flex justify-content-between'>";
                echo "<strong>" . htmlspecialchars($review['student_name']) . "</strong>";
                echo "<div>";
                for ($i = 1; $i <= 5; $i++) {
                    echo $i <= $review['rating'] ? '⭐' : '☆';
                }
                echo "</div>";
                echo "</div>";
                echo "<p>" . htmlspecialchars($review['comment']) . "</p>";
                echo "</div></div>";
            }
        }
        
        // 6. اختبار الاستعلام الكامل
        echo "<h4>🧪 اختبار الاستعلام الكامل</h4>";
        
        try {
            $stmt = $conn->prepare("
                SELECT c.*, u.name as instructor_name, u.email as instructor_email, u.phone as instructor_phone,
                       (SELECT COUNT(*) FROM course_enrollments WHERE course_id = c.id AND status = 'active') as enrolled_students,
                       (SELECT COUNT(*) FROM sessions WHERE course_id = c.id) as total_sessions,
                       (SELECT AVG(rating) FROM course_reviews WHERE course_id = c.id) as avg_rating,
                       (SELECT COUNT(*) FROM course_reviews WHERE course_id = c.id) as total_reviews
                FROM courses c
                JOIN users u ON c.instructor_id = u.id
                WHERE c.id = ? AND c.status = 'active'
            ");
            $stmt->execute([$course_id]);
            $full_course = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($full_course) {
                echo "<div class='alert alert-success'>✅ الاستعلام الكامل يعمل بنجاح</div>";
                
                echo "<div class='card'>";
                echo "<div class='card-header'><h5>البيانات الكاملة للكورس</h5></div>";
                echo "<div class='card-body'>";
                echo "<div class='row'>";
                echo "<div class='col-md-6'>";
                echo "<p><strong>العنوان:</strong> " . htmlspecialchars($full_course['title']) . "</p>";
                echo "<p><strong>المدرب:</strong> " . htmlspecialchars($full_course['instructor_name']) . "</p>";
                echo "<p><strong>التخصص:</strong> " . htmlspecialchars($full_course['category'] ?? 'غير محدد') . "</p>";
                echo "<p><strong>النوع:</strong> " . ($full_course['course_type'] === 'paid' ? 'مدفوع' : 'مجاني') . "</p>";
                echo "</div>";
                echo "<div class='col-md-6'>";
                echo "<p><strong>الطلاب:</strong> " . $full_course['enrolled_students'] . "</p>";
                echo "<p><strong>الجلسات:</strong> " . $full_course['total_sessions'] . "</p>";
                echo "<p><strong>التقييم:</strong> " . ($full_course['avg_rating'] ? number_format($full_course['avg_rating'], 1) : 'لا يوجد') . "</p>";
                echo "<p><strong>المراجعات:</strong> " . $full_course['total_reviews'] . "</p>";
                echo "</div>";
                echo "</div>";
                echo "</div></div>";
                
            } else {
                echo "<div class='alert alert-danger'>❌ فشل في جلب البيانات الكاملة</div>";
            }
            
        } catch (Exception $e) {
            echo "<div class='alert alert-danger'>❌ خطأ في الاستعلام: " . htmlspecialchars($e->getMessage()) . "</div>";
        }
    }
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>❌ خطأ عام: " . htmlspecialchars($e->getMessage()) . "</div>";
}

echo "<div class='text-center mt-4'>";
echo "<a href='course-details.php?id=$course_id' class='btn btn-primary btn-lg me-2'>📚 عرض صفحة التفاصيل</a>";
echo "<a href='courses.php' class='btn btn-success btn-lg me-2'>📋 جميع الكورسات</a>";
echo "<a href='index.php' class='btn btn-info btn-lg'>🏠 الصفحة الرئيسية</a>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>
