<?php
require_once 'config/database.php';

echo "<h2>إصلاح جدول الكورسات</h2>";

try {
    // التحقق من هيكل جدول courses
    $stmt = $conn->query("DESCRIBE courses");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<h3>الأعمدة الموجودة حالياً:</h3>";
    echo "<ul>";
    foreach ($columns as $column) {
        echo "<li>$column</li>";
    }
    echo "</ul>";
    
    // قائمة الأعمدة المطلوبة
    $required_columns = [
        'start_date' => 'DATE NULL',
        'end_date' => 'DATE NULL',
        'image_path' => 'VARCHAR(500) NULL',
        'course_type' => "ENUM('free', 'paid') DEFAULT 'free'",
        'price' => 'DECIMAL(10,2) DEFAULT 0',
        'currency' => "VARCHAR(10) DEFAULT 'ريال'",
        'max_students' => 'INT DEFAULT NULL',
        'level' => "ENUM('beginner', 'intermediate', 'advanced') DEFAULT 'beginner'",
        'language' => "VARCHAR(50) DEFAULT 'العربية'",
        'duration_weeks' => 'INT DEFAULT NULL',
        'certificate' => 'TINYINT(1) DEFAULT 0'
    ];
    
    echo "<h3>إضافة الأعمدة المفقودة:</h3>";
    
    foreach ($required_columns as $column_name => $column_definition) {
        if (!in_array($column_name, $columns)) {
            try {
                $conn->exec("ALTER TABLE courses ADD COLUMN $column_name $column_definition");
                echo "<p style='color: green;'>✅ تم إضافة العمود: $column_name</p>";
            } catch (PDOException $e) {
                echo "<p style='color: red;'>❌ خطأ في إضافة العمود $column_name: " . $e->getMessage() . "</p>";
            }
        } else {
            echo "<p style='color: blue;'>ℹ️ العمود $column_name موجود بالفعل</p>";
        }
    }
    
    // تحديث البيانات الموجودة
    echo "<h3>تحديث البيانات الموجودة:</h3>";
    
    // إضافة تواريخ افتراضية للكورسات الموجودة
    $stmt = $conn->query("SELECT COUNT(*) FROM courses WHERE start_date IS NULL");
    $courses_without_dates = $stmt->fetchColumn();
    
    if ($courses_without_dates > 0) {
        // تعيين تاريخ البدء كتاريخ اليوم وتاريخ الانتهاء بعد شهرين
        $conn->exec("UPDATE courses SET 
                     start_date = CURDATE(), 
                     end_date = DATE_ADD(CURDATE(), INTERVAL 2 MONTH) 
                     WHERE start_date IS NULL");
        echo "<p style='color: green;'>✅ تم تحديث تواريخ $courses_without_dates كورس</p>";
    }
    
    // إضافة صور افتراضية
    $stmt = $conn->query("SELECT COUNT(*) FROM courses WHERE image_path IS NULL OR image_path = ''");
    $courses_without_images = $stmt->fetchColumn();
    
    if ($courses_without_images > 0) {
        $conn->exec("UPDATE courses SET 
                     image_path = 'assets/images/default-course.jpg' 
                     WHERE image_path IS NULL OR image_path = ''");
        echo "<p style='color: green;'>✅ تم تحديث صور $courses_without_images كورس</p>";
    }
    
    // تحديث أنواع الكورسات
    $stmt = $conn->query("SELECT COUNT(*) FROM courses WHERE course_type IS NULL");
    $courses_without_type = $stmt->fetchColumn();
    
    if ($courses_without_type > 0) {
        $conn->exec("UPDATE courses SET course_type = 'free' WHERE course_type IS NULL");
        echo "<p style='color: green;'>✅ تم تحديث نوع $courses_without_type كورس إلى مجاني</p>";
    }
    
    // عرض هيكل الجدول المحدث
    echo "<h3>هيكل الجدول بعد التحديث:</h3>";
    $stmt = $conn->query("DESCRIBE courses");
    $updated_columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>اسم العمود</th><th>النوع</th><th>NULL</th><th>Default</th></tr>";
    foreach ($updated_columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . ($column['Default'] ?? 'NULL') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // عرض بعض الكورسات كمثال
    echo "<h3>الكورسات الموجودة:</h3>";
    $stmt = $conn->query("SELECT id, title, start_date, end_date, course_type, price FROM courses LIMIT 5");
    $courses = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($courses)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>العنوان</th><th>تاريخ البدء</th><th>تاريخ الانتهاء</th><th>النوع</th><th>السعر</th></tr>";
        foreach ($courses as $course) {
            echo "<tr>";
            echo "<td>" . $course['id'] . "</td>";
            echo "<td>" . htmlspecialchars($course['title']) . "</td>";
            echo "<td>" . ($course['start_date'] ?? 'غير محدد') . "</td>";
            echo "<td>" . ($course['end_date'] ?? 'غير محدد') . "</td>";
            echo "<td>" . ($course['course_type'] ?? 'غير محدد') . "</td>";
            echo "<td>" . ($course['price'] ?? '0') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<h3 style='color: green;'>✅ تم إصلاح جدول الكورسات بنجاح!</h3>";
    echo "<p><a href='instructor/course-details.php?id=1' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>اختبار صفحة تفاصيل الكورس</a></p>";
    
} catch (PDOException $e) {
    echo "<h3 style='color: red;'>❌ خطأ: " . $e->getMessage() . "</h3>";
}
?>
