<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من تسجيل الدخول كطالب
if (!isLoggedIn() || !isStudent()) {
    header('Location: ../login.php');
    exit;
}

$course_id = isset($_GET['course_id']) ? (int)$_GET['course_id'] : 0;
$student_id = $_SESSION['user_id'];

// التحقق من تسجيل الطالب في الكورس
try {
    $stmt = $conn->prepare("
        SELECT c.*, u.name as instructor_name 
        FROM courses c
        JOIN users u ON c.instructor_id = u.id
        JOIN course_students cs ON c.id = cs.course_id
        WHERE c.id = ? AND cs.student_id = ? AND cs.status = 'active'
    ");
    $stmt->execute([$course_id, $student_id]);
    $course = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$course) {
        die('غير مصرح لك بالوصول إلى هذا الكورس');
    }

    // جلب الجلسات القادمة
    $stmt = $conn->prepare("
        SELECT s.*, 
               COUNT(sa.id) as attendance_count
        FROM sessions s
        LEFT JOIN session_attendance sa ON s.id = sa.session_id AND sa.student_id = ?
        WHERE s.course_id = ? AND s.start_time >= NOW()
        GROUP BY s.id
        ORDER BY s.start_time ASC
    ");
    $stmt->execute([$student_id, $course_id]);
    $upcoming_sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // جلب الجلسات السابقة
    $stmt = $conn->prepare("
        SELECT s.*, 
               COUNT(sa.id) as attendance_count
        FROM sessions s
        LEFT JOIN session_attendance sa ON s.id = sa.session_id AND sa.student_id = ?
        WHERE s.course_id = ? AND s.start_time < NOW()
        GROUP BY s.id
        ORDER BY s.start_time DESC
    ");
    $stmt->execute([$student_id, $course_id]);
    $past_sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);

} catch (PDOException $e) {
    error_log($e->getMessage());
    die('حدث خطأ في النظام');
}

$pageTitle = 'جلسات الكورس - ' . $course['title'];
require_once '../includes/header.php';
?>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">معلومات الكورس</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>اسم الكورس:</strong> <?php echo htmlspecialchars($course['title']); ?></p>
                            <p><strong>المدرب:</strong> <?php echo htmlspecialchars($course['instructor_name']); ?></p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>تاريخ البداية:</strong> <?php echo date('Y-m-d', strtotime($course['start_date'])); ?></p>
                            <p><strong>تاريخ النهاية:</strong> <?php echo date('Y-m-d', strtotime($course['end_date'])); ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- الجلسات القادمة -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">الجلسات القادمة</h5>
        </div>
        <div class="card-body">
            <?php if ($upcoming_sessions): ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>عنوان الجلسة</th>
                                <th>التاريخ</th>
                                <th>الوقت</th>
                                <th>المدة (دقيقة)</th>
                                <th>الحالة</th>
                                <th>رابط الجلسة</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($upcoming_sessions as $session): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($session['title']); ?></td>
                                    <td><?php echo date('Y-m-d', strtotime($session['start_time'])); ?></td>
                                    <td><?php echo date('H:i', strtotime($session['start_time'])); ?></td>
                                    <td><?php echo $session['duration']; ?></td>
                                    <td>
                                        <span class="badge bg-<?php echo $session['status'] === 'scheduled' ? 'info' : 'success'; ?>">
                                            <?php echo $session['status'] === 'scheduled' ? 'مجدولة' : 'جارية'; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php if (strtotime($session['start_time']) <= time() + 900): // 15 minutes before start ?>
                                            <a href="<?php echo htmlspecialchars($session['zoom_join_url']); ?>" target="_blank" class="btn btn-primary btn-sm">
                                                انضم للجلسة
                                            </a>
                                        <?php else: ?>
                                            <button class="btn btn-secondary btn-sm" disabled>
                                                غير متاح حالياً
                                            </button>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <p class="text-muted mb-0">لا توجد جلسات قادمة</p>
            <?php endif; ?>
        </div>
    </div>

    <!-- الجلسات السابقة -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">الجلسات السابقة</h5>
        </div>
        <div class="card-body">
            <?php if ($past_sessions): ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>عنوان الجلسة</th>
                                <th>التاريخ</th>
                                <th>الوقت</th>
                                <th>المدة (دقيقة)</th>
                                <th>الحضور</th>
                                <th>التسجيل</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($past_sessions as $session): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($session['title']); ?></td>
                                    <td><?php echo date('Y-m-d', strtotime($session['start_time'])); ?></td>
                                    <td><?php echo date('H:i', strtotime($session['start_time'])); ?></td>
                                    <td><?php echo $session['duration']; ?></td>
                                    <td>
                                        <?php if ($session['attendance_count'] > 0): ?>
                                            <span class="badge bg-success">حاضر</span>
                                        <?php else: ?>
                                            <span class="badge bg-danger">غائب</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($session['recording_url']): ?>
                                            <a href="<?php echo htmlspecialchars($session['recording_url']); ?>" target="_blank" class="btn btn-info btn-sm">
                                                مشاهدة التسجيل
                                            </a>
                                        <?php else: ?>
                                            <span class="text-muted">لا يوجد تسجيل</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <p class="text-muted mb-0">لا توجد جلسات سابقة</p>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php require_once '../includes/footer.php'; ?>
