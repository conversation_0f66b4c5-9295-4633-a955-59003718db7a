<?php
require_once 'config/database.php';

echo "<h2>🔍 تشخيص طلبات الانضمام</h2>";

try {
    // التحقق من وجود جدول join_requests
    $stmt = $conn->query("SHOW TABLES LIKE 'join_requests'");
    if ($stmt->rowCount() == 0) {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h4>❌ جدول join_requests غير موجود</h4>";
        echo "<p><a href='create_sample_students.php'>انقر هنا لإنشاء الجدول</a></p>";
        echo "</div>";
        exit;
    }
    
    echo "<p style='color: green;'>✅ جدول join_requests موجود</p>";
    
    // عرض هيكل الجدول
    echo "<h3>هيكل جدول join_requests:</h3>";
    $stmt = $conn->query("DESCRIBE join_requests");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>اسم العمود</th><th>النوع</th><th>NULL</th><th>Default</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . ($column['Default'] ?? 'NULL') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // عرض جميع طلبات الانضمام
    echo "<h3>جميع طلبات الانضمام:</h3>";

    // التحقق من وجود عمود full_name في جدول users
    $stmt = $conn->query("DESCRIBE users");
    $user_columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    $has_full_name = in_array('full_name', $user_columns);

    if ($has_full_name) {
        $stmt = $conn->query("
            SELECT jr.*,
                   COALESCE(u.full_name, u.username) as student_name,
                   u.email as student_email,
                   c.title as course_title,
                   COALESCE(i.full_name, i.username) as instructor_name
            FROM join_requests jr
            INNER JOIN users u ON jr.student_id = u.id
            INNER JOIN courses c ON jr.course_id = c.id
            INNER JOIN users i ON c.instructor_id = i.id
            ORDER BY jr.created_at DESC
        ");
    } else {
        $stmt = $conn->query("
            SELECT jr.*,
                   u.username as student_name,
                   u.email as student_email,
                   c.title as course_title,
                   i.username as instructor_name
            FROM join_requests jr
            INNER JOIN users u ON jr.student_id = u.id
            INNER JOIN courses c ON jr.course_id = c.id
            INNER JOIN users i ON c.instructor_id = i.id
            ORDER BY jr.created_at DESC
        ");
    }

    $requests = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($requests)) {
        echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h4>⚠️ لا توجد طلبات انضمام</h4>";
        echo "<p>لم يتم إرسال أي طلبات انضمام بعد.</p>";
        echo "<p><a href='test-join-course.php'>جرب إرسال طلب انضمام</a></p>";
        echo "</div>";
    } else {
        echo "<p><strong>عدد الطلبات:</strong> " . count($requests) . "</p>";
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th>ID</th>";
        echo "<th>الطالب</th>";
        echo "<th>البريد الإلكتروني</th>";
        echo "<th>الكورس</th>";
        echo "<th>المدرب</th>";
        echo "<th>الرسالة</th>";
        echo "<th>الحالة</th>";
        echo "<th>تاريخ الطلب</th>";
        echo "</tr>";
        
        foreach ($requests as $request) {
            $status_color = '';
            switch ($request['status']) {
                case 'pending':
                    $status_color = 'orange';
                    break;
                case 'approved':
                    $status_color = 'green';
                    break;
                case 'rejected':
                    $status_color = 'red';
                    break;
            }
            
            echo "<tr>";
            echo "<td>" . $request['id'] . "</td>";
            echo "<td>" . htmlspecialchars($request['student_name']) . "</td>";
            echo "<td>" . htmlspecialchars($request['student_email']) . "</td>";
            echo "<td>" . htmlspecialchars($request['course_title']) . "</td>";
            echo "<td>" . htmlspecialchars($request['instructor_name']) . "</td>";
            echo "<td>" . htmlspecialchars(substr($request['message'] ?? '', 0, 50)) . "...</td>";
            echo "<td style='color: $status_color; font-weight: bold;'>" . $request['status'] . "</td>";
            echo "<td>" . date('Y-m-d H:i', strtotime($request['created_at'])) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // عرض طلبات الانضمام حسب الكورس
    echo "<h3>طلبات الانضمام حسب الكورس:</h3>";
    $stmt = $conn->query("
        SELECT c.id, c.title, COUNT(jr.id) as request_count,
               SUM(CASE WHEN jr.status = 'pending' THEN 1 ELSE 0 END) as pending_count,
               SUM(CASE WHEN jr.status = 'approved' THEN 1 ELSE 0 END) as approved_count,
               SUM(CASE WHEN jr.status = 'rejected' THEN 1 ELSE 0 END) as rejected_count
        FROM courses c
        LEFT JOIN join_requests jr ON c.id = jr.course_id
        GROUP BY c.id, c.title
        ORDER BY request_count DESC
    ");
    $course_stats = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th>ID الكورس</th>";
    echo "<th>عنوان الكورس</th>";
    echo "<th>إجمالي الطلبات</th>";
    echo "<th>معلقة</th>";
    echo "<th>مقبولة</th>";
    echo "<th>مرفوضة</th>";
    echo "<th>رابط المراجعة</th>";
    echo "</tr>";
    
    foreach ($course_stats as $stat) {
        echo "<tr>";
        echo "<td>" . $stat['id'] . "</td>";
        echo "<td>" . htmlspecialchars($stat['title']) . "</td>";
        echo "<td>" . $stat['request_count'] . "</td>";
        echo "<td style='color: orange;'>" . $stat['pending_count'] . "</td>";
        echo "<td style='color: green;'>" . $stat['approved_count'] . "</td>";
        echo "<td style='color: red;'>" . $stat['rejected_count'] . "</td>";
        echo "<td><a href='instructor/course-join-requests.php?course_id=" . $stat['id'] . "' target='_blank'>مراجعة الطلبات</a></td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // معلومات إضافية
    echo "<h3>معلومات إضافية:</h3>";
    echo "<ul>";
    echo "<li><strong>جدول المستخدمين:</strong> ";
    $stmt = $conn->query("SELECT COUNT(*) FROM users WHERE role = 'student'");
    echo $stmt->fetchColumn() . " طالب</li>";
    
    echo "<li><strong>جدول الكورسات:</strong> ";
    $stmt = $conn->query("SELECT COUNT(*) FROM courses WHERE status = 'active'");
    echo $stmt->fetchColumn() . " كورس نشط</li>";
    
    echo "<li><strong>جدول التسجيلات:</strong> ";
    $stmt = $conn->query("SHOW TABLES LIKE 'course_enrollments'");
    if ($stmt->rowCount() > 0) {
        $stmt = $conn->query("SELECT COUNT(*) FROM course_enrollments");
        echo $stmt->fetchColumn() . " تسجيل";
    } else {
        echo "الجدول غير موجود";
    }
    echo "</li>";
    echo "</ul>";
    
} catch (PDOException $e) {
    echo "<h3 style='color: red;'>❌ خطأ: " . $e->getMessage() . "</h3>";
}

echo "<hr>";
echo "<h3>🔗 روابط مفيدة:</h3>";
echo "<ul>";
echo "<li><a href='test-join-course.php'>اختبار طلب الانضمام</a></li>";
echo "<li><a href='instructor/course-join-requests.php?course_id=1'>مراجعة طلبات الكورس 1</a></li>";
echo "<li><a href='check-join-requests.php'>عرض جميع الطلبات</a></li>";
echo "<li><a href='check_students.php'>عرض الطلاب</a></li>";
echo "</ul>";
?>
