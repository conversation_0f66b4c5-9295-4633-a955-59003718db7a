<?php
require_once 'config/database.php';
require_once 'includes/functions.php';

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = filter_var($_POST['email'], FILTER_SANITIZE_EMAIL);
    
    if (empty($email)) {
        $error = 'الرجاء إدخال البريد الإلكتروني';
    } else {
        try {
            $stmt = $conn->prepare("SELECT id, name FROM users WHERE email = ?");
            $stmt->execute([$email]);
            $user = $stmt->fetch();
            
            if ($user) {
                // Generate reset token
                $token = bin2hex(random_bytes(32));
                $expires = date('Y-m-d H:i:s', strtotime('+1 hour'));
                
                $stmt = $conn->prepare("UPDATE users SET reset_token = ?, reset_expires = ? WHERE id = ?");
                $stmt->execute([$token, $expires, $user['id']]);
                
                // Send reset email
                $resetLink = "http://localhost/Zoom/reset-password.php?token=" . $token;
                $subject = 'استعادة كلمة المرور';
                $message = "
                    <h3>مرحباً {$user['name']}</h3>
                    <p>لقد طلبت استعادة كلمة المرور الخاصة بك. انقر على الرابط التالي لتعيين كلمة مرور جديدة:</p>
                    <p><a href='{$resetLink}'>تعيين كلمة مرور جديدة</a></p>
                    <p>هذا الرابط صالح لمدة ساعة واحدة فقط.</p>
                ";
                
                sendEmail($email, $subject, $message);
                $success = 'تم إرسال رابط استعادة كلمة المرور إلى بريدك الإلكتروني';
            } else {
                $error = 'البريد الإلكتروني غير مسجل في النظام';
            }
        } catch(PDOException $e) {
            $error = 'حدث خطأ في النظام';
        }
    }
}
?>
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>استعادة كلمة المرور</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header text-center bg-primary text-white">
                        <h3>استعادة كلمة المرور</h3>
                    </div>
                    <div class="card-body">
                        <?php if ($error): ?>
                            <div class="alert alert-danger"><?php echo $error; ?></div>
                        <?php endif; ?>
                        
                        <?php if ($success): ?>
                            <div class="alert alert-success"><?php echo $success; ?></div>
                        <?php else: ?>
                            <form method="POST">
                                <div class="mb-3">
                                    <label for="email" class="form-label">البريد الإلكتروني</label>
                                    <input type="email" class="form-control" id="email" name="email" required>
                                </div>
                                <button type="submit" class="btn btn-primary w-100">إرسال رابط الاستعادة</button>
                            </form>
                        <?php endif; ?>
                        
                        <div class="text-center mt-3">
                            <a href="login.php" class="text-decoration-none">العودة لتسجيل الدخول</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
