# 📝 دليل نظام الاختبارات - Quiz System Guide

## 🎯 نظرة عامة

تم إنشاء نظام اختبارات متكامل ومتطور يدعم أنواع مختلفة من الأسئلة مع إدارة شاملة للنتائج والتقييم.

## ✅ المميزات المكتملة

### 🔧 للمدربين (Instructors)

#### 1. **إدارة الاختبارات**
- **إنشاء اختبارات جديدة**: `instructor/quizzes.php`
- **تحديد المدة الزمنية والمحاولات المسموحة**
- **تحديد درجة النجاح ونظام التقييم**
- **جدولة الاختبارات (تاريخ البداية والنهاية)**

#### 2. **إدارة الأسئلة**
- **صفحة إدارة الأسئلة**: `instructor/quiz-questions.php`
- **أنواع الأسئلة المدعومة**:
  - اختيار متعدد (Multiple Choice)
  - صح/خطأ (True/False)
  - إجابة قصيرة (Short Answer)
  - مقال (Essay)
- **إضافة خيارات متعددة للأسئلة**
- **تحديد الإجابات الصحيحة**
- **إضافة تفسيرات للأسئلة**

#### 3. **معاينة ومراجعة**
- **معاينة الاختبار**: `instructor/quiz-preview.php`
- **عرض الأسئلة كما يراها الطلاب**
- **طباعة الاختبار**
- **تعديل الأسئلة والخيارات**

#### 4. **إدارة النتائج**
- **صفحة النتائج**: `instructor/quiz-results.php`
- **إحصائيات شاملة للأداء**
- **عرض تفاصيل كل محاولة**
- **تصدير النتائج**
- **إدارة الشهادات**

### 👨‍🎓 للطلاب (Students)

#### 1. **عرض الاختبارات**
- **صفحة الاختبارات**: `student/quizzes.php`
- **عرض الاختبارات المتاحة والمنتهية**
- **معلومات تفصيلية عن كل اختبار**
- **حالة المحاولات والنتائج**

#### 2. **حل الاختبارات**
- **صفحة حل الاختبار**: `student/take-quiz.php`
- **واجهة تفاعلية وسهلة الاستخدام**
- **عداد تنازلي للوقت**
- **حفظ تلقائي للإجابات**
- **شريط تقدم**
- **مراجعة الإجابات قبل الإرسال**

#### 3. **عرض النتائج**
- **صفحة النتائج**: `student/quiz-results.php`
- **عرض النتيجة والحالة (نجح/راسب)**
- **تفاصيل الإجابات الصحيحة والخاطئة**
- **سجل جميع المحاولات**
- **تحميل الشهادات للناجحين**

## 🗄️ هيكل قاعدة البيانات

### الجداول المنشأة:

#### 1. `quizzes` - جدول الاختبارات
```sql
- id: معرف الاختبار
- course_id: معرف الكورس
- title: عنوان الاختبار
- description: وصف الاختبار
- time_limit: المدة الزمنية (بالدقائق)
- max_attempts: عدد المحاولات المسموحة
- passing_grade: درجة النجاح
- status: حالة الاختبار (draft/published/closed)
```

#### 2. `quiz_questions` - جدول الأسئلة
```sql
- id: معرف السؤال
- quiz_id: معرف الاختبار
- question_text: نص السؤال
- question_type: نوع السؤال
- points: النقاط المخصصة
- explanation: تفسير السؤال
```

#### 3. `quiz_question_options` - جدول خيارات الأسئلة
```sql
- id: معرف الخيار
- question_id: معرف السؤال
- option_text: نص الخيار
- is_correct: هل الخيار صحيح
```

#### 4. `quiz_attempts` - جدول محاولات الاختبارات
```sql
- id: معرف المحاولة
- quiz_id: معرف الاختبار
- student_id: معرف الطالب
- score: النتيجة المئوية
- time_taken: الوقت المستغرق
- status: حالة المحاولة
```

#### 5. `quiz_answers` - جدول إجابات الطلاب
```sql
- id: معرف الإجابة
- attempt_id: معرف المحاولة
- question_id: معرف السؤال
- answer_text: نص الإجابة
- selected_option_id: الخيار المختار
- is_correct: هل الإجابة صحيحة
- points_earned: النقاط المكتسبة
```

## 🚀 كيفية الاستخدام

### للمدربين:

#### 1. إنشاء اختبار جديد:
1. اذهب إلى `instructor/quizzes.php`
2. انقر على "إنشاء اختبار جديد"
3. املأ معلومات الاختبار
4. احفظ الاختبار

#### 2. إضافة أسئلة:
1. من صفحة الاختبارات، انقر على "إدارة الأسئلة"
2. اختر نوع السؤال
3. اكتب السؤال والخيارات
4. حدد الإجابة الصحيحة
5. احفظ السؤال

#### 3. نشر الاختبار:
1. تأكد من إضافة جميع الأسئلة
2. راجع الاختبار في صفحة المعاينة
3. غيّر حالة الاختبار إلى "منشور"

### للطلاب:

#### 1. حل اختبار:
1. اذهب إلى `student/quizzes.php`
2. اختر الاختبار المطلوب
3. انقر على "بدء الاختبار"
4. أجب على الأسئلة
5. راجع إجاباتك
6. أرسل الاختبار

#### 2. عرض النتائج:
1. بعد إرسال الاختبار، ستظهر النتيجة فوراً
2. يمكن مراجعة الإجابات الصحيحة والخاطئة
3. تحميل الشهادة في حالة النجاح

## 🔧 الإعداد والتشغيل

### 1. إعداد قاعدة البيانات:
```bash
# قم بزيارة الرابط التالي لإعداد جداول الاختبارات:
http://localhost/Zoom/setup_quizzes.php
```

### 2. التحقق من الإعداد:
- تأكد من إنشاء جميع الجداول
- تحقق من وجود الاختبار التجريبي
- اختبر إنشاء اختبار جديد

## 📊 الإحصائيات والتقارير

### للمدربين:
- **إحصائيات الأداء**: عدد المحاولات، متوسط الدرجات، معدل النجاح
- **تفاصيل المحاولات**: وقت كل محاولة، الإجابات التفصيلية
- **تصدير البيانات**: تصدير النتائج بصيغ مختلفة

### للطلاب:
- **سجل المحاولات**: جميع المحاولات السابقة
- **أفضل النتائج**: أعلى درجة محققة
- **الوقت المستغرق**: تتبع الوقت في كل محاولة

## 🎨 المميزات التقنية

### 1. **واجهة المستخدم**:
- تصميم متجاوب يعمل على جميع الأجهزة
- دعم كامل للغة العربية (RTL)
- تأثيرات بصرية جذابة
- سهولة الاستخدام

### 2. **الأمان**:
- التحقق من صلاحيات المستخدمين
- حماية من التلاعب في النتائج
- تشفير البيانات الحساسة
- منع الغش والتلاعب

### 3. **الأداء**:
- حفظ تلقائي للإجابات
- تحميل سريع للصفحات
- استعلامات محسنة لقاعدة البيانات
- ذاكرة تخزين مؤقت

## 🔗 الروابط المهمة

### للمدربين:
- **إدارة الاختبارات**: `http://localhost/Zoom/instructor/quizzes.php`
- **إدارة الأسئلة**: `http://localhost/Zoom/instructor/quiz-questions.php`
- **معاينة الاختبار**: `http://localhost/Zoom/instructor/quiz-preview.php`
- **النتائج**: `http://localhost/Zoom/instructor/quiz-results.php`

### للطلاب:
- **الاختبارات المتاحة**: `http://localhost/Zoom/student/quizzes.php`
- **حل الاختبار**: `http://localhost/Zoom/student/take-quiz.php`
- **النتائج**: `http://localhost/Zoom/student/quiz-results.php`

### الإعداد:
- **إعداد النظام**: `http://localhost/Zoom/setup_quizzes.php`

## 🆘 استكشاف الأخطاء

### مشاكل شائعة:

#### ❌ لا تظهر الاختبارات
```
الحل: تأكد من تسجيل الطالب في الكورس وأن الاختبار منشور
```

#### ❌ خطأ في حفظ الإجابات
```
الحل: تحقق من اتصال قاعدة البيانات وصلاحيات الكتابة
```

#### ❌ لا تظهر النتائج
```
الحل: تأكد من اكتمال الاختبار وتفعيل خيار "إظهار النتائج"
```

## 🔄 التطويرات المستقبلية

### المخطط لها:
- [ ] نظام مراقبة الغش
- [ ] أسئلة بالصور والفيديو
- [ ] تصحيح تلقائي للأسئلة النصية
- [ ] تحليلات متقدمة للأداء
- [ ] تكامل مع أنظمة LTI
- [ ] تطبيق موبايل للاختبارات

---

**🎉 نظام الاختبارات جاهز للاستخدام!**

للمساعدة الإضافية أو الإبلاغ عن مشاكل، يرجى مراجعة الوثائق أو التواصل مع فريق الدعم.
