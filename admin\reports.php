<?php
require_once '../config/database.php';
require_once '../includes/functions.php';

// Get statistics
try {
    // Total students
    $stmt = $conn->query("SELECT COUNT(*) FROM users WHERE role = 'student'");
    $totalStudents = $stmt->fetchColumn();
    
    // Active students (approved)
    $stmt = $conn->query("SELECT COUNT(*) FROM users WHERE role = 'student' AND status = 'approved'");
    $activeStudents = $stmt->fetchColumn();
    
    // Total sessions
    $stmt = $conn->query("SELECT COUNT(*) FROM sessions");
    $totalSessions = $stmt->fetchColumn();
    
    // Sessions this month
    $stmt = $conn->query("SELECT COUNT(*) FROM sessions WHERE MONTH(start_time) = MONTH(CURRENT_DATE())");
    $sessionsThisMonth = $stmt->fetchColumn();
    
    // Get monthly statistics for the last 6 months
    $stmt = $conn->query("
        SELECT 
            DATE_FORMAT(start_time, '%Y-%m') as month,
            COUNT(*) as session_count
        FROM sessions 
        WHERE start_time >= DATE_SUB(NOW(), INTERVAL 6 MONTH)
        GROUP BY DATE_FORMAT(start_time, '%Y-%m')
        ORDER BY month DESC
    ");
    $monthlyStats = $stmt->fetchAll();
    
    // Get student registration trend
    $stmt = $conn->query("
        SELECT 
            DATE_FORMAT(created_at, '%Y-%m') as month,
            COUNT(*) as student_count
        FROM users 
        WHERE role = 'student' 
        AND created_at >= DATE_SUB(NOW(), INTERVAL 6 MONTH)
        GROUP BY DATE_FORMAT(created_at, '%Y-%m')
        ORDER BY month DESC
    ");
    $registrationTrend = $stmt->fetchAll();
    
} catch(PDOException $e) {
    error_log("Database Error: " . $e->getMessage());
}

$pageTitle = 'التقارير والإحصائيات';
require_once '../includes/header.php';
?>

<div class="container py-4">
    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <h5 class="card-title">إجمالي الطلاب</h5>
                    <h2><?php echo $totalStudents; ?></h2>
                    <p class="mb-0"><i class="fas fa-users"></i></p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <h5 class="card-title">الطلاب النشطين</h5>
                    <h2><?php echo $activeStudents; ?></h2>
                    <p class="mb-0"><i class="fas fa-user-check"></i></p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <h5 class="card-title">إجمالي الجلسات</h5>
                    <h2><?php echo $totalSessions; ?></h2>
                    <p class="mb-0"><i class="fas fa-video"></i></p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <h5 class="card-title">جلسات هذا الشهر</h5>
                    <h2><?php echo $sessionsThisMonth; ?></h2>
                    <p class="mb-0"><i class="fas fa-calendar-alt"></i></p>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts -->
    <div class="row">
        <!-- Monthly Sessions Chart -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">إحصائيات الجلسات الشهرية</h5>
                </div>
                <div class="card-body">
                    <canvas id="sessionsChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Student Registration Trend -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">اتجاه تسجيل الطلاب</h5>
                </div>
                <div class="card-body">
                    <canvas id="registrationChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Monthly Sessions Chart
const sessionsCtx = document.getElementById('sessionsChart').getContext('2d');
new Chart(sessionsCtx, {
    type: 'bar',
    data: {
        labels: <?php echo json_encode(array_column(array_reverse($monthlyStats), 'month')); ?>,
        datasets: [{
            label: 'عدد الجلسات',
            data: <?php echo json_encode(array_column(array_reverse($monthlyStats), 'session_count')); ?>,
            backgroundColor: 'rgba(54, 162, 235, 0.5)',
            borderColor: 'rgba(54, 162, 235, 1)',
            borderWidth: 1
        }]
    },
    options: {
        responsive: true,
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

// Registration Trend Chart
const registrationCtx = document.getElementById('registrationChart').getContext('2d');
new Chart(registrationCtx, {
    type: 'line',
    data: {
        labels: <?php echo json_encode(array_column(array_reverse($registrationTrend), 'month')); ?>,
        datasets: [{
            label: 'عدد التسجيلات',
            data: <?php echo json_encode(array_column(array_reverse($registrationTrend), 'student_count')); ?>,
            fill: false,
            borderColor: 'rgba(75, 192, 192, 1)',
            tension: 0.1
        }]
    },
    options: {
        responsive: true,
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});
</script>

<?php require_once '../includes/footer.php'; ?>
