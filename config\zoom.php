<?php
define('ZOOM_API_KEY', 'YOUR_ZOOM_API_KEY');
define('ZOOM_API_SECRET', 'YOUR_ZOOM_API_SECRET');
define('ZOOM_EMAIL', '<EMAIL>');

function createZoomMeeting($topic, $start_time, $duration = 60) {
    // Temporary function that returns a mock Zoom meeting URL
    // This is just for testing. You'll need to implement proper Zoom API integration later
    $mockMeetingId = rand(10000000, 99999999);
    return (object)[
        'join_url' => "https://zoom.us/j/{$mockMeetingId}",
        'id' => $mockMeetingId,
        'password' => substr(str_shuffle('ABCDEFGHIJKLMNOPQRSTUVWXYZ'), 0, 6)
    ];
}
?>
