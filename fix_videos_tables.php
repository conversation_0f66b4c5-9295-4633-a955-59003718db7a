<?php
// إنشاء جداول الفيديوهات المفقودة
require_once 'config/database.php';

echo "<h2>إنشاء جداول الفيديوهات</h2>";

try {
    // التحقق من وجود جدول courses أولاً
    $stmt = $conn->query("SHOW TABLES LIKE 'courses'");
    if ($stmt->rowCount() == 0) {
        echo "<p style='color: red;'>❌ جدول courses غير موجود. يجب إنشاؤه أولاً</p>";
        exit;
    }

    // التحقق من وجود جدول users أولاً
    $stmt = $conn->query("SHOW TABLES LIKE 'users'");
    if ($stmt->rowCount() == 0) {
        echo "<p style='color: red;'>❌ جدول users غير موجود. يجب إنشاؤه أولاً</p>";
        exit;
    }

    // إنشاء جدول فصول الكورسات
    $conn->exec("CREATE TABLE IF NOT EXISTS course_chapters (
        id INT AUTO_INCREMENT PRIMARY KEY,
        course_id INT NOT NULL,
        title VARCHAR(255) NOT NULL,
        description TEXT,
        order_number INT DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_course_id (course_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    echo "<p style='color: green;'>✅ تم إنشاء جدول course_chapters</p>";

    // إنشاء جدول فيديوهات الكورسات
    $conn->exec("CREATE TABLE IF NOT EXISTS course_videos (
        id INT AUTO_INCREMENT PRIMARY KEY,
        course_id INT NOT NULL,
        chapter_id INT NULL,
        title VARCHAR(255) NOT NULL,
        description TEXT,
        video_url VARCHAR(500) NOT NULL,
        duration INT DEFAULT 0,
        is_free TINYINT(1) DEFAULT 0,
        order_number INT DEFAULT 1,
        uploaded_by INT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_course_id (course_id),
        INDEX idx_chapter_id (chapter_id),
        INDEX idx_uploaded_by (uploaded_by)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    echo "<p style='color: green;'>✅ تم إنشاء جدول course_videos</p>";

    // إنشاء جدول مشاهدات الفيديوهات
    $conn->exec("CREATE TABLE IF NOT EXISTS video_watches (
        id INT AUTO_INCREMENT PRIMARY KEY,
        video_id INT NOT NULL,
        user_id INT NOT NULL,
        watch_percentage DECIMAL(5,2) DEFAULT 0,
        watch_duration INT DEFAULT 0,
        last_position INT DEFAULT 0,
        completed TINYINT(1) DEFAULT 0,
        started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        completed_at TIMESTAMP NULL,
        INDEX idx_video_id (video_id),
        INDEX idx_user_id (user_id),
        UNIQUE KEY unique_watch (video_id, user_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    echo "<p style='color: green;'>✅ تم إنشاء جدول video_watches</p>";

    // إدراج بيانات تجريبية للفصول
    $stmt = $conn->query("SELECT COUNT(*) FROM course_chapters");
    if ($stmt->fetchColumn() == 0) {
        echo "<h3>إدراج بيانات تجريبية للفصول:</h3>";
        
        // الحصول على الكورسات الموجودة
        $stmt = $conn->query("SELECT id, title FROM courses LIMIT 3");
        $courses = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        foreach ($courses as $course) {
            // التحقق من أعمدة الجدول أولاً
            $stmt = $conn->query("DESCRIBE course_chapters");
            $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);

            if (in_array('order_number', $columns)) {
                $conn->exec("INSERT INTO course_chapters (course_id, title, order_number) VALUES
                            ({$course['id']}, 'الفصل الأول - المقدمة', 1),
                            ({$course['id']}, 'الفصل الثاني - الأساسيات', 2),
                            ({$course['id']}, 'الفصل الثالث - التطبيق العملي', 3)");
            } else {
                // استخدام اسم العمود الصحيح
                $conn->exec("INSERT INTO course_chapters (course_id, title) VALUES
                            ({$course['id']}, 'الفصل الأول - المقدمة'),
                            ({$course['id']}, 'الفصل الثاني - الأساسيات'),
                            ({$course['id']}, 'الفصل الثالث - التطبيق العملي')");
            }
            echo "<p>✅ تم إدراج فصول للكورس: " . htmlspecialchars($course['title']) . "</p>";
        }
    }

    // إدراج بيانات تجريبية للفيديوهات
    $stmt = $conn->query("SELECT COUNT(*) FROM course_videos");
    if ($stmt->fetchColumn() == 0) {
        echo "<h3>إدراج بيانات تجريبية للفيديوهات:</h3>";
        
        // الحصول على المدرب والكورسات والفصول
        $stmt = $conn->query("SELECT id FROM users WHERE role = 'instructor' LIMIT 1");
        $instructor_id = $stmt->fetchColumn();
        
        if ($instructor_id) {
            $stmt = $conn->query("SELECT c.id as course_id, ch.id as chapter_id 
                                 FROM courses c 
                                 LEFT JOIN course_chapters ch ON c.id = ch.course_id 
                                 WHERE c.instructor_id = $instructor_id 
                                 LIMIT 6");
            $course_chapters = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            $sample_videos = [
                ['title' => 'مقدمة في البرمجة', 'description' => 'فيديو تعريفي عن أساسيات البرمجة', 'duration' => 15, 'is_free' => 1],
                ['title' => 'المتغيرات والثوابت', 'description' => 'شرح المتغيرات وأنواع البيانات', 'duration' => 25, 'is_free' => 1],
                ['title' => 'الحلقات التكرارية', 'description' => 'استخدام for و while loops', 'duration' => 30, 'is_free' => 0],
                ['title' => 'الدوال والوظائف', 'description' => 'إنشاء واستخدام الدوال', 'duration' => 35, 'is_free' => 0],
                ['title' => 'التعامل مع قواعد البيانات', 'description' => 'الاتصال بقاعدة البيانات', 'duration' => 40, 'is_free' => 0],
                ['title' => 'مشروع تطبيقي', 'description' => 'بناء تطبيق كامل', 'duration' => 60, 'is_free' => 0]
            ];
            
            foreach ($course_chapters as $index => $cc) {
                if (isset($sample_videos[$index])) {
                    $video = $sample_videos[$index];
                    $video_url = 'https://www.youtube.com/watch?v=dQw4w9WgXcQ'; // رابط تجريبي
                    
                    // التحقق من أعمدة جدول course_videos
                    $stmt = $conn->query("DESCRIBE course_videos");
                    $video_columns = $stmt->fetchAll(PDO::FETCH_COLUMN);

                    if (in_array('order_number', $video_columns)) {
                        $conn->exec("INSERT INTO course_videos
                                    (course_id, chapter_id, title, description, video_url, duration, is_free, order_number, uploaded_by)
                                    VALUES
                                    ({$cc['course_id']}, " . ($cc['chapter_id'] ? $cc['chapter_id'] : 'NULL') . ", '{$video['title']}', '{$video['description']}',
                                     '$video_url', {$video['duration']}, {$video['is_free']}, " . ($index + 1) . ", $instructor_id)");
                    } else {
                        $conn->exec("INSERT INTO course_videos
                                    (course_id, chapter_id, title, description, video_url, duration, is_free, uploaded_by)
                                    VALUES
                                    ({$cc['course_id']}, " . ($cc['chapter_id'] ? $cc['chapter_id'] : 'NULL') . ", '{$video['title']}', '{$video['description']}',
                                     '$video_url', {$video['duration']}, {$video['is_free']}, $instructor_id)");
                    }
                    
                    echo "<p>✅ تم إدراج فيديو: " . $video['title'] . "</p>";
                }
            }
        }
    }

    // إدراج بيانات تجريبية للمشاهدات
    $stmt = $conn->query("SELECT COUNT(*) FROM video_watches");
    if ($stmt->fetchColumn() == 0) {
        echo "<h3>إدراج بيانات تجريبية للمشاهدات:</h3>";
        
        $stmt = $conn->query("SELECT v.id as video_id, u.id as user_id 
                             FROM course_videos v, users u 
                             WHERE u.role = 'student' 
                             LIMIT 10");
        $watches = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        foreach ($watches as $watch) {
            $percentage = rand(20, 100);
            $completed = $percentage >= 90 ? 1 : 0;
            
            $conn->exec("INSERT INTO video_watches 
                        (video_id, user_id, watch_percentage, completed) 
                        VALUES 
                        ({$watch['video_id']}, {$watch['user_id']}, $percentage, $completed)");
        }
        echo "<p>✅ تم إدراج " . count($watches) . " مشاهدة تجريبية</p>";
    }

    // إضافة بعض الفيديوهات التجريبية إذا لم تكن موجودة
    $stmt = $conn->query("SELECT COUNT(*) FROM course_videos");
    if ($stmt->fetchColumn() == 0) {
        echo "<h3>إضافة فيديوهات تجريبية:</h3>";

        // الحصول على المدرب والكورسات
        $stmt = $conn->query("SELECT id FROM users WHERE role = 'instructor' LIMIT 1");
        $instructor_id = $stmt->fetchColumn();

        if ($instructor_id) {
            $stmt = $conn->query("SELECT id, title FROM courses WHERE instructor_id = $instructor_id LIMIT 3");
            $courses = $stmt->fetchAll(PDO::FETCH_ASSOC);

            $sample_videos = [
                [
                    'title' => 'مقدمة في البرمجة',
                    'description' => 'فيديو تعريفي عن أساسيات البرمجة وأهميتها في العصر الحديث',
                    'video_url' => 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
                    'duration' => 15,
                    'is_free' => 1
                ],
                [
                    'title' => 'المتغيرات والثوابت',
                    'description' => 'شرح مفصل عن المتغيرات وأنواع البيانات المختلفة',
                    'video_url' => 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
                    'duration' => 25,
                    'is_free' => 1
                ],
                [
                    'title' => 'الحلقات التكرارية',
                    'description' => 'استخدام for و while loops في البرمجة',
                    'video_url' => 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
                    'duration' => 30,
                    'is_free' => 0
                ],
                [
                    'title' => 'الدوال والوظائف',
                    'description' => 'إنشاء واستخدام الدوال في البرمجة',
                    'video_url' => 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
                    'duration' => 35,
                    'is_free' => 0
                ],
                [
                    'title' => 'التعامل مع قواعد البيانات',
                    'description' => 'الاتصال بقاعدة البيانات وتنفيذ الاستعلامات',
                    'video_url' => 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
                    'duration' => 40,
                    'is_free' => 0
                ],
                [
                    'title' => 'مشروع تطبيقي شامل',
                    'description' => 'بناء تطبيق كامل من الصفر',
                    'video_url' => 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
                    'duration' => 60,
                    'is_free' => 0
                ]
            ];

            foreach ($courses as $course_index => $course) {
                for ($i = 0; $i < 2; $i++) {
                    $video_index = ($course_index * 2) + $i;
                    if (isset($sample_videos[$video_index])) {
                        $video = $sample_videos[$video_index];

                        // التحقق من وجود عمود order_number
                        $stmt = $conn->query("DESCRIBE course_videos");
                        $video_columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
                        $has_order = in_array('order_number', $video_columns);

                        if ($has_order) {
                            $conn->exec("INSERT INTO course_videos
                                        (course_id, title, description, video_url, duration, is_free, order_number, uploaded_by)
                                        VALUES
                                        ({$course['id']}, '{$video['title']}', '{$video['description']}',
                                         '{$video['video_url']}', {$video['duration']}, {$video['is_free']}, " . ($i + 1) . ", $instructor_id)");
                        } else {
                            $conn->exec("INSERT INTO course_videos
                                        (course_id, title, description, video_url, duration, is_free, uploaded_by)
                                        VALUES
                                        ({$course['id']}, '{$video['title']}', '{$video['description']}',
                                         '{$video['video_url']}', {$video['duration']}, {$video['is_free']}, $instructor_id)");
                        }

                        echo "<p>✅ تم إدراج فيديو: " . $video['title'] . " للكورس: " . $course['title'] . "</p>";
                    }
                }
            }
        }
    }

    echo "<h3 style='color: green;'>✅ تم إنشاء جميع جداول الفيديوهات بنجاح!</h3>";
    echo "<p><a href='instructor/videos.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>الذهاب لصفحة الفيديوهات</a></p>";
    
    // عرض إحصائيات
    echo "<h3>الإحصائيات:</h3>";
    echo "<ul>";
    
    $stmt = $conn->query("SELECT COUNT(*) FROM course_chapters");
    echo "<li>الفصول: " . $stmt->fetchColumn() . "</li>";
    
    $stmt = $conn->query("SELECT COUNT(*) FROM course_videos");
    echo "<li>الفيديوهات: " . $stmt->fetchColumn() . "</li>";
    
    $stmt = $conn->query("SELECT COUNT(*) FROM video_watches");
    echo "<li>المشاهدات: " . $stmt->fetchColumn() . "</li>";
    
    echo "</ul>";
    
} catch (PDOException $e) {
    echo "<h3 style='color: red;'>❌ خطأ: " . $e->getMessage() . "</h3>";
}
?>
