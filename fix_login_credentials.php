<?php
/**
 * فحص وإصلاح بيانات تسجيل الدخول
 * Check and fix login credentials
 */

require_once 'config/database.php';

echo "<!DOCTYPE html>";
echo "<html dir='rtl' lang='ar'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>فحص وإصلاح بيانات تسجيل الدخول</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "</head>";
echo "<body>";
echo "<div class='container mt-4'>";
echo "<h2>🔐 فحص وإصلاح بيانات تسجيل الدخول</h2>";

try {
    // 1. فحص جدول المستخدمين
    echo "<h4>👥 فحص جدول المستخدمين</h4>";
    
    $stmt = $conn->query("SHOW TABLES LIKE 'users'");
    if ($stmt->rowCount() == 0) {
        // إنشاء جدول المستخدمين
        $conn->exec("
            CREATE TABLE users (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                email VARCHAR(255) UNIQUE NOT NULL,
                password VARCHAR(255) NOT NULL,
                role ENUM('admin', 'instructor', 'student') DEFAULT 'student',
                phone VARCHAR(20) DEFAULT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                status ENUM('active', 'inactive') DEFAULT 'active'
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        echo "<div class='alert alert-success'>✅ تم إنشاء جدول المستخدمين</div>";
    } else {
        echo "<div class='alert alert-info'>ℹ️ جدول المستخدمين موجود</div>";
    }

    // 2. فحص المستخدمين الموجودين
    echo "<h4>📋 المستخدمين الموجودين</h4>";
    
    $stmt = $conn->query("SELECT id, name, email, role, status FROM users ORDER BY role, id");
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($users)) {
        echo "<div class='table-responsive'>";
        echo "<table class='table table-striped'>";
        echo "<thead><tr><th>ID</th><th>الاسم</th><th>البريد الإلكتروني</th><th>الدور</th><th>الحالة</th></tr></thead>";
        echo "<tbody>";
        foreach ($users as $user) {
            $role_text = '';
            switch($user['role']) {
                case 'admin': $role_text = 'مدير'; break;
                case 'instructor': $role_text = 'مدرب'; break;
                case 'student': $role_text = 'طالب'; break;
            }
            echo "<tr>";
            echo "<td>{$user['id']}</td>";
            echo "<td>" . htmlspecialchars($user['name']) . "</td>";
            echo "<td>" . htmlspecialchars($user['email']) . "</td>";
            echo "<td><span class='badge bg-primary'>$role_text</span></td>";
            echo "<td><span class='badge bg-" . ($user['status'] === 'active' ? 'success' : 'danger') . "'>{$user['status']}</span></td>";
            echo "</tr>";
        }
        echo "</tbody></table>";
        echo "</div>";
    } else {
        echo "<div class='alert alert-warning'>⚠️ لا يوجد مستخدمين في النظام</div>";
    }

    // 3. إنشاء/تحديث المستخدمين الافتراضيين
    echo "<h4>🔧 إنشاء/تحديث المستخدمين الافتراضيين</h4>";
    
    // كلمات المرور المشفرة
    $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
    $instructor_password = password_hash('instructor123', PASSWORD_DEFAULT);
    $student_password = password_hash('student123', PASSWORD_DEFAULT);
    
    // المدير
    $stmt = $conn->prepare("SELECT id FROM users WHERE email = '<EMAIL>'");
    $stmt->execute();
    if ($stmt->rowCount() == 0) {
        $stmt = $conn->prepare("INSERT INTO users (name, email, password, role) VALUES (?, ?, ?, ?)");
        $stmt->execute(['مدير النظام', '<EMAIL>', $admin_password, 'admin']);
        echo "<div class='alert alert-success'>✅ تم إنشاء حساب المدير</div>";
    } else {
        $stmt = $conn->prepare("UPDATE users SET password = ?, name = ? WHERE email = '<EMAIL>'");
        $stmt->execute([$admin_password, 'مدير النظام']);
        echo "<div class='alert alert-info'>ℹ️ تم تحديث حساب المدير</div>";
    }
    
    // المدرب
    $stmt = $conn->prepare("SELECT id FROM users WHERE email = '<EMAIL>'");
    $stmt->execute();
    if ($stmt->rowCount() == 0) {
        $stmt = $conn->prepare("INSERT INTO users (name, email, password, role) VALUES (?, ?, ?, ?)");
        $stmt->execute(['أحمد محمد - مدرب', '<EMAIL>', $instructor_password, 'instructor']);
        echo "<div class='alert alert-success'>✅ تم إنشاء حساب المدرب</div>";
    } else {
        $stmt = $conn->prepare("UPDATE users SET password = ?, name = ? WHERE email = '<EMAIL>'");
        $stmt->execute([$instructor_password, 'أحمد محمد - مدرب']);
        echo "<div class='alert alert-info'>ℹ️ تم تحديث حساب المدرب</div>";
    }
    
    // الطالب
    $stmt = $conn->prepare("SELECT id FROM users WHERE email = '<EMAIL>'");
    $stmt->execute();
    if ($stmt->rowCount() == 0) {
        $stmt = $conn->prepare("INSERT INTO users (name, email, password, role) VALUES (?, ?, ?, ?)");
        $stmt->execute(['سارة أحمد - طالبة', '<EMAIL>', $student_password, 'student']);
        echo "<div class='alert alert-success'>✅ تم إنشاء حساب الطالب</div>";
    } else {
        $stmt = $conn->prepare("UPDATE users SET password = ?, name = ? WHERE email = '<EMAIL>'");
        $stmt->execute([$student_password, 'سارة أحمد - طالبة']);
        echo "<div class='alert alert-info'>ℹ️ تم تحديث حساب الطالب</div>";
    }

    // 4. إضافة مستخدمين إضافيين
    echo "<h4>👥 إضافة مستخدمين إضافيين</h4>";
    
    $additional_users = [
        ['محمد علي - مدرب', '<EMAIL>', $instructor_password, 'instructor'],
        ['فاطمة خالد - مدربة', '<EMAIL>', $instructor_password, 'instructor'],
        ['علي أحمد - طالب', '<EMAIL>', $student_password, 'student'],
        ['نور محمد - طالبة', '<EMAIL>', $student_password, 'student'],
        ['خالد سالم - طالب', '<EMAIL>', $student_password, 'student']
    ];
    
    foreach ($additional_users as $user_data) {
        $stmt = $conn->prepare("SELECT id FROM users WHERE email = ?");
        $stmt->execute([$user_data[1]]);
        if ($stmt->rowCount() == 0) {
            $stmt = $conn->prepare("INSERT INTO users (name, email, password, role) VALUES (?, ?, ?, ?)");
            $stmt->execute($user_data);
            echo "<div class='alert alert-success'>✅ تم إنشاء حساب: {$user_data[0]}</div>";
        }
    }

    // 5. اختبار تسجيل الدخول
    echo "<h4>🧪 اختبار تسجيل الدخول</h4>";
    
    $test_credentials = [
        ['<EMAIL>', 'admin123', 'مدير'],
        ['<EMAIL>', 'instructor123', 'مدرب'],
        ['<EMAIL>', 'student123', 'طالب']
    ];
    
    foreach ($test_credentials as $cred) {
        $stmt = $conn->prepare("SELECT id, name, password, role FROM users WHERE email = ?");
        $stmt->execute([$cred[0]]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($user && password_verify($cred[1], $user['password'])) {
            echo "<div class='alert alert-success'>";
            echo "✅ <strong>{$cred[2]}</strong> - البريد: {$cred[0]} | كلمة المرور: {$cred[1]} ✓";
            echo "</div>";
        } else {
            echo "<div class='alert alert-danger'>";
            echo "❌ <strong>{$cred[2]}</strong> - فشل في التحقق";
            echo "</div>";
        }
    }

    // 6. عرض بيانات تسجيل الدخول النهائية
    echo "<h4>🔑 بيانات تسجيل الدخول الصحيحة</h4>";
    
    echo "<div class='row'>";
    
    // المدير
    echo "<div class='col-md-4'>";
    echo "<div class='card border-danger'>";
    echo "<div class='card-header bg-danger text-white'>";
    echo "<h6 class='mb-0'><i class='fas fa-user-shield'></i> مدير النظام</h6>";
    echo "</div>";
    echo "<div class='card-body'>";
    echo "<p><strong>البريد:</strong> <EMAIL></p>";
    echo "<p><strong>كلمة المرور:</strong> admin123</p>";
    echo "<p><strong>الصلاحيات:</strong> إدارة النظام بالكامل</p>";
    echo "</div>";
    echo "</div>";
    echo "</div>";
    
    // المدرب
    echo "<div class='col-md-4'>";
    echo "<div class='card border-primary'>";
    echo "<div class='card-header bg-primary text-white'>";
    echo "<h6 class='mb-0'><i class='fas fa-chalkboard-teacher'></i> مدرب</h6>";
    echo "</div>";
    echo "<div class='card-body'>";
    echo "<p><strong>البريد:</strong> <EMAIL></p>";
    echo "<p><strong>كلمة المرور:</strong> instructor123</p>";
    echo "<p><strong>الصلاحيات:</strong> إدارة الكورسات والطلاب</p>";
    echo "</div>";
    echo "</div>";
    echo "</div>";
    
    // الطالب
    echo "<div class='col-md-4'>";
    echo "<div class='card border-success'>";
    echo "<div class='card-header bg-success text-white'>";
    echo "<h6 class='mb-0'><i class='fas fa-user-graduate'></i> طالب</h6>";
    echo "</div>";
    echo "<div class='card-body'>";
    echo "<p><strong>البريد:</strong> <EMAIL></p>";
    echo "<p><strong>كلمة المرور:</strong> student123</p>";
    echo "<p><strong>الصلاحيات:</strong> الوصول للكورسات والجلسات</p>";
    echo "</div>";
    echo "</div>";
    echo "</div>";
    
    echo "</div>";

    // 7. فحص ملف تسجيل الدخول
    echo "<h4>📄 فحص ملف تسجيل الدخول</h4>";
    
    if (file_exists('login.php')) {
        echo "<div class='alert alert-success'>✅ ملف login.php موجود</div>";
    } else {
        echo "<div class='alert alert-danger'>❌ ملف login.php غير موجود</div>";
    }
    
    if (file_exists('includes/session_config.php')) {
        echo "<div class='alert alert-success'>✅ ملف session_config.php موجود</div>";
    } else {
        echo "<div class='alert alert-danger'>❌ ملف session_config.php غير موجود</div>";
    }

    // 8. إحصائيات نهائية
    echo "<h4>📊 إحصائيات المستخدمين</h4>";
    
    $stmt = $conn->query("
        SELECT 
            COUNT(*) as total,
            COUNT(CASE WHEN role = 'admin' THEN 1 END) as admins,
            COUNT(CASE WHEN role = 'instructor' THEN 1 END) as instructors,
            COUNT(CASE WHEN role = 'student' THEN 1 END) as students,
            COUNT(CASE WHEN status = 'active' THEN 1 END) as active_users
        FROM users
    ");
    $stats = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "<div class='row'>";
    echo "<div class='col-md-2'>";
    echo "<div class='card bg-primary text-white'>";
    echo "<div class='card-body text-center'>";
    echo "<h3>{$stats['total']}</h3>";
    echo "<p class='mb-0'>إجمالي</p>";
    echo "</div></div></div>";
    
    echo "<div class='col-md-2'>";
    echo "<div class='card bg-danger text-white'>";
    echo "<div class='card-body text-center'>";
    echo "<h3>{$stats['admins']}</h3>";
    echo "<p class='mb-0'>مدراء</p>";
    echo "</div></div></div>";
    
    echo "<div class='col-md-2'>";
    echo "<div class='card bg-info text-white'>";
    echo "<div class='card-body text-center'>";
    echo "<h3>{$stats['instructors']}</h3>";
    echo "<p class='mb-0'>مدربين</p>";
    echo "</div></div></div>";
    
    echo "<div class='col-md-2'>";
    echo "<div class='card bg-success text-white'>";
    echo "<div class='card-body text-center'>";
    echo "<h3>{$stats['students']}</h3>";
    echo "<p class='mb-0'>طلاب</p>";
    echo "</div></div></div>";
    
    echo "<div class='col-md-2'>";
    echo "<div class='card bg-warning text-white'>";
    echo "<div class='card-body text-center'>";
    echo "<h3>{$stats['active_users']}</h3>";
    echo "<p class='mb-0'>نشط</p>";
    echo "</div></div></div>";
    
    echo "</div>";

    echo "<div class='alert alert-success mt-4'>";
    echo "<h5>🎉 تم إصلاح بيانات تسجيل الدخول بنجاح!</h5>";
    echo "<p>يمكنك الآن تسجيل الدخول باستخدام البيانات المعروضة أعلاه.</p>";
    echo "</div>";

} catch (Exception $e) {
    echo "<div class='alert alert-danger'>❌ خطأ: " . htmlspecialchars($e->getMessage()) . "</div>";
}

echo "<div class='mt-4'>";
echo "<a href='login.php' class='btn btn-primary btn-lg me-2'>🔐 تسجيل الدخول</a>";
echo "<a href='system_health_check.php' class='btn btn-info btn-lg me-2'>🔍 فحص النظام</a>";
echo "<a href='index.php' class='btn btn-success btn-lg'>🏠 الصفحة الرئيسية</a>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>
