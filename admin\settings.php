<?php
require_once '../config/database.php';
require_once '../includes/functions.php';


$success = '';
$error = '';

// Create settings table if not exists
try {
    $conn->exec("
        CREATE TABLE IF NOT EXISTS settings (
            name VARCHAR(50) PRIMARY KEY,
            value TEXT
        )
    ");
    
    // Insert default settings if not exist
    $default_settings = [
        'zoom_api_key' => '',
        'zoom_api_secret' => '',
        'smtp_host' => '',
        'smtp_port' => '587',
        'smtp_username' => '',
        'smtp_password' => ''
    ];
    
    foreach ($default_settings as $name => $value) {
        $stmt = $conn->prepare("INSERT IGNORE INTO settings (name, value) VALUES (?, ?)");
        $stmt->execute([$name, $value]);
    }
} catch(PDOException $e) {
    $error = 'حدث خطأ في إعداد قاعدة البيانات';
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['update_zoom'])) {
        $zoom_api_key = trim($_POST['zoom_api_key']);
        $zoom_api_secret = trim($_POST['zoom_api_secret']);
        
        if (empty($zoom_api_key) || empty($zoom_api_secret)) {
            $error = 'جميع حقول إعدادات Zoom مطلوبة';
        } else {
            try {
                $stmt = $conn->prepare("UPDATE settings SET value = ? WHERE name = 'zoom_api_key'");
                $stmt->execute([$zoom_api_key]);
                
                $stmt = $conn->prepare("UPDATE settings SET value = ? WHERE name = 'zoom_api_secret'");
                $stmt->execute([$zoom_api_secret]);
                
                $success = 'تم تحديث إعدادات Zoom بنجاح';
            } catch(PDOException $e) {
                $error = 'حدث خطأ أثناء تحديث إعدادات Zoom';
            }
        }
    }
    
    if (isset($_POST['update_email'])) {
        $smtp_host = trim($_POST['smtp_host']);
        $smtp_port = trim($_POST['smtp_port']);
        $smtp_username = trim($_POST['smtp_username']);
        $smtp_password = trim($_POST['smtp_password']);
        
        if (empty($smtp_host) || empty($smtp_port) || empty($smtp_username)) {
            $error = 'جميع حقول إعدادات البريد الإلكتروني مطلوبة ما عدا كلمة المرور إذا لم ترد تغييرها';
        } else {
            try {
                $stmt = $conn->prepare("UPDATE settings SET value = ? WHERE name = 'smtp_host'");
                $stmt->execute([$smtp_host]);
                
                $stmt = $conn->prepare("UPDATE settings SET value = ? WHERE name = 'smtp_port'");
                $stmt->execute([$smtp_port]);
                
                $stmt = $conn->prepare("UPDATE settings SET value = ? WHERE name = 'smtp_username'");
                $stmt->execute([$smtp_username]);
                
                if (!empty($smtp_password)) {
                    $stmt = $conn->prepare("UPDATE settings SET value = ? WHERE name = 'smtp_password'");
                    $stmt->execute([$smtp_password]);
                }
                
                $success = 'تم تحديث إعدادات البريد الإلكتروني بنجاح';
            } catch(PDOException $e) {
                $error = 'حدث خطأ أثناء تحديث إعدادات البريد الإلكتروني';
            }
        }
    }
}

// Get current settings
try {
    $stmt = $conn->query("SELECT * FROM settings");
    $settings = [];
    while ($row = $stmt->fetch()) {
        $settings[$row['name']] = $row['value'];
    }
} catch(PDOException $e) {
    $error = 'حدث خطأ في جلب الإعدادات';
}

$pageTitle = 'الإعدادات';
require_once '../includes/header.php';
?>

<div class="container py-4">
    <?php if ($success): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle"></i> <?php echo $success; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>
    
    <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle"></i> <?php echo $error; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <!-- Zoom Settings -->
        <div class="col-md-6 mb-4">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0"><i class="fas fa-video me-2"></i>إعدادات Zoom</h5>
                </div>
                <div class="card-body">
                    <form method="POST" class="needs-validation" novalidate>
                        <div class="mb-3">
                            <label for="zoom_api_key" class="form-label">API Key</label>
                            <input type="text" class="form-control" id="zoom_api_key" name="zoom_api_key" 
                                   value="<?php echo htmlspecialchars($settings['zoom_api_key'] ?? ''); ?>" required>
                            <div class="invalid-feedback">هذا الحقل مطلوب</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="zoom_api_secret" class="form-label">API Secret</label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="zoom_api_secret" name="zoom_api_secret" 
                                       value="<?php echo htmlspecialchars($settings['zoom_api_secret'] ?? ''); ?>" required>
                                <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('zoom_api_secret')">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <div class="invalid-feedback">هذا الحقل مطلوب</div>
                            </div>
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" name="update_zoom" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>حفظ إعدادات Zoom
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Email Settings -->
        <div class="col-md-6 mb-4">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0"><i class="fas fa-envelope me-2"></i>إعدادات البريد الإلكتروني</h5>
                </div>
                <div class="card-body">
                    <form method="POST" class="needs-validation" novalidate>
                        <div class="mb-3">
                            <label for="smtp_host" class="form-label">SMTP Host</label>
                            <input type="text" class="form-control" id="smtp_host" name="smtp_host" 
                                   value="<?php echo htmlspecialchars($settings['smtp_host'] ?? ''); ?>" required>
                            <div class="invalid-feedback">هذا الحقل مطلوب</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="smtp_port" class="form-label">SMTP Port</label>
                            <input type="number" class="form-control" id="smtp_port" name="smtp_port" 
                                   value="<?php echo htmlspecialchars($settings['smtp_port'] ?? '587'); ?>" required>
                            <div class="invalid-feedback">هذا الحقل مطلوب</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="smtp_username" class="form-label">SMTP Username</label>
                            <input type="text" class="form-control" id="smtp_username" name="smtp_username" 
                                   value="<?php echo htmlspecialchars($settings['smtp_username'] ?? ''); ?>" required>
                            <div class="invalid-feedback">هذا الحقل مطلوب</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="smtp_password" class="form-label">SMTP Password</label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="smtp_password" name="smtp_password" 
                                       placeholder="اتركه فارغاً إذا لم ترد تغييره">
                                <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('smtp_password')">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            <div class="form-text">اتركه فارغاً إذا لم ترد تغييره</div>
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" name="update_email" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>حفظ إعدادات البريد
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Form validation
(function () {
    'use strict'
    var forms = document.querySelectorAll('.needs-validation')
    Array.prototype.slice.call(forms).forEach(function (form) {
        form.addEventListener('submit', function (event) {
            if (!form.checkValidity()) {
                event.preventDefault()
                event.stopPropagation()
            }
            form.classList.add('was-validated')
        }, false)
    })
})()

// Toggle password visibility
function togglePassword(inputId) {
    const input = document.getElementById(inputId);
    const icon = event.currentTarget.querySelector('i');
    
    if (input.type === 'password') {
        input.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        input.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
}
</script>

<?php require_once '../includes/footer.php'; ?>
        <!-- Zoom Settings -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">إعدادات Zoom</h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="mb-3">
                            <label for="zoom_api_key" class="form-label">API Key</label>
                            <input type="text" class="form-control" id="zoom_api_key" name="zoom_api_key" 
                                   value="<?php echo htmlspecialchars($settings['zoom_api_key'] ?? ''); ?>" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="zoom_api_secret" class="form-label">API Secret</label>
                            <input type="password" class="form-control" id="zoom_api_secret" name="zoom_api_secret" 
                                   value="<?php echo htmlspecialchars($settings['zoom_api_secret'] ?? ''); ?>" required>
                        </div>
                        
                        <button type="submit" name="update_zoom" class="btn btn-primary">حفظ إعدادات Zoom</button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Email Settings -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">إعدادات البريد الإلكتروني</h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="mb-3">
                            <label for="smtp_host" class="form-label">SMTP Host</label>
                            <input type="text" class="form-control" id="smtp_host" name="smtp_host" 
                                   value="<?php echo htmlspecialchars($settings['smtp_host'] ?? ''); ?>" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="smtp_port" class="form-label">SMTP Port</label>
                            <input type="number" class="form-control" id="smtp_port" name="smtp_port" 
                                   value="<?php echo htmlspecialchars($settings['smtp_port'] ?? ''); ?>" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="smtp_username" class="form-label">SMTP Username</label>
                            <input type="text" class="form-control" id="smtp_username" name="smtp_username" 
                                   value="<?php echo htmlspecialchars($settings['smtp_username'] ?? ''); ?>" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="smtp_password" class="form-label">SMTP Password</label>
                            <input type="password" class="form-control" id="smtp_password" name="smtp_password" 
                                   placeholder="اتركه فارغاً إذا لم ترد تغييره">
                        </div>
                        
                        <button type="submit" name="update_email" class="btn btn-primary">حفظ إعدادات البريد</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once '../includes/footer.php'; ?>
