<?php
/**
 * إصلاح فوري لتسجيل الدخول
 * Instant login fix
 */

require_once 'config/database.php';

echo "<!DOCTYPE html>";
echo "<html dir='rtl' lang='ar'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>إصلاح فوري</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "</head>";
echo "<body>";
echo "<div class='container mt-4'>";
echo "<h2>⚡ إصلاح فوري لتسجيل الدخول</h2>";

try {
    // 1. حذف جميع المستخدمين الموجودين
    echo "<h4>🗑️ تنظيف المستخدمين</h4>";
    $conn->exec("DELETE FROM users");
    echo "<div class='alert alert-success'>✅ تم حذف جميع المستخدمين القدامى</div>";

    // 2. إنشاء المستخدمين من جديد
    echo "<h4>👥 إنشاء مستخدمين جدد</h4>";
    
    $users = [
        ['مدير النظام', '<EMAIL>', 'admin123', 'admin'],
        ['أحمد محمد - مدرب', '<EMAIL>', 'instructor123', 'instructor'],
        ['سارة أحمد - طالبة', '<EMAIL>', 'student123', 'student']
    ];
    
    $stmt = $conn->prepare("INSERT INTO users (name, email, password, role, status, created_at) VALUES (?, ?, ?, ?, 'active', NOW())");
    
    foreach ($users as $user) {
        $hashed_password = password_hash($user[2], PASSWORD_DEFAULT);
        $stmt->execute([$user[0], $user[1], $hashed_password, $user[3]]);
        echo "<div class='alert alert-success'>✅ تم إنشاء: {$user[0]} - {$user[1]}</div>";
    }

    // 3. اختبار المستخدمين
    echo "<h4>🧪 اختبار تسجيل الدخول</h4>";
    
    foreach ($users as $user) {
        $stmt = $conn->prepare("SELECT * FROM users WHERE email = ?");
        $stmt->execute([$user[1]]);
        $db_user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($db_user && password_verify($user[2], $db_user['password'])) {
            echo "<div class='alert alert-success'>";
            echo "✅ <strong>{$user[0]}</strong><br>";
            echo "البريد: <code>{$user[1]}</code><br>";
            echo "كلمة المرور: <code>{$user[2]}</code><br>";
            echo "الدور: <span class='badge bg-primary'>{$user[3]}</span>";
            echo "</div>";
        } else {
            echo "<div class='alert alert-danger'>❌ مشكلة في: {$user[0]}</div>";
        }
    }

    // 4. عرض بيانات تسجيل الدخول
    echo "<h4>🔑 بيانات تسجيل الدخول الجاهزة</h4>";
    
    echo "<div class='row'>";
    
    // المدير
    echo "<div class='col-md-4'>";
    echo "<div class='card border-danger'>";
    echo "<div class='card-header bg-danger text-white text-center'>";
    echo "<h5 class='mb-0'>👨‍💼 مدير</h5>";
    echo "</div>";
    echo "<div class='card-body'>";
    echo "<p><strong>البريد:</strong></p>";
    echo "<input type='text' class='form-control mb-2' value='<EMAIL>' readonly onclick='this.select()'>";
    echo "<p><strong>كلمة المرور:</strong></p>";
    echo "<input type='text' class='form-control mb-2' value='admin123' readonly onclick='this.select()'>";
    echo "<p class='text-center'><span class='badge bg-success'>✅ جاهز</span></p>";
    echo "</div>";
    echo "</div>";
    echo "</div>";
    
    // المدرب
    echo "<div class='col-md-4'>";
    echo "<div class='card border-primary'>";
    echo "<div class='card-header bg-primary text-white text-center'>";
    echo "<h5 class='mb-0'>👨‍🏫 مدرب</h5>";
    echo "</div>";
    echo "<div class='card-body'>";
    echo "<p><strong>البريد:</strong></p>";
    echo "<input type='text' class='form-control mb-2' value='<EMAIL>' readonly onclick='this.select()'>";
    echo "<p><strong>كلمة المرور:</strong></p>";
    echo "<input type='text' class='form-control mb-2' value='instructor123' readonly onclick='this.select()'>";
    echo "<p class='text-center'><span class='badge bg-success'>✅ جاهز</span></p>";
    echo "</div>";
    echo "</div>";
    echo "</div>";
    
    // الطالب
    echo "<div class='col-md-4'>";
    echo "<div class='card border-success'>";
    echo "<div class='card-header bg-success text-white text-center'>";
    echo "<h5 class='mb-0'>👨‍🎓 طالب</h5>";
    echo "</div>";
    echo "<div class='card-body'>";
    echo "<p><strong>البريد:</strong></p>";
    echo "<input type='text' class='form-control mb-2' value='<EMAIL>' readonly onclick='this.select()'>";
    echo "<p><strong>كلمة المرور:</strong></p>";
    echo "<input type='text' class='form-control mb-2' value='student123' readonly onclick='this.select()'>";
    echo "<p class='text-center'><span class='badge bg-success'>✅ جاهز</span></p>";
    echo "</div>";
    echo "</div>";
    echo "</div>";
    
    echo "</div>";

    echo "<div class='alert alert-success mt-4'>";
    echo "<h5>🎉 تم الإصلاح بنجاح!</h5>";
    echo "<p>جميع الحسابات جاهزة للاستخدام. انسخ البيانات من الحقول أعلاه.</p>";
    echo "</div>";

} catch (Exception $e) {
    echo "<div class='alert alert-danger'>❌ خطأ: " . htmlspecialchars($e->getMessage()) . "</div>";
}

echo "<div class='text-center mt-4'>";
echo "<a href='login.php' class='btn btn-primary btn-lg'>🔐 تسجيل الدخول الآن</a>";
echo "</div>";

echo "<div class='mt-4'>";
echo "<div class='alert alert-info'>";
echo "<h6>📋 خطوات تسجيل الدخول:</h6>";
echo "<ol>";
echo "<li>انسخ البريد الإلكتروني من الحقل أعلاه</li>";
echo "<li>انسخ كلمة المرور من الحقل أعلاه</li>";
echo "<li>اذهب لصفحة تسجيل الدخول</li>";
echo "<li>اختر نوع الحساب المناسب</li>";
echo "<li>الصق البيانات المنسوخة</li>";
echo "</ol>";
echo "</div>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>
