<?php
require_once 'config/database.php';

$request_found = false;
$request_data = null;
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = filter_var($_POST['email'], FILTER_SANITIZE_EMAIL);
    
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = 'البريد الإلكتروني غير صالح';
    } else {
        try {
            $stmt = $conn->prepare("SELECT * FROM join_requests WHERE email = ? ORDER BY created_at DESC LIMIT 1");
            $stmt->execute([$email]);
            $request_data = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($request_data) {
                $request_found = true;
            } else {
                $error = 'لم يتم العثور على طلب انضمام بهذا البريد الإلكتروني';
            }
        } catch (Exception $e) {
            $error = 'حدث خطأ أثناء البحث: ' . $e->getMessage();
        }
    }
}
?>
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فحص حالة طلب الانضمام - منصة التعلم</title>
    
    <!-- Bootstrap و Font Awesome -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/main.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Cairo', sans-serif;
        }
        
        .status-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            max-width: 600px;
            margin: 50px auto;
        }
        
        .status-header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .status-body {
            padding: 40px;
        }
        
        .status-badge {
            font-size: 1.2em;
            padding: 10px 20px;
            border-radius: 50px;
        }
        
        .status-pending {
            background: #ffc107;
            color: #000;
        }
        
        .status-approved {
            background: #28a745;
            color: white;
        }
        
        .status-rejected {
            background: #dc3545;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="status-card">
            <div class="status-header">
                <h2><i class="fas fa-search me-3"></i>فحص حالة طلب الانضمام</h2>
                <p class="mb-0">أدخل بريدك الإلكتروني للتحقق من حالة طلبك</p>
            </div>
            
            <div class="status-body">
                <?php if ($error): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <?php echo htmlspecialchars($error); ?>
                    </div>
                <?php endif; ?>
                
                <?php if ($request_found): ?>
                    <div class="alert alert-info">
                        <h5><i class="fas fa-info-circle me-2"></i>تفاصيل طلب الانضمام</h5>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>الاسم:</strong> <?php echo htmlspecialchars($request_data['name']); ?></p>
                            <p><strong>البريد الإلكتروني:</strong> <?php echo htmlspecialchars($request_data['email']); ?></p>
                            <p><strong>رقم الهاتف:</strong> <?php echo htmlspecialchars($request_data['phone']); ?></p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>تاريخ التقديم:</strong> <?php echo date('Y-m-d H:i', strtotime($request_data['created_at'])); ?></p>
                            <p><strong>الحالة:</strong></p>
                            <span class="status-badge status-<?php echo $request_data['status']; ?>">
                                <?php
                                switch($request_data['status']) {
                                    case 'pending': echo 'قيد المراجعة'; break;
                                    case 'approved': echo 'مقبول'; break;
                                    case 'rejected': echo 'مرفوض'; break;
                                    default: echo $request_data['status'];
                                }
                                ?>
                            </span>
                        </div>
                    </div>
                    
                    <?php if ($request_data['message']): ?>
                        <div class="mt-3">
                            <p><strong>الرسالة:</strong></p>
                            <div class="alert alert-light">
                                <?php echo nl2br(htmlspecialchars($request_data['message'])); ?>
                            </div>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($request_data['status'] === 'approved'): ?>
                        <div class="alert alert-success mt-3">
                            <h6><i class="fas fa-check-circle me-2"></i>تهانينا!</h6>
                            <p class="mb-0">تم قبول طلبك. يمكنك الآن <a href="login.php" class="alert-link">تسجيل الدخول</a> للوصول إلى المنصة.</p>
                        </div>
                    <?php elseif ($request_data['status'] === 'rejected'): ?>
                        <div class="alert alert-danger mt-3">
                            <h6><i class="fas fa-times-circle me-2"></i>تم رفض الطلب</h6>
                            <?php if ($request_data['rejection_reason']): ?>
                                <p><strong>سبب الرفض:</strong> <?php echo htmlspecialchars($request_data['rejection_reason']); ?></p>
                            <?php endif; ?>
                            <p class="mb-0">يمكنك <a href="join-request.php" class="alert-link">تقديم طلب جديد</a> إذا رغبت في ذلك.</p>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-warning mt-3">
                            <h6><i class="fas fa-clock me-2"></i>طلبك قيد المراجعة</h6>
                            <p class="mb-0">سيتم مراجعة طلبك من قبل الإدارة وستحصل على رد قريباً.</p>
                        </div>
                    <?php endif; ?>
                    
                    <div class="text-center mt-4">
                        <a href="check-request-status.php" class="btn btn-primary">
                            <i class="fas fa-search me-1"></i>فحص طلب آخر
                        </a>
                    </div>
                <?php else: ?>
                    <form method="POST" action="">
                        <div class="mb-3">
                            <label for="email" class="form-label">
                                <i class="fas fa-envelope me-2"></i>البريد الإلكتروني
                            </label>
                            <input type="email" class="form-control" id="email" name="email" 
                                   value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>" 
                                   placeholder="أدخل بريدك الإلكتروني" required>
                        </div>
                        
                        <div class="text-center">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search me-2"></i>فحص الحالة
                            </button>
                        </div>
                    </form>
                    
                    <div class="text-center mt-4">
                        <p class="text-muted">ليس لديك طلب انضمام؟</p>
                        <a href="join-request.php" class="btn btn-outline-success">
                            <i class="fas fa-user-plus me-1"></i>تقديم طلب انضمام
                        </a>
                    </div>
                <?php endif; ?>
                
                <div class="text-center mt-4">
                    <a href="index.php" class="btn btn-outline-primary">
                        <i class="fas fa-home me-1"></i>العودة للرئيسية
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
