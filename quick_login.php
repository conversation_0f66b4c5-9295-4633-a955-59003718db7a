<?php
session_start();
require_once 'config/database.php';

// تسجيل دخول سريع للمدرب التجريبي
try {
    $stmt = $conn->prepare("SELECT * FROM users WHERE email = '<EMAIL>' AND role = 'instructor'");
    $stmt->execute();
    $user = $stmt->fetch();
    
    if ($user) {
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['user_name'] = $user['name'];
        $_SESSION['user_email'] = $user['email'];
        $_SESSION['user_role'] = $user['role'];
        $_SESSION['is_logged_in'] = true;
        
        echo "<h2>تم تسجيل الدخول بنجاح!</h2>";
        echo "<p>مرحباً " . $user['name'] . "</p>";
        echo "<p>البريد الإلكتروني: " . $user['email'] . "</p>";
        echo "<p>الدور: " . $user['role'] . "</p>";
        echo "<br><a href='instructor/quizzes.php' class='btn btn-primary'>الذهاب لصفحة الاختبارات</a>";
        echo "<br><br><a href='instructor/create-quiz.php' class='btn btn-success'>إنشاء اختبار جديد</a>";
    } else {
        echo "<h2>لم يتم العثور على المدرب التجريبي</h2>";
        echo "<p>يرجى تشغيل create_sample_data.php أولاً</p>";
        echo "<a href='create_sample_data.php' class='btn btn-warning'>إنشاء البيانات التجريبية</a>";
    }
} catch (Exception $e) {
    echo "<h2>خطأ في تسجيل الدخول</h2>";
    echo "<p>الخطأ: " . $e->getMessage() . "</p>";
}
?>

<style>
body { 
    font-family: Arial, sans-serif; 
    margin: 20px; 
    background: #f8f9fa;
}
.btn { 
    display: inline-block;
    padding: 10px 20px; 
    margin: 5px;
    color: white; 
    text-decoration: none; 
    border-radius: 5px; 
}
.btn-primary { background: #007bff; }
.btn-success { background: #28a745; }
.btn-warning { background: #ffc107; color: #212529; }
</style>
