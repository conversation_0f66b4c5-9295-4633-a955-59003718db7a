<?php
require_once 'config/database.php';

try {
    // Create security tables
    $conn->exec("CREATE TABLE IF NOT EXISTS blocked_ips (
        id INT AUTO_INCREMENT PRIMARY KEY,
        ip VARCHAR(45) NOT NULL,
        blocked_until DATETIME NOT NULL,
        reason VARCHAR(255),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )");

    // Add failed_attempts and lock_expires columns to users table
    $conn->exec("ALTER TABLE users ADD COLUMN IF NOT EXISTS failed_attempts INT DEFAULT 0");
    $conn->exec("ALTER TABLE users ADD COLUMN IF NOT EXISTS lock_expires DATETIME NULL");
    $conn->exec("ALTER TABLE users ADD COLUMN IF NOT EXISTS last_login DATETIME NULL");

    $conn->exec("CREATE TABLE IF NOT EXISTS login_attempts (
        id INT AUTO_INCREMENT PRIMARY KEY,
        email VARCHAR(255) NOT NULL,
        ip_address VARCHAR(45) NOT NULL,
        success BOOLEAN DEFAULT FALSE,
        attempt_time DATETIME NOT NULL,
        INDEX (email, attempt_time)
    )");

    $conn->exec("CREATE TABLE IF NOT EXISTS activity_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        action VARCHAR(255) NOT NULL,
        details TEXT,
        ip_address VARCHAR(45),
        user_agent TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id)
    )");

    $conn->exec("CREATE TABLE IF NOT EXISTS audit_trail (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT,
        action VARCHAR(255) NOT NULL,
        table_name VARCHAR(255),
        record_id INT,
        old_values TEXT,
        new_values TEXT,
        ip_address VARCHAR(45),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id)
    )");

    // Add security columns to users table
    $conn->exec("ALTER TABLE users 
        ADD COLUMN IF NOT EXISTS reset_token VARCHAR(64) NULL,
        ADD COLUMN IF NOT EXISTS reset_expires DATETIME NULL,
        ADD COLUMN IF NOT EXISTS google_id VARCHAR(255) NULL,
        ADD COLUMN IF NOT EXISTS last_login DATETIME NULL,
        ADD COLUMN IF NOT EXISTS last_password_change DATETIME NULL,
        ADD COLUMN IF NOT EXISTS failed_attempts INT DEFAULT 0,
        ADD COLUMN IF NOT EXISTS account_locked BOOLEAN DEFAULT FALSE,
        ADD COLUMN IF NOT EXISTS lock_expires DATETIME NULL,
        ADD COLUMN IF NOT EXISTS two_factor_enabled BOOLEAN DEFAULT FALSE,
        ADD COLUMN IF NOT EXISTS two_factor_secret VARCHAR(32) NULL
    ");

    echo "✅ تم تحديث قاعدة البيانات وإضافة جداول الأمان بنجاح";
    
} catch(PDOException $e) {
    die("خطأ: " . $e->getMessage());
}
?>
