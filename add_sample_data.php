<?php
/**
 * إضافة بيانات تجريبية شاملة للنظام
 * Add Comprehensive Sample Data
 */

// تفعيل عرض الأخطاء
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// تضمين ملف الاتصال بقاعدة البيانات
require_once 'config/database.php';

echo "<!DOCTYPE html>";
echo "<html dir='rtl' lang='ar'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>إضافة البيانات التجريبية</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<style>";
echo "body { font-family: 'Tahoma', sans-serif; background-color: #f8f9fa; }";
echo ".container { max-width: 900px; margin-top: 2rem; }";
echo ".step { margin-bottom: 1rem; padding: 1rem; border-radius: 8px; }";
echo ".step.success { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; }";
echo ".step.error { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }";
echo ".step.info { background-color: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }";
echo "</style>";
echo "</head>";
echo "<body>";
echo "<div class='container'>";
echo "<h1 class='text-center mb-4'>📊 إضافة البيانات التجريبية</h1>";

try {
    // 1. إضافة المزيد من المستخدمين
    echo "<div class='step info'>";
    echo "<h5>👥 إضافة مستخدمين إضافيين</h5>";
    echo "</div>";
    
    $password = password_hash('password', PASSWORD_DEFAULT);
    
    $additionalUsers = [
        ['د. محمد أحمد', '<EMAIL>', 'instructor2', $password, 'instructor'],
        ['أ. فاطمة علي', '<EMAIL>', 'instructor3', $password, 'instructor'],
        ['علي محمد', '<EMAIL>', 'student2', $password, 'student'],
        ['نور أحمد', '<EMAIL>', 'student3', $password, 'student'],
        ['خالد سعد', '<EMAIL>', 'student4', $password, 'student'],
        ['مريم حسن', '<EMAIL>', 'student5', $password, 'student']
    ];
    
    $insertUser = $conn->prepare("INSERT IGNORE INTO users (name, email, username, password, role, status, phone) VALUES (?, ?, ?, ?, ?, 'active', ?)");
    
    foreach ($additionalUsers as $index => $user) {
        $phone = '+96650' . (1234567 + $index);
        $insertUser->execute(array_merge($user, [$phone]));
        echo "<div class='step success'>";
        echo "<small>✅ تم إضافة المستخدم: {$user[0]}</small>";
        echo "</div>";
    }
    
    // 2. إضافة كورسات متنوعة
    echo "<div class='step info'>";
    echo "<h5>📚 إضافة كورسات متنوعة</h5>";
    echo "</div>";
    
    // الحصول على معرفات المدربين
    $stmt = $conn->query("SELECT id FROM users WHERE role = 'instructor'");
    $instructors = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    if (!empty($instructors)) {
        $courses = [
            [
                'title' => 'تطوير تطبيقات الويب بـ PHP',
                'slug' => 'php-web-development',
                'description' => 'دورة شاملة لتعلم تطوير تطبيقات الويب باستخدام PHP و MySQL',
                'short_description' => 'تعلم PHP من الصفر حتى الاحتراف',
                'level' => 'intermediate',
                'duration_hours' => 60,
                'max_students' => 25,
                'price' => 299.00
            ],
            [
                'title' => 'أساسيات التصميم الجرافيكي',
                'slug' => 'graphic-design-basics',
                'description' => 'تعلم أساسيات التصميم الجرافيكي باستخدام Adobe Photoshop و Illustrator',
                'short_description' => 'ابدأ رحلتك في عالم التصميم',
                'level' => 'beginner',
                'duration_hours' => 40,
                'max_students' => 20,
                'price' => 199.00
            ],
            [
                'title' => 'التسويق الرقمي ووسائل التواصل الاجتماعي',
                'slug' => 'digital-marketing-social-media',
                'description' => 'استراتيجيات التسويق الرقمي الحديثة وإدارة وسائل التواصل الاجتماعي',
                'short_description' => 'احترف التسويق الرقمي',
                'level' => 'intermediate',
                'duration_hours' => 35,
                'max_students' => 30,
                'price' => 249.00
            ],
            [
                'title' => 'إدارة المشاريع الاحترافية',
                'slug' => 'professional-project-management',
                'description' => 'تعلم إدارة المشاريع باستخدام أحدث الأساليب والأدوات',
                'short_description' => 'كن مدير مشاريع محترف',
                'level' => 'advanced',
                'duration_hours' => 50,
                'max_students' => 15,
                'price' => 399.00
            ],
            [
                'title' => 'تعلم اللغة الإنجليزية للأعمال',
                'slug' => 'business-english',
                'description' => 'تطوير مهارات اللغة الإنجليزية في بيئة العمل والأعمال',
                'short_description' => 'إنجليزية الأعمال بطلاقة',
                'level' => 'intermediate',
                'duration_hours' => 45,
                'max_students' => 20,
                'price' => 179.00
            ]
        ];
        
        $insertCourse = $conn->prepare("
            INSERT IGNORE INTO courses (title, slug, description, short_description, instructor_id, level, duration_hours, max_students, price, status, featured, created_at) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'active', ?, NOW())
        ");
        
        foreach ($courses as $index => $course) {
            $instructorId = $instructors[$index % count($instructors)];
            $featured = $index < 2 ? 1 : 0; // أول كورسين مميزين
            
            $insertCourse->execute([
                $course['title'],
                $course['slug'],
                $course['description'],
                $course['short_description'],
                $instructorId,
                $course['level'],
                $course['duration_hours'],
                $course['max_students'],
                $course['price'],
                $featured
            ]);
            
            echo "<div class='step success'>";
            echo "<small>✅ تم إضافة الكورس: {$course['title']}</small>";
            echo "</div>";
        }
    }
    
    // 3. إضافة جلسات تعليمية
    echo "<div class='step info'>";
    echo "<h5>🎥 إضافة جلسات تعليمية</h5>";
    echo "</div>";
    
    $stmt = $conn->query("SELECT id FROM courses LIMIT 3");
    $courseIds = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    if (!empty($courseIds)) {
        $sessions = [
            [
                'title' => 'مقدمة في البرمجة',
                'description' => 'جلسة تعريفية بأساسيات البرمجة والمفاهيم الأساسية',
                'start_time' => date('Y-m-d H:i:s', strtotime('+1 day')),
                'duration_minutes' => 90
            ],
            [
                'title' => 'أساسيات HTML و CSS',
                'description' => 'تعلم بناء صفحات الويب باستخدام HTML و CSS',
                'start_time' => date('Y-m-d H:i:s', strtotime('+2 days')),
                'duration_minutes' => 120
            ],
            [
                'title' => 'JavaScript للمبتدئين',
                'description' => 'مقدمة في لغة JavaScript وتطبيقاتها',
                'start_time' => date('Y-m-d H:i:s', strtotime('+3 days')),
                'duration_minutes' => 100
            ],
            [
                'title' => 'قواعد البيانات MySQL',
                'description' => 'تعلم التعامل مع قواعد البيانات MySQL',
                'start_time' => date('Y-m-d H:i:s', strtotime('+4 days')),
                'duration_minutes' => 110
            ],
            [
                'title' => 'مشروع تطبيقي شامل',
                'description' => 'بناء مشروع تطبيقي متكامل',
                'start_time' => date('Y-m-d H:i:s', strtotime('+5 days')),
                'duration_minutes' => 150
            ]
        ];
        
        $insertSession = $conn->prepare("
            INSERT IGNORE INTO sessions (course_id, title, description, start_time, end_time, duration_minutes, status, zoom_join_url) 
            VALUES (?, ?, ?, ?, ?, ?, 'scheduled', ?)
        ");
        
        foreach ($sessions as $index => $session) {
            $courseId = $courseIds[$index % count($courseIds)];
            $endTime = date('Y-m-d H:i:s', strtotime($session['start_time'] . ' +' . $session['duration_minutes'] . ' minutes'));
            $zoomUrl = 'https://zoom.us/j/123456789' . ($index + 1);
            
            $insertSession->execute([
                $courseId,
                $session['title'],
                $session['description'],
                $session['start_time'],
                $endTime,
                $session['duration_minutes'],
                $zoomUrl
            ]);
            
            echo "<div class='step success'>";
            echo "<small>✅ تم إضافة الجلسة: {$session['title']}</small>";
            echo "</div>";
        }
    }
    
    // 4. إضافة طلبات انضمام
    echo "<div class='step info'>";
    echo "<h5>📝 إضافة طلبات انضمام</h5>";
    echo "</div>";
    
    $stmt = $conn->query("SELECT id FROM users WHERE role = 'instructor'");
    $instructorIds = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $stmt = $conn->query("SELECT id FROM users WHERE role = 'student'");
    $studentIds = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    if (!empty($instructorIds) && !empty($studentIds)) {
        $insertRequest = $conn->prepare("
            INSERT IGNORE INTO instructor_students (instructor_id, student_id, status, request_date) 
            VALUES (?, ?, ?, ?)
        ");
        
        $statuses = ['pending', 'approved', 'pending', 'approved', 'pending'];
        
        for ($i = 0; $i < min(5, count($studentIds)); $i++) {
            $instructorId = $instructorIds[$i % count($instructorIds)];
            $studentId = $studentIds[$i];
            $status = $statuses[$i % count($statuses)];
            $requestDate = date('Y-m-d H:i:s', strtotime('-' . ($i + 1) . ' days'));
            
            $insertRequest->execute([$instructorId, $studentId, $status, $requestDate]);
            
            echo "<div class='step success'>";
            echo "<small>✅ تم إضافة طلب انضمام ($status)</small>";
            echo "</div>";
        }
    }
    
    // 5. إحصائيات نهائية
    echo "<div class='step success'>";
    echo "<h5>📊 إحصائيات النظام النهائية</h5>";
    
    $stats = [
        'users' => $conn->query("SELECT COUNT(*) FROM users")->fetchColumn(),
        'courses' => $conn->query("SELECT COUNT(*) FROM courses")->fetchColumn(),
        'sessions' => $conn->query("SELECT COUNT(*) FROM sessions")->fetchColumn(),
        'requests' => $conn->query("SELECT COUNT(*) FROM instructor_students")->fetchColumn()
    ];
    
    echo "<div class='row'>";
    echo "<div class='col-md-3'><div class='card bg-primary text-white'><div class='card-body text-center'><h3>{$stats['users']}</h3><p>المستخدمين</p></div></div></div>";
    echo "<div class='col-md-3'><div class='card bg-success text-white'><div class='card-body text-center'><h3>{$stats['courses']}</h3><p>الكورسات</p></div></div></div>";
    echo "<div class='col-md-3'><div class='card bg-info text-white'><div class='card-body text-center'><h3>{$stats['sessions']}</h3><p>الجلسات</p></div></div></div>";
    echo "<div class='col-md-3'><div class='card bg-warning text-white'><div class='card-body text-center'><h3>{$stats['requests']}</h3><p>طلبات الانضمام</p></div></div></div>";
    echo "</div>";
    echo "</div>";
    
    echo "<div class='step success'>";
    echo "<h5>🎉 تم إضافة جميع البيانات التجريبية بنجاح!</h5>";
    echo "<div class='mt-3'>";
    echo "<a href='admin/dashboard.php' class='btn btn-primary me-2'>لوحة تحكم المدير</a>";
    echo "<a href='admin/manage_join_requests.php' class='btn btn-success me-2'>طلبات الانضمام</a>";
    echo "<a href='admin/sessions.php' class='btn btn-info me-2'>إدارة الجلسات</a>";
    echo "<a href='login.php' class='btn btn-secondary'>تسجيل الدخول</a>";
    echo "</div>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='step error'>";
    echo "<h5>❌ حدث خطأ</h5>";
    echo "<p><strong>الخطأ:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>الملف:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>السطر:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}

echo "</div>";
echo "<script src='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js'></script>";
echo "</body>";
echo "</html>";
?>
