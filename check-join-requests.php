<?php
require_once 'config/database.php';

$course_id = isset($_GET['course_id']) ? (int)$_GET['course_id'] : 0;
$user_id = isset($_GET['user_id']) ? (int)$_GET['user_id'] : 0;

try {
    // التحقق من وجود جدول join_requests
    $stmt = $conn->query("SHOW TABLES LIKE 'join_requests'");
    if ($stmt->rowCount() == 0) {
        echo "<div class='alert alert-warning'>جدول طلبات الانضمام غير موجود</div>";
        exit;
    }
    
    // جلب طلبات الانضمام للمستخدم والكورس
    if ($course_id && $user_id) {
        $stmt = $conn->prepare("
            SELECT jr.*, c.title as course_title, u.full_name as student_name
            FROM join_requests jr
            INNER JOIN courses c ON jr.course_id = c.id
            INNER JOIN users u ON jr.student_id = u.id
            WHERE jr.course_id = ? AND jr.student_id = ?
            ORDER BY jr.created_at DESC
        ");
        $stmt->execute([$course_id, $user_id]);
    } else if ($course_id) {
        $stmt = $conn->prepare("
            SELECT jr.*, c.title as course_title, u.full_name as student_name
            FROM join_requests jr
            INNER JOIN courses c ON jr.course_id = c.id
            INNER JOIN users u ON jr.student_id = u.id
            WHERE jr.course_id = ?
            ORDER BY jr.created_at DESC
        ");
        $stmt->execute([$course_id]);
    } else {
        $stmt = $conn->query("
            SELECT jr.*, c.title as course_title, u.full_name as student_name
            FROM join_requests jr
            INNER JOIN courses c ON jr.course_id = c.id
            INNER JOIN users u ON jr.student_id = u.id
            ORDER BY jr.created_at DESC
            LIMIT 10
        ");
    }
    
    $requests = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($requests)) {
        echo "<div class='alert alert-info'>لا توجد طلبات انضمام</div>";
    } else {
        echo "<div class='table-responsive'>";
        echo "<table class='table table-striped'>";
        echo "<thead>";
        echo "<tr>";
        echo "<th>ID</th>";
        echo "<th>الطالب</th>";
        echo "<th>الكورس</th>";
        echo "<th>الرسالة</th>";
        echo "<th>الحالة</th>";
        echo "<th>تاريخ الطلب</th>";
        echo "</tr>";
        echo "</thead>";
        echo "<tbody>";
        
        foreach ($requests as $request) {
            $status_class = '';
            $status_text = '';
            
            switch ($request['status']) {
                case 'pending':
                    $status_class = 'warning';
                    $status_text = 'معلق';
                    break;
                case 'approved':
                    $status_class = 'success';
                    $status_text = 'مقبول';
                    break;
                case 'rejected':
                    $status_class = 'danger';
                    $status_text = 'مرفوض';
                    break;
                default:
                    $status_class = 'secondary';
                    $status_text = $request['status'];
            }
            
            echo "<tr>";
            echo "<td>" . $request['id'] . "</td>";
            echo "<td>" . htmlspecialchars($request['student_name']) . "</td>";
            echo "<td>" . htmlspecialchars($request['course_title']) . "</td>";
            echo "<td>" . htmlspecialchars(substr($request['message'] ?? '', 0, 50)) . "...</td>";
            echo "<td><span class='badge bg-$status_class'>$status_text</span></td>";
            echo "<td>" . date('Y-m-d H:i', strtotime($request['created_at'])) . "</td>";
            echo "</tr>";
        }
        
        echo "</tbody>";
        echo "</table>";
        echo "</div>";
    }
    
} catch (PDOException $e) {
    echo "<div class='alert alert-danger'>خطأ في قاعدة البيانات: " . $e->getMessage() . "</div>";
}
?>
