<?php
require_once 'config/database.php';

try {
    // إنشاء جدول تسجيل الطلاب في الكورسات
    $conn->exec("CREATE TABLE IF NOT EXISTS course_students (
        id INT AUTO_INCREMENT PRIMARY KEY,
        course_id INT NOT NULL,
        student_id INT NOT NULL,
        status ENUM('pending', 'active', 'completed', 'inactive') DEFAULT 'active',
        enrolled_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        completed_at TIMESTAMP NULL,
        FOREIGN KEY (course_id) REFERENCES courses(id),
        FOREIGN KEY (student_id) REFERENCES users(id),
        UNIQUE KEY unique_course_student (course_id, student_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");

    echo "تم إنشاء جدول تسجيل الطلاب في الكورسات بنجاح!";
} catch (PDOException $e) {
    echo "خطأ في قاعدة البيانات: " . $e->getMessage();
}
?>
