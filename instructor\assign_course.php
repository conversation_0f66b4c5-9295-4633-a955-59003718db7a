<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// التحقق من تسجيل الدخول كمدرب
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'instructor') {
    header('Location: ../role_login.php');
    exit();
}

$instructor_id = $_SESSION['user_id'];
$student_id = isset($_GET['student_id']) ? (int)$_GET['student_id'] : 0;
$success = '';
$error = '';

// التحقق من وجود الطالب وأنه مرتبط بالمدرب
$stmt = $conn->prepare("
    SELECT u.id, u.name, u.email
    FROM users u
    INNER JOIN instructor_students is2 ON u.id = is2.student_id
    WHERE is2.instructor_id = ? AND u.id = ? AND is2.status = 'active'
");
$stmt->execute([$instructor_id, $student_id]);
$student = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$student) {
    header('Location: manage_students.php');
    exit();
}

// معالجة تخصيص الكورس
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $course_id = (int)$_POST['course_id'];
    
    try {
        // التحقق من أن الكورس ينتمي للمدرب
        $stmt = $conn->prepare("SELECT id FROM courses WHERE id = ? AND instructor_id = ?");
        $stmt->execute([$course_id, $instructor_id]);
        
        if ($stmt->rowCount() > 0) {
            // إضافة الطالب إلى الكورس
            $stmt = $conn->prepare("
                INSERT INTO course_students (course_id, student_id, status, enrolled_at)
                VALUES (?, ?, 'active', NOW())
                ON DUPLICATE KEY UPDATE status = 'active'
            ");
            $stmt->execute([$course_id, $student_id]);
            $success = 'تم تخصيص الكورس للطالب بنجاح';
        } else {
            $error = 'الكورس غير موجود أو غير مصرح لك بتخصيصه';
        }
    } catch (PDOException $e) {
        $error = 'حدث خطأ أثناء تخصيص الكورس';
    }
}

// جلب قائمة الكورسات الخاصة بالمدرب
$stmt = $conn->prepare("
    SELECT c.*, 
           (SELECT COUNT(*) FROM course_students cs WHERE cs.course_id = c.id AND cs.student_id = ? AND cs.status = 'active') as is_enrolled
    FROM courses c
    WHERE c.instructor_id = ? AND c.status = 'active'
");
$stmt->execute([$student_id, $instructor_id]);
$courses = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تخصيص كورس</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h2 class="mb-4">تخصيص كورس للطالب: <?php echo htmlspecialchars($student['name']); ?></h2>
        
        <?php if ($success): ?>
            <div class="alert alert-success"><?php echo $success; ?></div>
        <?php endif; ?>
        
        <?php if ($error): ?>
            <div class="alert alert-danger"><?php echo $error; ?></div>
        <?php endif; ?>

        <div class="row">
            <?php foreach ($courses as $course): ?>
                <div class="col-md-4 mb-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title"><?php echo htmlspecialchars($course['title']); ?></h5>
                            <p class="card-text"><?php echo htmlspecialchars($course['description']); ?></p>
                            <form method="POST">
                                <input type="hidden" name="course_id" value="<?php echo $course['id']; ?>">
                                <?php if ($course['is_enrolled']): ?>
                                    <button type="button" class="btn btn-success" disabled>تم التسجيل</button>
                                <?php else: ?>
                                    <button type="submit" class="btn btn-primary">تخصيص الكورس</button>
                                <?php endif; ?>
                            </form>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>

        <?php if (empty($courses)): ?>
            <div class="alert alert-info">لا توجد كورسات متاحة حالياً</div>
        <?php endif; ?>
        
        <div class="mt-3">
            <a href="manage_students.php" class="btn btn-secondary">العودة لإدارة الطلاب</a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
