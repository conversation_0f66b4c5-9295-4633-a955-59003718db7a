<?php
/**
 * إنشاء أنشطة حية ومحاولات تسجيل دخول
 * Generate Live Activities and Login Attempts
 */

session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

echo "<!DOCTYPE html>";
echo "<html dir='rtl' lang='ar'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>إنشاء أنشطة حية</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "</head>";
echo "<body>";
echo "<div class='container mt-4'>";
echo "<h2>🔥 إنشاء أنشطة حية ومحاولات تسجيل دخول</h2>";

try {
    // إنشاء الجداول إذا لم تكن موجودة
    $conn->exec("
        CREATE TABLE IF NOT EXISTS activity_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT DEFAULT NULL,
            action VARCHAR(255) NOT NULL,
            description TEXT,
            ip_address VARCHAR(45),
            user_agent TEXT,
            additional_data JSON DEFAULT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
            INDEX idx_user_id (user_id),
            INDEX idx_action (action),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    
    $conn->exec("
        CREATE TABLE IF NOT EXISTS login_attempts (
            id INT AUTO_INCREMENT PRIMARY KEY,
            email VARCHAR(255) NOT NULL,
            ip_address VARCHAR(45) NOT NULL,
            user_agent TEXT,
            success BOOLEAN DEFAULT FALSE,
            failure_reason VARCHAR(255) DEFAULT NULL,
            attempt_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            
            INDEX idx_email (email),
            INDEX idx_ip (ip_address),
            INDEX idx_time (attempt_time),
            INDEX idx_success (success)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    
    echo "<div class='alert alert-success'>✅ تم إنشاء الجداول بنجاح</div>";
    
    // الحصول على المستخدمين
    $stmt = $conn->query("SELECT id, name, email, role FROM users");
    $users = $stmt->fetchAll();
    
    if (empty($users)) {
        echo "<div class='alert alert-warning'>⚠️ لا توجد مستخدمين في النظام</div>";
        echo "</div></body></html>";
        exit;
    }
    
    // إضافة أنشطة متنوعة
    $activities = [
        ['action' => 'login', 'description' => 'تسجيل دخول ناجح للنظام'],
        ['action' => 'logout', 'description' => 'تسجيل خروج من النظام'],
        ['action' => 'profile_view', 'description' => 'عرض الملف الشخصي'],
        ['action' => 'profile_update', 'description' => 'تحديث الملف الشخصي'],
        ['action' => 'password_change', 'description' => 'تغيير كلمة المرور'],
        ['action' => 'course_view', 'description' => 'عرض صفحة الكورسات'],
        ['action' => 'session_view', 'description' => 'عرض صفحة الجلسات'],
        ['action' => 'dashboard_view', 'description' => 'عرض لوحة التحكم'],
        ['action' => 'student_management', 'description' => 'إدارة الطلاب'],
        ['action' => 'join_request', 'description' => 'طلب انضمام جديد'],
        ['action' => 'join_request_approved', 'description' => 'الموافقة على طلب انضمام'],
        ['action' => 'join_request_rejected', 'description' => 'رفض طلب انضمام'],
        ['action' => 'course_created', 'description' => 'إنشاء كورس جديد'],
        ['action' => 'session_created', 'description' => 'إنشاء جلسة جديدة'],
        ['action' => 'report_generated', 'description' => 'إنشاء تقرير'],
        ['action' => 'settings_updated', 'description' => 'تحديث الإعدادات'],
        ['action' => 'user_search', 'description' => 'البحث عن مستخدم'],
        ['action' => 'file_upload', 'description' => 'رفع ملف'],
        ['action' => 'email_sent', 'description' => 'إرسال بريد إلكتروني'],
        ['action' => 'notification_sent', 'description' => 'إرسال إشعار']
    ];
    
    $ipAddresses = [
        '*************', '*************', '*************', '*************',
        '*********', '*********', '************', '************'
    ];
    
    $userAgents = [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15'
    ];
    
    // إضافة 100 نشاط عشوائي
    $insertActivity = $conn->prepare("
        INSERT INTO activity_logs (user_id, action, description, ip_address, user_agent, created_at) 
        VALUES (?, ?, ?, ?, ?, ?)
    ");
    
    echo "<h4>📊 إضافة الأنشطة:</h4>";
    echo "<div class='progress mb-3'>";
    echo "<div class='progress-bar' role='progressbar' style='width: 0%'></div>";
    echo "</div>";
    echo "<div id='activity-log'></div>";
    
    for ($i = 0; $i < 100; $i++) {
        $user = $users[array_rand($users)];
        $activity = $activities[array_rand($activities)];
        $ip = $ipAddresses[array_rand($ipAddresses)];
        $userAgent = $userAgents[array_rand($userAgents)];
        
        // توقيت عشوائي في آخر 7 أيام
        $randomTime = date('Y-m-d H:i:s', strtotime('-' . rand(0, 7) . ' days -' . rand(0, 23) . ' hours -' . rand(0, 59) . ' minutes'));
        
        $description = $activity['description'] . ' - ' . $user['name'] . ' (' . $user['role'] . ')';
        
        $insertActivity->execute([
            $user['id'],
            $activity['action'],
            $description,
            $ip,
            $userAgent,
            $randomTime
        ]);
        
        if ($i % 10 == 0) {
            $progress = ($i / 100) * 100;
            echo "<script>";
            echo "document.querySelector('.progress-bar').style.width = '{$progress}%';";
            echo "document.querySelector('.progress-bar').textContent = '{$progress}%';";
            echo "</script>";
            flush();
        }
    }
    
    echo "<div class='alert alert-success'>✅ تم إضافة 100 نشاط بنجاح</div>";
    
    // إضافة محاولات تسجيل دخول
    echo "<h4>🔐 إضافة محاولات تسجيل الدخول:</h4>";
    
    $insertLoginAttempt = $conn->prepare("
        INSERT INTO login_attempts (email, ip_address, user_agent, success, failure_reason, attempt_time) 
        VALUES (?, ?, ?, ?, ?, ?)
    ");
    
    $failureReasons = [
        'كلمة مرور خاطئة',
        'مستخدم غير موجود',
        'حساب غير مفعل',
        'دور خاطئ',
        'حساب محظور'
    ];
    
    // إضافة 50 محاولة تسجيل دخول (30 ناجحة، 20 فاشلة)
    for ($i = 0; $i < 50; $i++) {
        $user = $users[array_rand($users)];
        $ip = $ipAddresses[array_rand($ipAddresses)];
        $userAgent = $userAgents[array_rand($userAgents)];
        $success = $i < 30; // أول 30 ناجحة
        $failureReason = $success ? null : $failureReasons[array_rand($failureReasons)];
        $attemptTime = date('Y-m-d H:i:s', strtotime('-' . rand(0, 7) . ' days -' . rand(0, 23) . ' hours -' . rand(0, 59) . ' minutes'));
        
        $insertLoginAttempt->execute([
            $user['email'],
            $ip,
            $userAgent,
            $success ? 1 : 0,
            $failureReason,
            $attemptTime
        ]);
    }
    
    echo "<div class='alert alert-success'>✅ تم إضافة 50 محاولة تسجيل دخول (30 ناجحة، 20 فاشلة)</div>";
    
    // إحصائيات نهائية
    echo "<h4>📈 الإحصائيات النهائية:</h4>";
    
    $stats = [];
    
    $stmt = $conn->query("SELECT COUNT(*) FROM activity_logs");
    $stats['total_activities'] = $stmt->fetchColumn();
    
    $stmt = $conn->query("SELECT COUNT(*) FROM login_attempts WHERE success = 1");
    $stats['successful_logins'] = $stmt->fetchColumn();
    
    $stmt = $conn->query("SELECT COUNT(*) FROM login_attempts WHERE success = 0");
    $stats['failed_logins'] = $stmt->fetchColumn();
    
    $stmt = $conn->query("SELECT COUNT(DISTINCT user_id) FROM activity_logs WHERE DATE(created_at) = CURDATE()");
    $stats['active_users_today'] = $stmt->fetchColumn();
    
    $stmt = $conn->query("SELECT action, COUNT(*) as count FROM activity_logs GROUP BY action ORDER BY count DESC LIMIT 5");
    $topActions = $stmt->fetchAll();
    
    echo "<div class='row'>";
    echo "<div class='col-md-3'><div class='card bg-primary text-white'><div class='card-body text-center'><h3>{$stats['total_activities']}</h3><p>إجمالي الأنشطة</p></div></div></div>";
    echo "<div class='col-md-3'><div class='card bg-success text-white'><div class='card-body text-center'><h3>{$stats['successful_logins']}</h3><p>تسجيلات دخول ناجحة</p></div></div></div>";
    echo "<div class='col-md-3'><div class='card bg-danger text-white'><div class='card-body text-center'><h3>{$stats['failed_logins']}</h3><p>محاولات دخول فاشلة</p></div></div></div>";
    echo "<div class='col-md-3'><div class='card bg-info text-white'><div class='card-body text-center'><h3>{$stats['active_users_today']}</h3><p>مستخدمين نشطين اليوم</p></div></div></div>";
    echo "</div>";
    
    echo "<h5 class='mt-4'>أكثر الأنشطة:</h5>";
    echo "<div class='table-responsive'>";
    echo "<table class='table table-striped'>";
    echo "<thead><tr><th>النشاط</th><th>العدد</th></tr></thead>";
    echo "<tbody>";
    foreach ($topActions as $action) {
        echo "<tr><td>{$action['action']}</td><td>{$action['count']}</td></tr>";
    }
    echo "</tbody></table>";
    echo "</div>";
    
    // آخر 10 أنشطة
    echo "<h5>آخر الأنشطة:</h5>";
    $stmt = $conn->query("
        SELECT al.*, u.name as user_name 
        FROM activity_logs al 
        LEFT JOIN users u ON al.user_id = u.id 
        ORDER BY al.created_at DESC 
        LIMIT 10
    ");
    $recentActivities = $stmt->fetchAll();
    
    echo "<div class='table-responsive'>";
    echo "<table class='table table-striped'>";
    echo "<thead><tr><th>المستخدم</th><th>النشاط</th><th>الوصف</th><th>التاريخ</th></tr></thead>";
    echo "<tbody>";
    foreach ($recentActivities as $activity) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($activity['user_name'] ?? 'غير محدد') . "</td>";
        echo "<td>" . htmlspecialchars($activity['action']) . "</td>";
        echo "<td>" . htmlspecialchars($activity['description']) . "</td>";
        echo "<td>" . date('Y-m-d H:i', strtotime($activity['created_at'])) . "</td>";
        echo "</tr>";
    }
    echo "</tbody></table>";
    echo "</div>";
    
    echo "<div class='mt-4'>";
    echo "<a href='admin/activity-logs.php' class='btn btn-primary btn-lg'>🔍 عرض سجل الأنشطة الكامل</a> ";
    echo "<a href='admin/dashboard.php' class='btn btn-success btn-lg'>📊 لوحة التحكم</a> ";
    echo "<a href='login.php' class='btn btn-info btn-lg'>🔐 تسجيل الدخول</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>";
    echo "<h5>❌ حدث خطأ</h5>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "</div>";
echo "<script>";
echo "document.querySelector('.progress-bar').style.width = '100%';";
echo "document.querySelector('.progress-bar').textContent = '100%';";
echo "</script>";
echo "</body>";
echo "</html>";
?>
