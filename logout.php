<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';
require_once 'includes/activity_logger.php';

// تسجيل نشاط تسجيل الخروج
if (isset($_SESSION['user_id']) && isset($_SESSION['name'])) {
    $userName = $_SESSION['name'];
    $userRole = $_SESSION['role'] ?? 'غير محدد';

    logUserActivity('logout', "تسجيل خروج - {$userName} ({$userRole})");
}

// مسح جميع بيانات الجلسة
session_destroy();
header('Location: login.php?logout=success');
exit;
?>
