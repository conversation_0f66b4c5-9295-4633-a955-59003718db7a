<?php
/**
 * إصلاح سريع لمشكلة تسجيل الدخول
 * Quick Fix for Login Issues
 */

echo "<!DOCTYPE html>";
echo "<html dir='rtl' lang='ar'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>إصلاح سريع - تسجيل الدخول</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css' rel='stylesheet'>";
echo "</head>";
echo "<body class='bg-light'>";

echo "<div class='container mt-5'>";
echo "<div class='row justify-content-center'>";
echo "<div class='col-md-10'>";

echo "<div class='card shadow'>";
echo "<div class='card-header bg-warning text-dark text-center'>";
echo "<h3><i class='fas fa-tools me-2'></i>إصلاح سريع لمشكلة تسجيل الدخول</h3>";
echo "</div>";
echo "<div class='card-body'>";

try {
    // الاتصال بقاعدة البيانات
    $conn = new PDO("mysql:host=localhost;charset=utf8mb4", "root", "");
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // إنشاء قاعدة البيانات
    $conn->exec("CREATE DATABASE IF NOT EXISTS zoom_learning_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    $conn->exec("USE zoom_learning_system");
    
    echo "<div class='alert alert-success'>";
    echo "<i class='fas fa-check-circle me-2'></i>";
    echo "تم الاتصال بقاعدة البيانات بنجاح";
    echo "</div>";
    
    // إنشاء جدول المستخدمين
    $conn->exec("CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        email VARCHAR(255) NOT NULL UNIQUE,
        username VARCHAR(50) NULL,
        phone VARCHAR(20) NULL,
        password VARCHAR(255) NOT NULL,
        role ENUM('admin', 'instructor', 'student') NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        last_login TIMESTAMP NULL,
        status ENUM('active', 'inactive') DEFAULT 'active'
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    
    // التحقق من وجود المستخدم المطلوب
    $email = '<EMAIL>';
    $stmt = $conn->prepare("SELECT * FROM users WHERE email = ?");
    $stmt->execute([$email]);
    $user = $stmt->fetch();
    
    if (!$user) {
        // إنشاء المستخدم
        $password = password_hash('12345678', PASSWORD_DEFAULT); // كلمة مرور افتراضية
        $stmt = $conn->prepare("INSERT INTO users (name, email, username, password, role, status) VALUES (?, ?, ?, ?, ?, ?)");
        $stmt->execute([
            'مستخدم تجريبي',
            $email,
            'wow11',
            $password,
            'instructor', // جعله مدرب
            'active'
        ]);
        
        echo "<div class='alert alert-success'>";
        echo "<i class='fas fa-user-plus me-2'></i>";
        echo "<strong>تم إنشاء المستخدم بنجاح!</strong><br>";
        echo "البريد الإلكتروني: $email<br>";
        echo "كلمة المرور: 12345678<br>";
        echo "الدور: مدرب";
        echo "</div>";
    } else {
        // تحديث كلمة المرور
        $password = password_hash('12345678', PASSWORD_DEFAULT);
        $stmt = $conn->prepare("UPDATE users SET password = ?, status = 'active' WHERE email = ?");
        $stmt->execute([$password, $email]);
        
        echo "<div class='alert alert-info'>";
        echo "<i class='fas fa-sync me-2'></i>";
        echo "<strong>تم تحديث المستخدم!</strong><br>";
        echo "البريد الإلكتروني: $email<br>";
        echo "كلمة المرور الجديدة: 12345678<br>";
        echo "الدور: {$user['role']}";
        echo "</div>";
    }
    
    // إنشاء جداول أخرى مطلوبة
    echo "<h5><i class='fas fa-database me-2'></i>إنشاء الجداول المطلوبة:</h5>";
    
    // جدول الكورسات
    $conn->exec("CREATE TABLE IF NOT EXISTS courses (
        id INT AUTO_INCREMENT PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        description TEXT,
        instructor_id INT,
        max_students INT DEFAULT 50,
        price DECIMAL(10,2) DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        status ENUM('active', 'inactive') DEFAULT 'active',
        FOREIGN KEY (instructor_id) REFERENCES users(id) ON DELETE SET NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    
    // جدول الواجبات
    $conn->exec("CREATE TABLE IF NOT EXISTS assignments (
        id INT AUTO_INCREMENT PRIMARY KEY,
        course_id INT NOT NULL,
        instructor_id INT NOT NULL,
        title VARCHAR(255) NOT NULL,
        description TEXT,
        instructions TEXT,
        due_date DATETIME NOT NULL,
        max_grade INT DEFAULT 100,
        is_required TINYINT(1) DEFAULT 0,
        status ENUM('active', 'inactive') DEFAULT 'active',
        created_by INT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
        FOREIGN KEY (instructor_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    
    // جدول تسليمات الواجبات
    $conn->exec("CREATE TABLE IF NOT EXISTS assignment_submissions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        assignment_id INT NOT NULL,
        student_id INT NOT NULL,
        submission_text TEXT,
        file_path VARCHAR(500),
        score INT NULL,
        feedback TEXT,
        submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        graded_at TIMESTAMP NULL,
        status ENUM('submitted', 'graded', 'late') DEFAULT 'submitted',
        FOREIGN KEY (assignment_id) REFERENCES assignments(id) ON DELETE CASCADE,
        FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
        UNIQUE KEY unique_submission (assignment_id, student_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    
    // جدول التسجيل في الكورسات
    $conn->exec("CREATE TABLE IF NOT EXISTS course_enrollments (
        id INT AUTO_INCREMENT PRIMARY KEY,
        course_id INT,
        student_id INT,
        enrolled_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        status ENUM('active', 'completed', 'dropped') DEFAULT 'active',
        progress_percentage DECIMAL(5,2) DEFAULT 0,
        FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
        FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    
    // جدول الإشعارات
    $conn->exec("CREATE TABLE IF NOT EXISTS notifications (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        title VARCHAR(255) NOT NULL,
        message TEXT NOT NULL,
        type ENUM('info', 'success', 'warning', 'error') DEFAULT 'info',
        category ENUM('system', 'course', 'payment', 'assignment', 'session') DEFAULT 'system',
        action_url VARCHAR(500),
        is_read BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        read_at TIMESTAMP NULL,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    
    echo "<div class='alert alert-success'>";
    echo "<i class='fas fa-check-circle me-2'></i>";
    echo "تم إنشاء جميع الجداول المطلوبة بنجاح";
    echo "</div>";
    
    // إنشاء كورس تجريبي للمدرب
    $stmt = $conn->prepare("SELECT id FROM users WHERE email = ?");
    $stmt->execute([$email]);
    $instructor = $stmt->fetch();
    
    if ($instructor) {
        $instructor_id = $instructor['id'];
        
        // التحقق من وجود كورسات
        $stmt = $conn->prepare("SELECT COUNT(*) FROM courses WHERE instructor_id = ?");
        $stmt->execute([$instructor_id]);
        $course_count = $stmt->fetchColumn();
        
        if ($course_count == 0) {
            // إنشاء كورس تجريبي
            $stmt = $conn->prepare("INSERT INTO courses (title, description, instructor_id, status) VALUES (?, ?, ?, ?)");
            $stmt->execute([
                'كورس البرمجة الأساسية',
                'تعلم أساسيات البرمجة من الصفر',
                $instructor_id,
                'active'
            ]);
            
            echo "<div class='alert alert-success'>";
            echo "<i class='fas fa-book me-2'></i>";
            echo "تم إنشاء كورس تجريبي للمدرب";
            echo "</div>";
        }
    }
    
    echo "<div class='alert alert-success mt-4'>";
    echo "<h5><i class='fas fa-check-circle me-2'></i>تم الإصلاح بنجاح!</h5>";
    echo "<p>يمكنك الآن تسجيل الدخول باستخدام:</p>";
    echo "<ul>";
    echo "<li><strong>البريد الإلكتروني:</strong> $email</li>";
    echo "<li><strong>كلمة المرور:</strong> 12345678</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div class='text-center mt-4'>";
    echo "<a href='login.php' class='btn btn-primary btn-lg me-2'>";
    echo "<i class='fas fa-sign-in-alt me-2'></i>تسجيل الدخول الآن";
    echo "</a>";
    echo "<a href='instructor/assignments.php' class='btn btn-success btn-lg'>";
    echo "<i class='fas fa-tasks me-2'></i>إدارة الواجبات";
    echo "</a>";
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<div class='alert alert-danger'>";
    echo "<h5><i class='fas fa-exclamation-triangle me-2'></i>خطأ!</h5>";
    echo "<p><strong>رسالة الخطأ:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>الحل:</strong> تأكد من تشغيل XAMPP وأن خدمة MySQL تعمل</p>";
    echo "</div>";
}

echo "</div>";
echo "</div>";
echo "</div>";
echo "</div>";
echo "</div>";

echo "</body>";
echo "</html>";
?>
