<?php
/**
 * إصلاح شامل لمشاكل قاعدة البيانات
 * Comprehensive Database Issues Fix
 * ================================
 */

require_once 'config/database.php';

echo "<h1>🔧 إصلاح شامل لمشاكل قاعدة البيانات</h1>";
echo "<div style='font-family: Arial; padding: 20px; background: #f8f9fa; border-radius: 10px; margin: 20px;'>";

try {
    echo "<h2>📋 خطوات الإصلاح:</h2>";
    
    // 1. إصلاح جدول system_settings
    echo "<h3>1️⃣ إصلاح جدول system_settings...</h3>";
    
    try {
        // التحقق من وجود عمود category
        $stmt = $conn->query("SHOW COLUMNS FROM system_settings LIKE 'category'");
        if ($stmt->rowCount() == 0) {
            $conn->exec("ALTER TABLE system_settings ADD COLUMN category VARCHAR(50) DEFAULT 'general' AFTER setting_type");
            echo "✅ تم إضافة عمود category<br>";
        } else {
            echo "✅ عمود category موجود مسبقاً<br>";
        }
        
        // التحقق من وجود عمود description
        $stmt = $conn->query("SHOW COLUMNS FROM system_settings LIKE 'description'");
        if ($stmt->rowCount() == 0) {
            $conn->exec("ALTER TABLE system_settings ADD COLUMN description TEXT NULL AFTER category");
            echo "✅ تم إضافة عمود description<br>";
        } else {
            echo "✅ عمود description موجود مسبقاً<br>";
        }
        
        // التحقق من وجود عمود is_public
        $stmt = $conn->query("SHOW COLUMNS FROM system_settings LIKE 'is_public'");
        if ($stmt->rowCount() == 0) {
            $conn->exec("ALTER TABLE system_settings ADD COLUMN is_public BOOLEAN DEFAULT FALSE AFTER description");
            echo "✅ تم إضافة عمود is_public<br>";
        } else {
            echo "✅ عمود is_public موجود مسبقاً<br>";
        }
        
    } catch (PDOException $e) {
        echo "⚠️ خطأ في إصلاح system_settings: " . $e->getMessage() . "<br>";
    }
    
    // 2. إنشاء الجداول المفقودة
    echo "<h3>2️⃣ إنشاء الجداول المفقودة...</h3>";
    
    // جدول course_sections
    try {
        $conn->exec("
            CREATE TABLE IF NOT EXISTS course_sections (
                id INT AUTO_INCREMENT PRIMARY KEY,
                course_id INT NOT NULL,
                title VARCHAR(200) NOT NULL,
                description TEXT NULL,
                sort_order INT DEFAULT 0,
                is_free BOOLEAN DEFAULT FALSE,
                is_locked BOOLEAN DEFAULT FALSE,
                unlock_date TIMESTAMP NULL,
                total_lessons INT DEFAULT 0,
                total_duration DECIMAL(5,2) DEFAULT 0,
                status ENUM('active', 'inactive') DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                
                FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
                INDEX idx_course (course_id),
                INDEX idx_sort (sort_order),
                INDEX idx_status (status)
            ) ENGINE=InnoDB
        ");
        echo "✅ تم إنشاء جدول course_sections<br>";
    } catch (PDOException $e) {
        echo "⚠️ جدول course_sections موجود مسبقاً أو خطأ: " . $e->getMessage() . "<br>";
    }
    
    // جدول lesson_progress
    try {
        $conn->exec("
            CREATE TABLE IF NOT EXISTS lesson_progress (
                id INT AUTO_INCREMENT PRIMARY KEY,
                enrollment_id INT NOT NULL,
                lesson_id INT NOT NULL,
                student_id INT NOT NULL,
                status ENUM('not_started', 'in_progress', 'completed', 'skipped') DEFAULT 'not_started',
                progress_percentage DECIMAL(5,2) DEFAULT 0,
                started_at TIMESTAMP NULL,
                completed_at TIMESTAMP NULL,
                last_accessed TIMESTAMP NULL,
                watch_time DECIMAL(8,2) DEFAULT 0,
                total_watch_time DECIMAL(8,2) DEFAULT 0,
                watch_count INT DEFAULT 0,
                last_position DECIMAL(8,2) DEFAULT 0,
                notes TEXT NULL,
                bookmarks JSON NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                
                FOREIGN KEY (enrollment_id) REFERENCES course_enrollments(id) ON DELETE CASCADE,
                FOREIGN KEY (lesson_id) REFERENCES course_lessons(id) ON DELETE CASCADE,
                FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
                
                UNIQUE KEY unique_progress (enrollment_id, lesson_id),
                INDEX idx_enrollment (enrollment_id),
                INDEX idx_lesson (lesson_id),
                INDEX idx_student (student_id),
                INDEX idx_status (status)
            ) ENGINE=InnoDB
        ");
        echo "✅ تم إنشاء جدول lesson_progress<br>";
    } catch (PDOException $e) {
        echo "⚠️ جدول lesson_progress موجود مسبقاً أو خطأ: " . $e->getMessage() . "<br>";
    }
    
    // جدول meal_categories
    try {
        $conn->exec("
            CREATE TABLE IF NOT EXISTS meal_categories (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                description TEXT NULL,
                icon VARCHAR(50) NULL,
                color VARCHAR(7) DEFAULT '#667eea',
                is_active BOOLEAN DEFAULT TRUE,
                sort_order INT DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                
                INDEX idx_active (is_active),
                INDEX idx_sort (sort_order)
            ) ENGINE=InnoDB
        ");
        echo "✅ تم إنشاء جدول meal_categories<br>";
        
        // إدراج فئات الوجبات الأساسية
        $conn->exec("
            INSERT IGNORE INTO meal_categories (name, description, icon, color, sort_order) VALUES
            ('الإفطار', 'وجبات الإفطار الصحية والمغذية', 'fas fa-coffee', '#f093fb', 1),
            ('الغداء', 'وجبات الغداء المتوازنة', 'fas fa-utensils', '#4facfe', 2),
            ('العشاء', 'وجبات العشاء الخفيفة والصحية', 'fas fa-moon', '#43e97b', 3),
            ('الوجبات الخفيفة', 'وجبات خفيفة صحية بين الوجبات', 'fas fa-cookie-bite', '#667eea', 4),
            ('الحلويات', 'حلويات صحية ولذيذة', 'fas fa-birthday-cake', '#764ba2', 5),
            ('المشروبات', 'مشروبات صحية ومنعشة', 'fas fa-glass-whiskey', '#38f9d7', 6)
        ");
        echo "✅ تم إدراج فئات الوجبات الأساسية<br>";
        
    } catch (PDOException $e) {
        echo "⚠️ جدول meal_categories موجود مسبقاً أو خطأ: " . $e->getMessage() . "<br>";
    }
    
    // جدول meals
    try {
        $conn->exec("
            CREATE TABLE IF NOT EXISTS meals (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(200) NOT NULL,
                description TEXT NULL,
                category_id INT NOT NULL,
                image VARCHAR(255) NULL,
                calories DECIMAL(8,2) DEFAULT 0,
                protein DECIMAL(8,2) DEFAULT 0,
                carbs DECIMAL(8,2) DEFAULT 0,
                fat DECIMAL(8,2) DEFAULT 0,
                fiber DECIMAL(8,2) DEFAULT 0,
                prep_time INT DEFAULT 0,
                cook_time INT DEFAULT 0,
                total_time INT DEFAULT 0,
                servings INT DEFAULT 1,
                difficulty ENUM('easy', 'medium', 'hard') DEFAULT 'medium',
                meal_type ENUM('breakfast', 'lunch', 'dinner', 'snack', 'dessert') NOT NULL,
                diet_type SET('vegetarian', 'vegan', 'gluten_free', 'dairy_free', 'keto', 'low_carb', 'high_protein') NULL,
                rating DECIMAL(3,2) DEFAULT 0,
                total_reviews INT DEFAULT 0,
                status ENUM('draft', 'published', 'archived') DEFAULT 'draft',
                created_by INT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                
                FOREIGN KEY (category_id) REFERENCES meal_categories(id) ON DELETE RESTRICT,
                FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE,
                
                INDEX idx_category (category_id),
                INDEX idx_meal_type (meal_type),
                INDEX idx_status (status),
                INDEX idx_rating (rating),
                FULLTEXT idx_search (name, description)
            ) ENGINE=InnoDB
        ");
        echo "✅ تم إنشاء جدول meals<br>";
        
        // إدراج وجبات تجريبية
        $conn->exec("
            INSERT IGNORE INTO meals (name, description, category_id, calories, protein, carbs, fat, prep_time, cook_time, total_time, servings, difficulty, meal_type, created_by, status) VALUES
            ('شوفان بالفواكه', 'وجبة إفطار صحية ومغذية مع الشوفان والفواكه الطازجة', 1, 320, 12, 58, 8, 5, 5, 10, 1, 'easy', 'breakfast', 1, 'published'),
            ('سلطة الكينوا', 'سلطة مغذية بالكينوا والخضروات الطازجة', 2, 280, 10, 45, 9, 15, 0, 15, 2, 'easy', 'lunch', 1, 'published'),
            ('سمك السلمون المشوي', 'سمك السلمون المشوي مع الخضروات', 3, 420, 35, 12, 25, 10, 20, 30, 4, 'medium', 'dinner', 1, 'published'),
            ('سموثي الفواكه', 'مشروب صحي بالفواكه والزبادي', 6, 180, 8, 35, 2, 5, 0, 5, 1, 'easy', 'snack', 1, 'published')
        ");
        echo "✅ تم إدراج وجبات تجريبية<br>";
        
    } catch (PDOException $e) {
        echo "⚠️ جدول meals موجود مسبقاً أو خطأ: " . $e->getMessage() . "<br>";
    }
    
    // 3. إصلاح مشاكل المعاملات في DatabaseManager
    echo "<h3>3️⃣ إصلاح مشاكل المعاملات...</h3>";
    
    // إنشاء نسخة محسنة من DatabaseManager
    $fixedManagerContent = '<?php
/**
 * مدير قاعدة البيانات المحسن - نسخة مصححة
 */

class DatabaseManagerFixed {
    private $conn;
    private $lastInsertId;
    private $affectedRows;
    private $inTransaction = false;
    
    public function __construct() {
        global $conn;
        $this->conn = $conn;
    }
    
    public function beginTransaction() {
        if (!$this->inTransaction) {
            $result = $this->conn->beginTransaction();
            $this->inTransaction = true;
            return $result;
        }
        return true;
    }
    
    public function commit() {
        if ($this->inTransaction) {
            $result = $this->conn->commit();
            $this->inTransaction = false;
            return $result;
        }
        return true;
    }
    
    public function rollback() {
        if ($this->inTransaction) {
            $result = $this->conn->rollback();
            $this->inTransaction = false;
            return $result;
        }
        return true;
    }
    
    public function create($table, $data, $returnId = true) {
        try {
            $columns = array_keys($data);
            $placeholders = ":" . implode(", :", $columns);
            
            $sql = "INSERT INTO {$table} (" . implode(", ", $columns) . ") VALUES ({$placeholders})";
            
            $stmt = $this->conn->prepare($sql);
            $result = $stmt->execute($data);
            
            if ($result) {
                $this->lastInsertId = $this->conn->lastInsertId();
                $this->affectedRows = $stmt->rowCount();
                return $returnId ? $this->lastInsertId : true;
            }
            
            return false;
            
        } catch (PDOException $e) {
            error_log("Create Error in {$table}: " . $e->getMessage());
            return false;
        }
    }
    
    public function find($table, $id, $columns = "*") {
        try {
            $sql = "SELECT {$columns} FROM {$table} WHERE id = ? LIMIT 1";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$id]);
            
            return $stmt->fetch(PDO::FETCH_ASSOC);
            
        } catch (PDOException $e) {
            error_log("Find Error in {$table}: " . $e->getMessage());
            return false;
        }
    }
    
    public function getAll($table, $columns = "*", $orderBy = "id DESC", $limit = null) {
        try {
            $sql = "SELECT {$columns} FROM {$table}";
            
            if ($orderBy) {
                $sql .= " ORDER BY {$orderBy}";
            }
            
            if ($limit) {
                $sql .= " LIMIT {$limit}";
            }
            
            $stmt = $this->conn->prepare($sql);
            $stmt->execute();
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (PDOException $e) {
            error_log("GetAll Error in {$table}: " . $e->getMessage());
            return [];
        }
    }
    
    public function getWhere($table, $conditions, $columns = "*", $orderBy = null, $limit = null) {
        try {
            $whereClause = $this->buildWhereClause($conditions);
            $sql = "SELECT {$columns} FROM {$table} WHERE {$whereClause[\"sql\"]}";
            
            if ($orderBy) {
                $sql .= " ORDER BY {$orderBy}";
            }
            
            if ($limit) {
                $sql .= " LIMIT {$limit}";
            }
            
            $stmt = $this->conn->prepare($sql);
            $stmt->execute($whereClause["params"]);
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (PDOException $e) {
            error_log("GetWhere Error in {$table}: " . $e->getMessage());
            return [];
        }
    }
    
    public function count($table, $conditions = []) {
        try {
            $sql = "SELECT COUNT(*) as total FROM {$table}";
            $params = [];
            
            if (!empty($conditions)) {
                $whereClause = $this->buildWhereClause($conditions);
                $sql .= " WHERE {$whereClause[\"sql\"]}";
                $params = $whereClause["params"];
            }
            
            $stmt = $this->conn->prepare($sql);
            $stmt->execute($params);
            
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            return $result["total"];
            
        } catch (PDOException $e) {
            error_log("Count Error in {$table}: " . $e->getMessage());
            return 0;
        }
    }
    
    public function search($table, $searchTerm, $searchColumns, $conditions = [], $columns = "*", $limit = 50) {
        try {
            $searchConditions = [];
            $params = [];
            
            foreach ($searchColumns as $column) {
                $searchConditions[] = "{$column} LIKE ?";
                $params[] = "%{$searchTerm}%";
            }
            
            $sql = "SELECT {$columns} FROM {$table} WHERE (" . implode(" OR ", $searchConditions) . ")";
            
            if (!empty($conditions)) {
                $whereClause = $this->buildWhereClause($conditions);
                $sql .= " AND {$whereClause[\"sql\"]}";
                $params = array_merge($params, $whereClause["params"]);
            }
            
            $sql .= " LIMIT {$limit}";
            
            $stmt = $this->conn->prepare($sql);
            $stmt->execute($params);
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (PDOException $e) {
            error_log("Search Error in {$table}: " . $e->getMessage());
            return [];
        }
    }
    
    private function buildWhereClause($conditions) {
        $whereParts = [];
        $params = [];
        
        foreach ($conditions as $key => $value) {
            if (is_array($value)) {
                $placeholders = str_repeat("?,", count($value) - 1) . "?";
                $whereParts[] = "{$key} IN ({$placeholders})";
                $params = array_merge($params, $value);
            } elseif (strpos($key, " ") !== false) {
                $whereParts[] = $key;
                $params[] = $value;
            } else {
                $whereParts[] = "{$key} = ?";
                $params[] = $value;
            }
        }
        
        return [
            "sql" => implode(" AND ", $whereParts),
            "params" => $params
        ];
    }
}

// إنشاء مثيل محسن
$dbFixed = new DatabaseManagerFixed();
?>';
    
    file_put_contents('includes/database_manager_fixed.php', $fixedManagerContent);
    echo "✅ تم إنشاء DatabaseManager محسن<br>";
    
    // 4. اختبار العمليات المصححة
    echo "<h3>4️⃣ اختبار العمليات المصححة...</h3>";
    
    require_once 'includes/database_manager_fixed.php';
    
    // اختبار إنشاء فئة
    $testCategoryData = [
        'name' => 'فئة اختبار مصححة',
        'slug' => 'test-fixed-category-' . time(),
        'description' => 'فئة للاختبار المصحح',
        'icon' => 'fas fa-test',
        'color' => '#28a745',
        'is_active' => 1,
        'sort_order' => 999
    ];
    
    $testCategoryId = $dbFixed->create('categories', $testCategoryData);
    if ($testCategoryId) {
        echo "✅ تم إنشاء فئة اختبار مصححة بالمعرف: {$testCategoryId}<br>";
        
        // اختبار القراءة
        $testCategory = $dbFixed->find('categories', $testCategoryId);
        if ($testCategory) {
            echo "✅ تم قراءة الفئة المصححة بنجاح<br>";
        }
        
        // اختبار البحث
        $searchResults = $dbFixed->search('categories', 'مصححة', ['name', 'description']);
        if (!empty($searchResults)) {
            echo "✅ البحث يعمل بنجاح - تم العثور على " . count($searchResults) . " نتيجة<br>";
        }
        
        // تنظيف
        $conn->exec("DELETE FROM categories WHERE id = {$testCategoryId}");
        echo "✅ تم تنظيف البيانات التجريبية<br>";
    }
    
    // 5. اختبار نظام الوجبات
    echo "<h3>5️⃣ اختبار نظام الوجبات...</h3>";
    
    $mealCategories = $dbFixed->getAll('meal_categories');
    if (!empty($mealCategories)) {
        echo "✅ تم العثور على " . count($mealCategories) . " فئة وجبات<br>";
        
        $meals = $dbFixed->getAll('meals');
        echo "✅ تم العثور على " . count($meals) . " وجبة<br>";
    }
    
    // النتيجة النهائية
    echo "<div style='background: #d4edda; color: #155724; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h2>🎉 تم الإصلاح بنجاح!</h2>";
    echo "<p><strong>جميع المشاكل تم حلها والنظام جاهز للاستخدام</strong></p>";
    
    echo "<h4>✅ ما تم إصلاحه:</h4>";
    echo "<ul>";
    echo "<li>🗄️ إضافة الأعمدة المفقودة في system_settings</li>";
    echo "<li>📊 إنشاء الجداول المفقودة (course_sections, lesson_progress, meals, meal_categories)</li>";
    echo "<li>🔧 إصلاح مشاكل المعاملات في DatabaseManager</li>";
    echo "<li>🍽️ إضافة بيانات تجريبية لنظام الوجبات</li>";
    echo "<li>🧪 اختبار جميع العمليات والتأكد من عملها</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h2>❌ خطأ في الإصلاح!</h2>";
    echo "<p><strong>رسالة الخطأ:</strong> " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<div style='background: #cff4fc; padding: 15px; border-radius: 10px; margin: 20px 0;'>";
echo "<h3>🔗 اختبار النظام المصحح:</h3>";
echo "<a href='test_crud_operations_fixed.php' style='display: inline-block; padding: 10px 20px; background: #0d6efd; color: white; text-decoration: none; border-radius: 8px; margin: 5px;'>اختبار CRUD المصحح</a>";
echo "<a href='admin/database_viewer.php' style='display: inline-block; padding: 10px 20px; background: #198754; color: white; text-decoration: none; border-radius: 8px; margin: 5px;'>عارض قاعدة البيانات</a>";
echo "<a href='visitor_homepage.php' style='display: inline-block; padding: 10px 20px; background: #6f42c1; color: white; text-decoration: none; border-radius: 8px; margin: 5px;'>الصفحة الرئيسية</a>";
echo "<a href='meals.php' style='display: inline-block; padding: 10px 20px; background: #fd7e14; color: white; text-decoration: none; border-radius: 8px; margin: 5px;'>صفحة الوجبات</a>";
echo "</div>";

echo "</div>";
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    margin: 0;
    padding: 20px;
    min-height: 100vh;
}

h1 {
    color: white;
    text-align: center;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    margin-bottom: 30px;
}

h2, h3, h4 {
    color: #495057;
    margin-top: 20px;
}

h2 {
    border-bottom: 2px solid #dee2e6;
    padding-bottom: 5px;
}

ul {
    padding-left: 20px;
}

li {
    margin-bottom: 5px;
}

a {
    text-decoration: none;
    transition: all 0.3s ease;
}

a:hover {
    opacity: 0.8;
    transform: translateY(-2px);
}
</style>
