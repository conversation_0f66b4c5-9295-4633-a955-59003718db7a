<?php
require_once 'config/database.php';
require_once 'includes/functions.php';

// معالجة الفلاتر والبحث
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$category = isset($_GET['category']) ? trim($_GET['category']) : '';
$type = isset($_GET['type']) ? trim($_GET['type']) : '';
$sort = isset($_GET['sort']) ? trim($_GET['sort']) : 'newest';

// بناء الاستعلام
$where_conditions = ["c.status = 'active'"];
$params = [];

if (!empty($search)) {
    $where_conditions[] = "(c.title LIKE ? OR c.description LIKE ? OR u.name LIKE ?)";
    $search_param = "%$search%";
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
}

if (!empty($category)) {
    $where_conditions[] = "c.category = ?";
    $params[] = $category;
}

if (!empty($type)) {
    $where_conditions[] = "c.course_type = ?";
    $params[] = $type;
}

$where_clause = implode(' AND ', $where_conditions);

// ترتيب النتائج
$order_clause = "ORDER BY ";
switch ($sort) {
    case 'oldest':
        $order_clause .= "c.created_at ASC";
        break;
    case 'price_low':
        $order_clause .= "c.price ASC";
        break;
    case 'price_high':
        $order_clause .= "c.price DESC";
        break;
    case 'popular':
        $order_clause .= "enrolled_students DESC";
        break;
    case 'rating':
        $order_clause .= "avg_rating DESC";
        break;
    default: // newest
        $order_clause .= "c.created_at DESC";
        break;
}

try {
    // جلب الكورسات
    $stmt = $conn->prepare("
        SELECT c.*, u.name as instructor_name, u.email as instructor_email,
               (SELECT COUNT(*) FROM course_enrollments WHERE course_id = c.id AND status = 'active') as enrolled_students,
               (SELECT COUNT(*) FROM sessions WHERE course_id = c.id) as total_sessions,
               (SELECT AVG(rating) FROM course_reviews WHERE course_id = c.id) as avg_rating,
               (SELECT COUNT(*) FROM course_reviews WHERE course_id = c.id) as total_reviews
        FROM courses c
        JOIN users u ON c.instructor_id = u.id
        WHERE $where_clause
        $order_clause
    ");
    $stmt->execute($params);
    $courses = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // جلب التخصصات المتاحة
    $stmt = $conn->query("SELECT DISTINCT category FROM courses WHERE status = 'active' AND category IS NOT NULL ORDER BY category");
    $categories = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    // إحصائيات البحث
    $total_courses = count($courses);
    $free_courses = count(array_filter($courses, function($c) { return $c['course_type'] === 'free'; }));
    $paid_courses = count(array_filter($courses, function($c) { return $c['course_type'] === 'paid'; }));
    
} catch (PDOException $e) {
    $courses = [];
    $categories = [];
    $total_courses = $free_courses = $paid_courses = 0;
}

$pageTitle = 'جميع الكورسات';
?>

<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - منصة التعلم</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }
        
        .page-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 60px 0;
        }
        
        .search-filters {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            padding: 20px;
            margin-top: -30px;
            position: relative;
            z-index: 10;
        }
        
        .course-card {
            transition: all 0.3s ease;
            border: none;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            height: 100%;
        }
        
        .course-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }
        
        .course-image {
            height: 200px;
            background: linear-gradient(45deg, #f093fb 0%, #f5576c 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 3rem;
        }
        
        .price-badge {
            position: absolute;
            top: 15px;
            right: 15px;
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: bold;
        }
        
        .price-badge.paid {
            background: linear-gradient(45deg, #ffc107, #fd7e14);
        }
        
        .rating-stars {
            color: #ffc107;
        }
        
        .filter-btn {
            border-radius: 25px;
            padding: 8px 20px;
            margin: 5px;
            border: 2px solid #667eea;
            background: transparent;
            color: #667eea;
            transition: all 0.3s ease;
        }
        
        .filter-btn:hover,
        .filter-btn.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }
        
        .stats-card {
            background: white;
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .navbar-brand {
            font-weight: bold;
            font-size: 1.5rem;
        }
        
        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            font-weight: 600;
        }
        
        .btn-outline-primary {
            border: 2px solid #667eea;
            color: #667eea;
            border-radius: 25px;
            padding: 12px 30px;
            font-weight: 600;
        }
        
        .btn-outline-primary:hover {
            background: #667eea;
            border-color: #667eea;
        }
        
        .no-courses {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }
        
        .pagination {
            justify-content: center;
        }
        
        .page-link {
            border-radius: 50%;
            margin: 0 5px;
            border: none;
            color: #667eea;
        }
        
        .page-link:hover {
            background: #667eea;
            color: white;
        }
        
        .page-item.active .page-link {
            background: #667eea;
            border-color: #667eea;
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm fixed-top">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-graduation-cap text-primary me-2"></i>
                منصة التعلم
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">الرئيسية</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="courses.php">الكورسات</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="index.php#services">الخدمات</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="index.php#about">من نحن</a>
                    </li>
                </ul>
                
                <div class="d-flex">
                    <a href="login.php" class="btn btn-outline-primary me-2">
                        <i class="fas fa-sign-in-alt me-1"></i>
                        تسجيل الدخول
                    </a>
                    <a href="register.php" class="btn btn-primary">
                        <i class="fas fa-user-plus me-1"></i>
                        إنشاء حساب
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- رأس الصفحة -->
    <section class="page-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="display-5 fw-bold mb-3">جميع الكورسات</h1>
                    <p class="lead mb-0">اكتشف مجموعة واسعة من الكورسات في مختلف المجالات</p>
                </div>
                <div class="col-lg-4 text-center">
                    <i class="fas fa-book-open" style="font-size: 8rem; opacity: 0.3;"></i>
                </div>
            </div>
        </div>
    </section>

    <!-- فلاتر البحث -->
    <section class="py-4">
        <div class="container">
            <div class="search-filters">
                <form method="GET" action="courses.php">
                    <div class="row align-items-end">
                        <div class="col-lg-4 mb-3">
                            <label class="form-label">البحث في الكورسات</label>
                            <div class="input-group">
                                <input type="text" class="form-control" name="search" 
                                       value="<?php echo htmlspecialchars($search); ?>" 
                                       placeholder="ابحث عن كورس، مدرب، أو موضوع...">
                                <button class="btn btn-primary" type="submit">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                        
                        <div class="col-lg-3 mb-3">
                            <label class="form-label">التخصص</label>
                            <select class="form-select" name="category">
                                <option value="">جميع التخصصات</option>
                                <?php foreach ($categories as $cat): ?>
                                    <option value="<?php echo htmlspecialchars($cat); ?>" 
                                            <?php echo $category === $cat ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($cat); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="col-lg-2 mb-3">
                            <label class="form-label">النوع</label>
                            <select class="form-select" name="type">
                                <option value="">الكل</option>
                                <option value="free" <?php echo $type === 'free' ? 'selected' : ''; ?>>مجاني</option>
                                <option value="paid" <?php echo $type === 'paid' ? 'selected' : ''; ?>>مدفوع</option>
                            </select>
                        </div>
                        
                        <div class="col-lg-2 mb-3">
                            <label class="form-label">ترتيب حسب</label>
                            <select class="form-select" name="sort">
                                <option value="newest" <?php echo $sort === 'newest' ? 'selected' : ''; ?>>الأحدث</option>
                                <option value="oldest" <?php echo $sort === 'oldest' ? 'selected' : ''; ?>>الأقدم</option>
                                <option value="popular" <?php echo $sort === 'popular' ? 'selected' : ''; ?>>الأكثر شعبية</option>
                                <option value="rating" <?php echo $sort === 'rating' ? 'selected' : ''; ?>>الأعلى تقييماً</option>
                                <option value="price_low" <?php echo $sort === 'price_low' ? 'selected' : ''; ?>>السعر: منخفض لعالي</option>
                                <option value="price_high" <?php echo $sort === 'price_high' ? 'selected' : ''; ?>>السعر: عالي لمنخفض</option>
                            </select>
                        </div>
                        
                        <div class="col-lg-1 mb-3">
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-filter"></i>
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </section>

    <!-- إحصائيات النتائج -->
    <section class="py-3">
        <div class="container">
            <div class="row">
                <div class="col-md-3">
                    <div class="stats-card">
                        <h4 class="text-primary"><?php echo $total_courses; ?></h4>
                        <p class="mb-0">إجمالي الكورسات</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card">
                        <h4 class="text-success"><?php echo $free_courses; ?></h4>
                        <p class="mb-0">كورسات مجانية</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card">
                        <h4 class="text-warning"><?php echo $paid_courses; ?></h4>
                        <p class="mb-0">كورسات مدفوعة</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card">
                        <h4 class="text-info"><?php echo count($categories); ?></h4>
                        <p class="mb-0">تخصصات متاحة</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- عرض الكورسات -->
    <section class="py-5">
        <div class="container">
            <?php if (empty($courses)): ?>
                <div class="no-courses">
                    <i class="fas fa-search fa-5x mb-4"></i>
                    <h3>لا توجد كورسات تطابق البحث</h3>
                    <p class="lead">جرب تغيير معايير البحث أو الفلاتر</p>
                    <a href="courses.php" class="btn btn-primary">عرض جميع الكورسات</a>
                </div>
            <?php else: ?>
                <div class="row">
                    <?php foreach ($courses as $course): ?>
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="card course-card">
                                <div class="position-relative">
                                    <?php if ($course['image_path']): ?>
                                        <img src="<?php echo htmlspecialchars($course['image_path']); ?>" 
                                             class="card-img-top" style="height: 200px; object-fit: cover;" 
                                             alt="<?php echo htmlspecialchars($course['title']); ?>">
                                    <?php else: ?>
                                        <div class="course-image">
                                            <i class="fas fa-graduation-cap"></i>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <div class="price-badge <?php echo $course['course_type'] === 'paid' ? 'paid' : ''; ?>">
                                        <?php if ($course['course_type'] === 'paid'): ?>
                                            <?php echo number_format($course['price'], 0); ?> <?php echo $course['currency']; ?>
                                        <?php else: ?>
                                            مجاني
                                        <?php endif; ?>
                                    </div>
                                </div>
                                
                                <div class="card-body">
                                    <h5 class="card-title"><?php echo htmlspecialchars($course['title']); ?></h5>
                                    <p class="card-text text-muted">
                                        <?php echo htmlspecialchars(substr($course['description'], 0, 100)) . '...'; ?>
                                    </p>
                                    
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <small class="text-muted">
                                            <i class="fas fa-user me-1"></i>
                                            <?php echo htmlspecialchars($course['instructor_name']); ?>
                                        </small>
                                        <small class="text-muted">
                                            <i class="fas fa-users me-1"></i>
                                            <?php echo $course['enrolled_students']; ?> طالب
                                        </small>
                                    </div>
                                    
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <small class="text-muted">
                                            <i class="fas fa-video me-1"></i>
                                            <?php echo $course['total_sessions']; ?> جلسة
                                        </small>
                                        <?php if ($course['avg_rating']): ?>
                                            <div class="d-flex align-items-center">
                                                <div class="rating-stars me-1">
                                                    <?php
                                                    $rating = round($course['avg_rating']);
                                                    for ($i = 1; $i <= 5; $i++) {
                                                        echo $i <= $rating ? '<i class="fas fa-star"></i>' : '<i class="far fa-star"></i>';
                                                    }
                                                    ?>
                                                </div>
                                                <small class="text-muted"><?php echo number_format($course['avg_rating'], 1); ?></small>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <?php if ($course['category']): ?>
                                        <div class="mb-3">
                                            <span class="badge bg-secondary"><?php echo htmlspecialchars($course['category']); ?></span>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                
                                <div class="card-footer bg-transparent">
                                    <a href="course-details.php?id=<?php echo $course['id']; ?>" class="btn btn-primary w-100">
                                        <i class="fas fa-eye me-1"></i>
                                        عرض التفاصيل
                                    </a>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </section>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تحديث الصفحة عند تغيير الفلاتر
        document.querySelectorAll('select[name="category"], select[name="type"], select[name="sort"]').forEach(select => {
            select.addEventListener('change', function() {
                this.form.submit();
            });
        });
        
        // تأثير التمرير السلس
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    </script>
</body>
</html>
