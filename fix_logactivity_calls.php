<?php
/**
 * إصلاح جميع استدعاءات دالة logActivity القديمة
 * Fix all old logActivity function calls
 */

echo "<!DOCTYPE html>";
echo "<html dir='rtl' lang='ar'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>إصلاح استدعاءات logActivity</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "</head>";
echo "<body>";
echo "<div class='container mt-4'>";
echo "<h2>🔧 إصلاح استدعاءات logActivity القديمة</h2>";

// قائمة الملفات والاستبدالات المطلوبة
$files_to_fix = [
    'ajax/reset_student_password.php' => [
        'old' => 'logActivity($conn, $_SESSION[\'user_id\'], \'reset_password\', "Password reset for student ID: $student_id");',
        'new' => 'logUserActivity(\'reset_password\', "Password reset for student ID: $student_id");'
    ],
    'ajax/remove_student.php' => [
        'old' => 'logActivity($conn, $_SESSION[\'user_id\'], \'remove_student\', "Student removed: ID $student_id");',
        'new' => 'logUserActivity(\'remove_student\', "Student removed: ID $student_id");'
    ],
    'instructor/manage-students.php' => [
        'old' => 'logActivity($conn, $_SESSION[\'user_id\'], \'إضافة طالب\', "تم إضافة طالب جديد: $name");',
        'new' => 'logUserActivity(\'إضافة طالب\', "تم إضافة طالب جديد: $name");'
    ]
];

$fixed_files = [];
$errors = [];

foreach ($files_to_fix as $file => $replacement) {
    if (file_exists($file)) {
        $content = file_get_contents($file);
        
        // البحث عن النمط القديم واستبداله
        $old_pattern = $replacement['old'];
        $new_replacement = $replacement['new'];
        
        // استبدال مرن للنمط
        $patterns = [
            '/logActivity\(\$conn,\s*\$_SESSION\[\'user_id\'\],\s*\'([^\']+)\',\s*"([^"]+)"\);/',
            '/logActivity\(\$conn,\s*\$_SESSION\[\'user_id\'\],\s*\'([^\']+)\',\s*\'([^\']+)\'\);/',
            '/logActivity\(\$conn,\s*\$_SESSION\[\'user_id\'\],\s*"([^"]+)",\s*"([^"]+)"\);/'
        ];
        
        $new_content = $content;
        $replaced = false;
        
        foreach ($patterns as $pattern) {
            $new_content = preg_replace_callback($pattern, function($matches) {
                $action = $matches[1];
                $description = $matches[2];
                return "logUserActivity('$action', \"$description\");";
            }, $new_content);
            
            if ($new_content !== $content) {
                $replaced = true;
                $content = $new_content;
            }
        }
        
        // التحقق من وجود activity_logger.php
        if (strpos($new_content, "require_once '../includes/activity_logger.php'") === false && 
            strpos($new_content, "require_once 'includes/activity_logger.php'") === false) {
            
            // إضافة require_once للملف
            if (strpos($file, 'ajax/') === 0) {
                // ملفات ajax
                $pattern = "/require_once '\.\.\/config\/database\.php';/";
                $replacement_include = "require_once '../config/database.php';\nrequire_once '../includes/activity_logger.php';";
            } elseif (strpos($file, 'instructor/') === 0) {
                // ملفات المدرب
                $pattern = "/require_once '\.\.\/includes\/security\.php';/";
                $replacement_include = "require_once '../includes/security.php';\nrequire_once '../includes/activity_logger.php';";
            } else {
                // ملفات الجذر
                $pattern = "/require_once 'includes\/security\.php';/";
                $replacement_include = "require_once 'includes/security.php';\nrequire_once 'includes/activity_logger.php';";
            }
            
            $new_content = preg_replace($pattern, $replacement_include, $new_content);
        }
        
        if ($new_content !== file_get_contents($file)) {
            if (file_put_contents($file, $new_content)) {
                $fixed_files[] = $file;
                echo "<div class='alert alert-success'>✅ تم إصلاح: $file</div>";
            } else {
                $errors[] = "فشل في كتابة: $file";
                echo "<div class='alert alert-danger'>❌ فشل في إصلاح: $file</div>";
            }
        } else {
            echo "<div class='alert alert-info'>ℹ️ الملف لا يحتاج إصلاح: $file</div>";
        }
    } else {
        echo "<div class='alert alert-warning'>⚠️ الملف غير موجود: $file</div>";
    }
}

// إصلاح ملفات إضافية قد تحتوي على استدعاءات قديمة
echo "<h4>🔍 البحث عن ملفات أخرى...</h4>";

$additional_files = glob('*.php');
$additional_files = array_merge($additional_files, glob('admin/*.php'));
$additional_files = array_merge($additional_files, glob('instructor/*.php'));
$additional_files = array_merge($additional_files, glob('ajax/*.php'));

foreach ($additional_files as $file) {
    if (file_exists($file) && !in_array($file, array_keys($files_to_fix))) {
        $content = file_get_contents($file);
        
        // البحث عن استدعاءات logActivity القديمة
        if (preg_match('/logActivity\(\$conn,/', $content)) {
            echo "<div class='alert alert-warning'>⚠️ وجد استدعاء قديم في: $file</div>";
            
            // محاولة إصلاح تلقائي
            $new_content = preg_replace_callback(
                '/logActivity\(\$conn,\s*\$_SESSION\[\'user_id\'\],\s*[\'"]([^\'"]+)[\'"],\s*[\'"]([^\'"]+)[\'"]\);/',
                function($matches) {
                    $action = $matches[1];
                    $description = $matches[2];
                    return "logUserActivity('$action', \"$description\");";
                },
                $content
            );
            
            if ($new_content !== $content) {
                // إضافة activity_logger.php إذا لم يكن موجود
                if (strpos($new_content, 'activity_logger.php') === false) {
                    if (strpos($file, 'admin/') === 0 || strpos($file, 'instructor/') === 0) {
                        $new_content = str_replace(
                            "require_once '../includes/security.php';",
                            "require_once '../includes/security.php';\nrequire_once '../includes/activity_logger.php';",
                            $new_content
                        );
                    } else {
                        $new_content = str_replace(
                            "require_once 'includes/security.php';",
                            "require_once 'includes/security.php';\nrequire_once 'includes/activity_logger.php';",
                            $new_content
                        );
                    }
                }
                
                if (file_put_contents($file, $new_content)) {
                    echo "<div class='alert alert-success'>✅ تم إصلاح تلقائي: $file</div>";
                    $fixed_files[] = $file;
                }
            }
        }
    }
}

// اختبار الدوال
echo "<h4>🧪 اختبار الدوال المحدثة</h4>";

try {
    require_once 'config/database.php';
    require_once 'includes/activity_logger.php';
    
    // اختبار دالة logUserActivity
    $test_result = logUserActivity('اختبار الإصلاح', 'اختبار بعد إصلاح الاستدعاءات');
    
    if ($test_result) {
        echo "<div class='alert alert-success'>✅ دالة logUserActivity تعمل بشكل صحيح</div>";
    } else {
        echo "<div class='alert alert-danger'>❌ دالة logUserActivity لا تعمل</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>❌ خطأ في الاختبار: " . htmlspecialchars($e->getMessage()) . "</div>";
}

// ملخص النتائج
echo "<h4>📊 ملخص النتائج:</h4>";
echo "<div class='row'>";
echo "<div class='col-md-6'>";
echo "<div class='card bg-success text-white'>";
echo "<div class='card-body text-center'>";
echo "<h3>" . count($fixed_files) . "</h3>";
echo "<p>ملفات تم إصلاحها</p>";
echo "</div>";
echo "</div>";
echo "</div>";

echo "<div class='col-md-6'>";
echo "<div class='card bg-danger text-white'>";
echo "<div class='card-body text-center'>";
echo "<h3>" . count($errors) . "</h3>";
echo "<p>أخطاء</p>";
echo "</div>";
echo "</div>";
echo "</div>";
echo "</div>";

if (!empty($fixed_files)) {
    echo "<h5>✅ الملفات المُصلحة:</h5>";
    echo "<ul class='list-group'>";
    foreach ($fixed_files as $file) {
        echo "<li class='list-group-item list-group-item-success'>$file</li>";
    }
    echo "</ul>";
}

if (!empty($errors)) {
    echo "<h5>❌ الأخطاء:</h5>";
    echo "<ul class='list-group'>";
    foreach ($errors as $error) {
        echo "<li class='list-group-item list-group-item-danger'>$error</li>";
    }
    echo "</ul>";
}

echo "<div class='mt-4'>";
echo "<a href='instructor/add-course.php' class='btn btn-primary btn-lg me-2'>➕ اختبار إضافة كورس</a>";
echo "<a href='admin/manage-sessions.php' class='btn btn-success btn-lg me-2'>🎥 إدارة الجلسات</a>";
echo "<a href='admin/activity-logs.php' class='btn btn-info btn-lg me-2'>📋 سجل الأنشطة</a>";
echo "<a href='fix_database_structure.php' class='btn btn-warning btn-lg'>🔧 إصلاح قاعدة البيانات</a>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>
