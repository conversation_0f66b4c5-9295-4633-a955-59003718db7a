<?php
/**
 * إصلاح مشاكل عرض الكورسات
 * Fix courses display issues
 */

require_once 'config/database.php';

echo "<!DOCTYPE html>";
echo "<html dir='rtl' lang='ar'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>إصلاح مشاكل عرض الكورسات</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "</head>";
echo "<body>";
echo "<div class='container mt-4'>";
echo "<h2>🔧 إصلاح مشاكل عرض الكورسات</h2>";

try {
    // 1. فحص جدول الكورسات
    echo "<h4>📚 فحص جدول الكورسات</h4>";
    
    $stmt = $conn->query("SELECT COUNT(*) FROM courses");
    $total_courses = $stmt->fetchColumn();
    
    echo "<div class='alert alert-info'>ℹ️ إجمالي الكورسات في قاعدة البيانات: $total_courses</div>";
    
    // 2. فحص الكورسات النشطة
    $stmt = $conn->query("SELECT COUNT(*) FROM courses WHERE status = 'active'");
    $active_courses = $stmt->fetchColumn();
    
    echo "<div class='alert alert-success'>✅ الكورسات النشطة: $active_courses</div>";
    
    // 3. فحص الكورسات غير النشطة
    $stmt = $conn->query("SELECT COUNT(*) FROM courses WHERE status != 'active' OR status IS NULL");
    $inactive_courses = $stmt->fetchColumn();
    
    if ($inactive_courses > 0) {
        echo "<div class='alert alert-warning'>⚠️ كورسات غير نشطة: $inactive_courses</div>";
        
        // تفعيل الكورسات غير النشطة
        $stmt = $conn->query("UPDATE courses SET status = 'active' WHERE status != 'active' OR status IS NULL");
        $updated = $stmt->rowCount();
        
        if ($updated > 0) {
            echo "<div class='alert alert-success'>✅ تم تفعيل $updated كورس</div>";
        }
    }

    // 4. فحص الأعمدة المطلوبة
    echo "<h4>🔍 فحص أعمدة الكورسات</h4>";
    
    $stmt = $conn->query("SHOW COLUMNS FROM courses");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $required_columns = ['id', 'title', 'description', 'instructor_id', 'status', 'created_at', 'category', 'course_type', 'price', 'currency'];
    $missing_columns = [];
    
    foreach ($required_columns as $col) {
        if (!in_array($col, $columns)) {
            $missing_columns[] = $col;
        }
    }
    
    if (!empty($missing_columns)) {
        echo "<div class='alert alert-warning'>⚠️ أعمدة مفقودة: " . implode(', ', $missing_columns) . "</div>";
        
        // إضافة الأعمدة المفقودة
        foreach ($missing_columns as $col) {
            switch ($col) {
                case 'status':
                    $conn->exec("ALTER TABLE courses ADD COLUMN status ENUM('active', 'inactive') DEFAULT 'active'");
                    echo "<div class='alert alert-success'>✅ تم إضافة عمود status</div>";
                    break;
                case 'category':
                    $conn->exec("ALTER TABLE courses ADD COLUMN category VARCHAR(100) DEFAULT NULL");
                    echo "<div class='alert alert-success'>✅ تم إضافة عمود category</div>";
                    break;
                case 'course_type':
                    $conn->exec("ALTER TABLE courses ADD COLUMN course_type ENUM('free', 'paid') DEFAULT 'free'");
                    echo "<div class='alert alert-success'>✅ تم إضافة عمود course_type</div>";
                    break;
                case 'price':
                    $conn->exec("ALTER TABLE courses ADD COLUMN price DECIMAL(10,2) DEFAULT 0.00");
                    echo "<div class='alert alert-success'>✅ تم إضافة عمود price</div>";
                    break;
                case 'currency':
                    $conn->exec("ALTER TABLE courses ADD COLUMN currency VARCHAR(10) DEFAULT 'ريال'");
                    echo "<div class='alert alert-success'>✅ تم إضافة عمود currency</div>";
                    break;
            }
        }
    } else {
        echo "<div class='alert alert-info'>ℹ️ جميع الأعمدة المطلوبة موجودة</div>";
    }

    // 5. تحديث الكورسات بدون تخصص
    echo "<h4>🏷️ تحديث تخصصات الكورسات</h4>";
    
    $categories = [
        'البرمجة وتطوير الويب',
        'التصميم الجرافيكي',
        'التسويق الرقمي',
        'إدارة الأعمال',
        'اللغات',
        'العلوم والرياضيات'
    ];
    
    $stmt = $conn->query("SELECT id, title FROM courses WHERE category IS NULL OR category = ''");
    $courses_without_category = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $updated_categories = 0;
    foreach ($courses_without_category as $course) {
        $random_category = $categories[array_rand($categories)];
        
        $stmt = $conn->prepare("UPDATE courses SET category = ? WHERE id = ?");
        $stmt->execute([$random_category, $course['id']]);
        $updated_categories++;
    }
    
    if ($updated_categories > 0) {
        echo "<div class='alert alert-success'>✅ تم تحديث تخصص $updated_categories كورس</div>";
    } else {
        echo "<div class='alert alert-info'>ℹ️ جميع الكورسات لديها تخصصات</div>";
    }

    // 6. تحديث أنواع الكورسات
    echo "<h4>💰 تحديث أنواع وأسعار الكورسات</h4>";
    
    $stmt = $conn->query("SELECT id, title FROM courses WHERE course_type IS NULL OR course_type = ''");
    $courses_without_type = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $updated_types = 0;
    foreach ($courses_without_type as $course) {
        $is_paid = rand(0, 1); // عشوائي: مجاني أو مدفوع
        $type = $is_paid ? 'paid' : 'free';
        $price = $is_paid ? rand(99, 499) : 0;
        
        $stmt = $conn->prepare("UPDATE courses SET course_type = ?, price = ? WHERE id = ?");
        $stmt->execute([$type, $price, $course['id']]);
        $updated_types++;
    }
    
    if ($updated_types > 0) {
        echo "<div class='alert alert-success'>✅ تم تحديث نوع $updated_types كورس</div>";
    } else {
        echo "<div class='alert alert-info'>ℹ️ جميع الكورسات لديها أنواع محددة</div>";
    }

    // 7. عرض الكورسات الحالية
    echo "<h4>📋 الكورسات الحالية</h4>";
    
    $stmt = $conn->query("
        SELECT c.*, u.name as instructor_name,
               (SELECT COUNT(*) FROM course_enrollments WHERE course_id = c.id AND status = 'active') as enrolled_students
        FROM courses c
        LEFT JOIN users u ON c.instructor_id = u.id
        ORDER BY c.created_at DESC
    ");
    $courses = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($courses)) {
        echo "<div class='table-responsive'>";
        echo "<table class='table table-striped'>";
        echo "<thead class='table-dark'>";
        echo "<tr>";
        echo "<th>ID</th>";
        echo "<th>العنوان</th>";
        echo "<th>المدرب</th>";
        echo "<th>التخصص</th>";
        echo "<th>النوع</th>";
        echo "<th>السعر</th>";
        echo "<th>الطلاب</th>";
        echo "<th>الحالة</th>";
        echo "</tr>";
        echo "</thead>";
        echo "<tbody>";
        
        foreach ($courses as $course) {
            echo "<tr>";
            echo "<td>{$course['id']}</td>";
            echo "<td>" . htmlspecialchars($course['title']) . "</td>";
            echo "<td>" . htmlspecialchars($course['instructor_name'] ?? 'غير محدد') . "</td>";
            echo "<td>" . htmlspecialchars($course['category'] ?? 'غير محدد') . "</td>";
            echo "<td>";
            if ($course['course_type'] === 'paid') {
                echo "<span class='badge bg-warning'>مدفوع</span>";
            } else {
                echo "<span class='badge bg-success'>مجاني</span>";
            }
            echo "</td>";
            echo "<td>";
            if ($course['course_type'] === 'paid') {
                echo number_format($course['price'], 0) . " " . $course['currency'];
            } else {
                echo "مجاني";
            }
            echo "</td>";
            echo "<td>{$course['enrolled_students']}</td>";
            echo "<td>";
            if ($course['status'] === 'active') {
                echo "<span class='badge bg-success'>نشط</span>";
            } else {
                echo "<span class='badge bg-secondary'>غير نشط</span>";
            }
            echo "</td>";
            echo "</tr>";
        }
        
        echo "</tbody>";
        echo "</table>";
        echo "</div>";
    }

    // 8. اختبار صفحة الكورسات
    echo "<h4>🧪 اختبار صفحة الكورسات</h4>";
    
    try {
        // محاكاة الاستعلام من صفحة courses.php
        $stmt = $conn->prepare("
            SELECT c.*, u.name as instructor_name, u.email as instructor_email,
                   (SELECT COUNT(*) FROM course_enrollments WHERE course_id = c.id AND status = 'active') as enrolled_students,
                   (SELECT COUNT(*) FROM sessions WHERE course_id = c.id) as total_sessions,
                   (SELECT AVG(rating) FROM course_reviews WHERE course_id = c.id) as avg_rating,
                   (SELECT COUNT(*) FROM course_reviews WHERE course_id = c.id) as total_reviews
            FROM courses c
            JOIN users u ON c.instructor_id = u.id
            WHERE c.status = 'active'
            ORDER BY c.created_at DESC
            LIMIT 12
        ");
        $stmt->execute();
        $test_courses = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<div class='alert alert-success'>✅ استعلام صفحة الكورسات يعمل بنجاح</div>";
        echo "<div class='alert alert-info'>ℹ️ عدد الكورسات المعروضة: " . count($test_courses) . "</div>";
        
    } catch (Exception $e) {
        echo "<div class='alert alert-danger'>❌ خطأ في استعلام صفحة الكورسات: " . htmlspecialchars($e->getMessage()) . "</div>";
    }

    // 9. إحصائيات نهائية
    echo "<h4>📊 الإحصائيات النهائية</h4>";
    
    $stmt = $conn->query("SELECT COUNT(*) FROM courses WHERE status = 'active'");
    $final_active = $stmt->fetchColumn();
    
    $stmt = $conn->query("SELECT COUNT(*) FROM courses WHERE course_type = 'free' AND status = 'active'");
    $final_free = $stmt->fetchColumn();
    
    $stmt = $conn->query("SELECT COUNT(*) FROM courses WHERE course_type = 'paid' AND status = 'active'");
    $final_paid = $stmt->fetchColumn();
    
    $stmt = $conn->query("SELECT COUNT(DISTINCT category) FROM courses WHERE category IS NOT NULL AND status = 'active'");
    $final_categories = $stmt->fetchColumn();
    
    echo "<div class='row'>";
    
    echo "<div class='col-md-3'>";
    echo "<div class='card bg-primary text-white'>";
    echo "<div class='card-body text-center'>";
    echo "<h3>$final_active</h3>";
    echo "<p class='mb-0'>كورسات نشطة</p>";
    echo "</div></div></div>";
    
    echo "<div class='col-md-3'>";
    echo "<div class='card bg-success text-white'>";
    echo "<div class='card-body text-center'>";
    echo "<h3>$final_free</h3>";
    echo "<p class='mb-0'>كورسات مجانية</p>";
    echo "</div></div></div>";
    
    echo "<div class='col-md-3'>";
    echo "<div class='card bg-warning text-white'>";
    echo "<div class='card-body text-center'>";
    echo "<h3>$final_paid</h3>";
    echo "<p class='mb-0'>كورسات مدفوعة</p>";
    echo "</div></div></div>";
    
    echo "<div class='col-md-3'>";
    echo "<div class='card bg-info text-white'>";
    echo "<div class='card-body text-center'>";
    echo "<h3>$final_categories</h3>";
    echo "<p class='mb-0'>تخصصات</p>";
    echo "</div></div></div>";
    
    echo "</div>";

    echo "<div class='alert alert-success mt-4'>";
    echo "<h5>🎉 تم إصلاح جميع مشاكل الكورسات بنجاح!</h5>";
    echo "<ul class='mb-0'>";
    echo "<li>✅ تم إصلاح جدول الكورسات</li>";
    echo "<li>✅ تم تفعيل الكورسات غير النشطة</li>";
    echo "<li>✅ تم إضافة الأعمدة المفقودة</li>";
    echo "<li>✅ تم تحديث التخصصات والأسعار</li>";
    echo "<li>✅ تم اختبار صفحة الكورسات</li>";
    echo "</ul>";
    echo "</div>";

} catch (Exception $e) {
    echo "<div class='alert alert-danger'>❌ خطأ: " . htmlspecialchars($e->getMessage()) . "</div>";
}

echo "<div class='text-center mt-4'>";
echo "<a href='courses.php' class='btn btn-primary btn-lg me-2'>📚 صفحة الكورسات</a>";
echo "<a href='admin/manage-courses.php' class='btn btn-success btn-lg me-2'>⚙️ إدارة الكورسات</a>";
echo "<a href='index.php' class='btn btn-info btn-lg'>🏠 الصفحة الرئيسية</a>";
echo "</div>";

echo "<div class='mt-4'>";
echo "<div class='alert alert-info'>";
echo "<h6>💡 تم إصلاح المشاكل التالية:</h6>";
echo "<ul>";
echo "<li>خطأ 'Undefined variable \$courses' في إدارة الكورسات</li>";
echo "<li>استعلام خاطئ يبحث عن جدول course_students بدلاً من course_enrollments</li>";
echo "<li>إضافة معالجة للأخطاء وقيم افتراضية</li>";
echo "<li>تفعيل الكورسات غير النشطة</li>";
echo "<li>إضافة التخصصات والأسعار المفقودة</li>";
echo "</ul>";
echo "</div>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>
