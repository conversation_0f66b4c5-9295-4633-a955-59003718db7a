-- إنشاء قاعدة البيانات الموحدة
DROP DATABASE IF EXISTS zoom_unified_platform;
CREATE DATABASE zoom_unified_platform CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE zoom_unified_platform;

-- ===== الجداول الأساسية =====

-- جدول المستخدمين الموحد
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    role ENUM('admin', 'instructor', 'student') NOT NULL DEFAULT 'student',
    status ENUM('active', 'inactive', 'pending', 'suspended') DEFAULT 'pending',
    profile_image VARCHAR(500),
    bio TEXT,
    specialization VARCHAR(255),
    experience_years INT DEFAULT 0,
    education TEXT,
    certificates TEXT,
    social_links JSON,
    email_verified BOOLEAN DEFAULT FALSE,
    email_verification_token VARCHAR(255),
    password_reset_token VARCHAR(255),
    password_reset_expires DATETIME,
    last_login TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_email (email),
    INDEX idx_role (role),
    INDEX idx_status (status)
) ENGINE=InnoDB;

-- جدول التصنيفات
CREATE TABLE categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    icon VARCHAR(100),
    color VARCHAR(7) DEFAULT '#007bff',
    parent_id INT NULL,
    sort_order INT DEFAULT 0,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (parent_id) REFERENCES categories(id) ON DELETE SET NULL,
    INDEX idx_parent (parent_id),
    INDEX idx_status (status)
) ENGINE=InnoDB;

-- جدول الكورسات الموحد
CREATE TABLE courses (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    short_description VARCHAR(500),
    instructor_id INT NOT NULL,
    category_id INT,
    level ENUM('beginner', 'intermediate', 'advanced') DEFAULT 'beginner',
    language VARCHAR(10) DEFAULT 'ar',
    duration_hours INT DEFAULT 0,
    price DECIMAL(10,2) DEFAULT 0.00,
    discount_price DECIMAL(10,2) NULL,
    is_free BOOLEAN DEFAULT TRUE,
    max_students INT DEFAULT 0,
    requirements TEXT,
    what_you_learn TEXT,
    course_image VARCHAR(500),
    course_video VARCHAR(500),
    status ENUM('draft', 'published', 'archived') DEFAULT 'draft',
    featured BOOLEAN DEFAULT FALSE,
    rating DECIMAL(3,2) DEFAULT 0.00,
    total_reviews INT DEFAULT 0,
    total_students INT DEFAULT 0,
    total_lessons INT DEFAULT 0,
    total_duration INT DEFAULT 0,
    certificate_enabled BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (instructor_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL,
    INDEX idx_instructor (instructor_id),
    INDEX idx_category (category_id),
    INDEX idx_status (status),
    INDEX idx_featured (featured),
    INDEX idx_price (price)
) ENGINE=InnoDB;

-- جدول فصول الكورس
CREATE TABLE course_chapters (
    id INT AUTO_INCREMENT PRIMARY KEY,
    course_id INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    sort_order INT DEFAULT 0,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    INDEX idx_course (course_id),
    INDEX idx_sort (sort_order)
) ENGINE=InnoDB;

-- جدول دروس الكورس
CREATE TABLE course_lessons (
    id INT AUTO_INCREMENT PRIMARY KEY,
    course_id INT NOT NULL,
    chapter_id INT,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    content TEXT,
    video_url VARCHAR(500),
    video_duration INT DEFAULT 0,
    lesson_type ENUM('video', 'text', 'quiz', 'assignment') DEFAULT 'video',
    sort_order INT DEFAULT 0,
    is_free BOOLEAN DEFAULT FALSE,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    FOREIGN KEY (chapter_id) REFERENCES course_chapters(id) ON DELETE SET NULL,
    INDEX idx_course (course_id),
    INDEX idx_chapter (chapter_id),
    INDEX idx_sort (sort_order)
) ENGINE=InnoDB;

-- جدول تسجيل الطلاب في الكورسات
CREATE TABLE course_enrollments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    student_id INT NOT NULL,
    course_id INT NOT NULL,
    enrollment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completion_date TIMESTAMP NULL,
    progress_percentage DECIMAL(5,2) DEFAULT 0.00,
    status ENUM('active', 'completed', 'dropped', 'suspended') DEFAULT 'active',
    payment_id INT NULL,
    certificate_issued BOOLEAN DEFAULT FALSE,
    certificate_date TIMESTAMP NULL,
    
    FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    UNIQUE KEY unique_enrollment (student_id, course_id),
    INDEX idx_student (student_id),
    INDEX idx_course (course_id),
    INDEX idx_status (status)
) ENGINE=InnoDB;

-- جدول طلبات الانضمام للكورسات
CREATE TABLE course_join_requests (
    id INT AUTO_INCREMENT PRIMARY KEY,
    student_id INT NOT NULL,
    course_id INT NOT NULL,
    instructor_id INT NOT NULL,
    message TEXT,
    status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
    response_message TEXT,
    requested_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    responded_at TIMESTAMP NULL,
    responded_by INT NULL,
    
    FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    FOREIGN KEY (instructor_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (responded_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_student (student_id),
    INDEX idx_course (course_id),
    INDEX idx_instructor (instructor_id),
    INDEX idx_status (status)
) ENGINE=InnoDB;

-- جدول الجلسات المباشرة
CREATE TABLE sessions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    course_id INT NOT NULL,
    instructor_id INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    session_date DATE NOT NULL,
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    meeting_url VARCHAR(500),
    meeting_id VARCHAR(100),
    meeting_password VARCHAR(100),
    max_attendees INT DEFAULT 0,
    status ENUM('scheduled', 'live', 'completed', 'cancelled') DEFAULT 'scheduled',
    recording_url VARCHAR(500),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    FOREIGN KEY (instructor_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_course (course_id),
    INDEX idx_instructor (instructor_id),
    INDEX idx_date (session_date),
    INDEX idx_status (status)
) ENGINE=InnoDB;

-- جدول حضور الجلسات
CREATE TABLE session_attendees (
    id INT AUTO_INCREMENT PRIMARY KEY,
    session_id INT NOT NULL,
    user_id INT NOT NULL,
    joined_at TIMESTAMP NULL,
    left_at TIMESTAMP NULL,
    duration_minutes INT DEFAULT 0,
    status ENUM('registered', 'attended', 'absent') DEFAULT 'registered',
    
    FOREIGN KEY (session_id) REFERENCES sessions(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_attendance (session_id, user_id),
    INDEX idx_session (session_id),
    INDEX idx_user (user_id)
) ENGINE=InnoDB;

-- ===== نظام الواجبات والتقييم =====

-- جدول الواجبات
CREATE TABLE assignments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    course_id INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    instructions TEXT,
    due_date DATETIME NOT NULL,
    max_grade DECIMAL(5,2) DEFAULT 100.00,
    is_required BOOLEAN DEFAULT FALSE,
    status ENUM('active', 'inactive', 'draft') DEFAULT 'active',
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_course (course_id),
    INDEX idx_due_date (due_date),
    INDEX idx_status (status)
) ENGINE=InnoDB;

-- جدول تسليمات الواجبات
CREATE TABLE assignment_submissions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    assignment_id INT NOT NULL,
    student_id INT NOT NULL,
    submission_text TEXT,
    file_path VARCHAR(500),
    original_filename VARCHAR(255),
    file_size INT,
    submission_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status ENUM('draft', 'submitted', 'graded', 'returned') DEFAULT 'submitted',
    grade DECIMAL(5,2) NULL,
    feedback TEXT,
    graded_by INT NULL,
    graded_at TIMESTAMP NULL,
    late_submission BOOLEAN DEFAULT FALSE,

    FOREIGN KEY (assignment_id) REFERENCES assignments(id) ON DELETE CASCADE,
    FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (graded_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_assignment (assignment_id),
    INDEX idx_student (student_id),
    INDEX idx_status (status)
) ENGINE=InnoDB;

-- جدول ملفات الواجبات
CREATE TABLE assignment_files (
    id INT AUTO_INCREMENT PRIMARY KEY,
    assignment_id INT NOT NULL,
    filename VARCHAR(255) NOT NULL,
    original_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size INT NOT NULL,
    file_type VARCHAR(100),
    uploaded_by INT NOT NULL,
    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (assignment_id) REFERENCES assignments(id) ON DELETE CASCADE,
    FOREIGN KEY (uploaded_by) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_assignment (assignment_id)
) ENGINE=InnoDB;

-- جدول ملفات التسليمات
CREATE TABLE submission_files (
    id INT AUTO_INCREMENT PRIMARY KEY,
    submission_id INT NOT NULL,
    filename VARCHAR(255) NOT NULL,
    original_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size INT NOT NULL,
    file_type VARCHAR(100),
    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (submission_id) REFERENCES assignment_submissions(id) ON DELETE CASCADE,
    INDEX idx_submission (submission_id)
) ENGINE=InnoDB;

-- جدول الاختبارات
CREATE TABLE quizzes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    course_id INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    instructions TEXT,
    time_limit INT DEFAULT 0,
    max_attempts INT DEFAULT 1,
    passing_score DECIMAL(5,2) DEFAULT 60.00,
    status ENUM('active', 'inactive', 'draft') DEFAULT 'active',
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_course (course_id),
    INDEX idx_status (status)
) ENGINE=InnoDB;

-- جدول درجات الطلاب
CREATE TABLE student_grades (
    id INT AUTO_INCREMENT PRIMARY KEY,
    student_id INT NOT NULL,
    course_id INT NOT NULL,
    assignment_id INT NULL,
    quiz_id INT NULL,
    grade DECIMAL(5,2) NOT NULL,
    max_grade DECIMAL(5,2) NOT NULL,
    percentage DECIMAL(5,2) GENERATED ALWAYS AS ((grade / max_grade) * 100) STORED,
    feedback TEXT,
    graded_by INT NOT NULL,
    graded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    FOREIGN KEY (assignment_id) REFERENCES assignments(id) ON DELETE CASCADE,
    FOREIGN KEY (quiz_id) REFERENCES quizzes(id) ON DELETE CASCADE,
    FOREIGN KEY (graded_by) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_student (student_id),
    INDEX idx_course (course_id)
) ENGINE=InnoDB;

-- ===== نظام الدفع والعمولات =====

-- جدول المدفوعات
CREATE TABLE payments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    course_id INT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'USD',
    payment_method ENUM('credit_card', 'paypal', 'bank_transfer', 'wallet') NOT NULL,
    payment_gateway VARCHAR(50),
    transaction_id VARCHAR(255),
    gateway_response JSON,
    status ENUM('pending', 'completed', 'failed', 'refunded', 'cancelled') DEFAULT 'pending',
    paid_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    INDEX idx_user (user_id),
    INDEX idx_course (course_id),
    INDEX idx_status (status),
    INDEX idx_transaction (transaction_id)
) ENGINE=InnoDB;

-- جدول إعدادات العمولة
CREATE TABLE commission_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    commission_rate DECIMAL(5,2) NOT NULL DEFAULT 30.00,
    min_rate DECIMAL(5,2) NOT NULL DEFAULT 15.00,
    max_rate DECIMAL(5,2) NOT NULL DEFAULT 50.00,
    effective_from DATE NOT NULL,
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_effective_date (effective_from)
) ENGINE=InnoDB;

-- جدول العمولات
CREATE TABLE commissions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    instructor_id INT NOT NULL,
    course_id INT NOT NULL,
    payment_id INT NOT NULL,
    course_price DECIMAL(10,2) NOT NULL,
    commission_rate DECIMAL(5,2) NOT NULL,
    commission_amount DECIMAL(10,2) NOT NULL,
    instructor_amount DECIMAL(10,2) NOT NULL,
    status ENUM('pending', 'approved', 'paid', 'cancelled') DEFAULT 'pending',
    approved_by INT NULL,
    approved_at TIMESTAMP NULL,
    paid_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (instructor_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    FOREIGN KEY (payment_id) REFERENCES payments(id) ON DELETE CASCADE,
    FOREIGN KEY (approved_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_instructor (instructor_id),
    INDEX idx_course (course_id),
    INDEX idx_payment (payment_id),
    INDEX idx_status (status)
) ENGINE=InnoDB;

-- جدول أرباح المدربين
CREATE TABLE instructor_earnings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    instructor_id INT NOT NULL,
    total_earnings DECIMAL(10,2) DEFAULT 0.00,
    pending_earnings DECIMAL(10,2) DEFAULT 0.00,
    paid_earnings DECIMAL(10,2) DEFAULT 0.00,
    last_payout_date TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (instructor_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_instructor (instructor_id)
) ENGINE=InnoDB;

-- جدول سلة التسوق
CREATE TABLE shopping_cart (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    course_id INT NOT NULL,
    added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    UNIQUE KEY unique_cart_item (user_id, course_id),
    INDEX idx_user (user_id)
) ENGINE=InnoDB;

-- ===== نظام التقييمات والمراجعات =====

-- جدول مراجعات الكورسات
CREATE TABLE course_reviews (
    id INT AUTO_INCREMENT PRIMARY KEY,
    course_id INT NOT NULL,
    student_id INT NOT NULL,
    rating INT NOT NULL CHECK (rating >= 1 AND rating <= 5),
    review_text TEXT,
    status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_review (course_id, student_id),
    INDEX idx_course (course_id),
    INDEX idx_student (student_id),
    INDEX idx_rating (rating)
) ENGINE=InnoDB;

-- جدول مراجعات المدربين
CREATE TABLE instructor_reviews (
    id INT AUTO_INCREMENT PRIMARY KEY,
    instructor_id INT NOT NULL,
    student_id INT NOT NULL,
    course_id INT NOT NULL,
    rating INT NOT NULL CHECK (rating >= 1 AND rating <= 5),
    review_text TEXT,
    status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (instructor_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    UNIQUE KEY unique_instructor_review (instructor_id, student_id, course_id),
    INDEX idx_instructor (instructor_id),
    INDEX idx_student (student_id),
    INDEX idx_rating (rating)
) ENGINE=InnoDB;

-- ===== نظام الشهادات =====

-- جدول الشهادات
CREATE TABLE certificates (
    id INT AUTO_INCREMENT PRIMARY KEY,
    student_id INT NOT NULL,
    course_id INT NOT NULL,
    instructor_id INT NOT NULL,
    certificate_number VARCHAR(100) UNIQUE NOT NULL,
    issue_date DATE NOT NULL,
    completion_date DATE NOT NULL,
    final_grade DECIMAL(5,2),
    certificate_url VARCHAR(500),
    status ENUM('active', 'revoked') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    FOREIGN KEY (instructor_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_certificate (student_id, course_id),
    INDEX idx_student (student_id),
    INDEX idx_course (course_id),
    INDEX idx_certificate_number (certificate_number)
) ENGINE=InnoDB;

-- جدول شهادات المدربين
CREATE TABLE instructor_certificates (
    id INT AUTO_INCREMENT PRIMARY KEY,
    instructor_id INT NOT NULL,
    certificate_name VARCHAR(255) NOT NULL,
    issuing_organization VARCHAR(255) NOT NULL,
    issue_date DATE NOT NULL,
    expiry_date DATE NULL,
    certificate_url VARCHAR(500),
    verification_url VARCHAR(500),
    status ENUM('active', 'expired', 'revoked') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (instructor_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_instructor (instructor_id),
    INDEX idx_status (status)
) ENGINE=InnoDB;

-- ===== نظام الإشعارات والأنشطة =====

-- جدول الإشعارات
CREATE TABLE notifications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    type ENUM('info', 'success', 'warning', 'error') DEFAULT 'info',
    category ENUM('system', 'course', 'payment', 'assignment', 'session') DEFAULT 'system',
    action_url VARCHAR(500),
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    read_at TIMESTAMP NULL,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user (user_id),
    INDEX idx_is_read (is_read),
    INDEX idx_type (type),
    INDEX idx_category (category)
) ENGINE=InnoDB;

-- جدول سجل الأنشطة
CREATE TABLE activity_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NULL,
    action VARCHAR(255) NOT NULL,
    description TEXT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_action (action),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB;

-- جدول محاولات تسجيل الدخول
CREATE TABLE login_attempts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    email VARCHAR(255),
    success BOOLEAN,
    ip_address VARCHAR(45),
    attempt_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    INDEX idx_email (email),
    INDEX idx_ip (ip_address),
    INDEX idx_attempt_time (attempt_time)
) ENGINE=InnoDB;

-- جدول عناوين IP المحظورة
CREATE TABLE blocked_ips (
    id INT AUTO_INCREMENT PRIMARY KEY,
    ip VARCHAR(45) NOT NULL,
    blocked_until TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    INDEX idx_ip (ip),
    INDEX idx_blocked_until (blocked_until)
) ENGINE=InnoDB;

-- ===== إعدادات النظام =====

-- جدول إعدادات النظام
CREATE TABLE system_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(255) UNIQUE NOT NULL,
    setting_value TEXT,
    setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
    description TEXT,
    updated_by INT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_key (setting_key)
) ENGINE=InnoDB;

-- جدول الصلاحيات
CREATE TABLE permissions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) UNIQUE NOT NULL,
    description TEXT,
    module VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB;

-- جدول صلاحيات الأدوار
CREATE TABLE role_permissions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    role ENUM('admin', 'instructor', 'student') NOT NULL,
    permission_id INT NOT NULL,
    granted BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE,
    UNIQUE KEY unique_role_permission (role, permission_id),
    INDEX idx_role (role)
) ENGINE=InnoDB;

-- ===== إدراج البيانات الأساسية =====

-- إدراج التصنيفات الأساسية
INSERT INTO categories (name, description, icon, color) VALUES
('البرمجة', 'دورات البرمجة وتطوير البرمجيات', 'fas fa-code', '#007bff'),
('التصميم', 'دورات التصميم الجرافيكي وتصميم المواقع', 'fas fa-paint-brush', '#28a745'),
('التسويق', 'دورات التسويق الرقمي والتقليدي', 'fas fa-bullhorn', '#ffc107'),
('الأعمال', 'دورات إدارة الأعمال والريادة', 'fas fa-briefcase', '#dc3545'),
('اللغات', 'دورات تعلم اللغات المختلفة', 'fas fa-language', '#6f42c1');

-- إدراج إعدادات النظام الأساسية
INSERT INTO system_settings (setting_key, setting_value, setting_type, description) VALUES
('site_name', 'منصة التعلم الموحدة', 'string', 'اسم الموقع'),
('site_description', 'منصة تعليمية شاملة للكورسات المباشرة والمسجلة', 'string', 'وصف الموقع'),
('commission_rate', '30', 'number', 'نسبة العمولة الافتراضية'),
('currency', 'USD', 'string', 'العملة الافتراضية'),
('max_file_size', '10485760', 'number', 'الحد الأقصى لحجم الملف بالبايت (10MB)'),
('allowed_file_types', 'pdf,doc,docx,ppt,pptx,jpg,jpeg,png,gif,mp4,mp3', 'string', 'أنواع الملفات المسموحة'),
('email_verification_required', 'true', 'boolean', 'هل التحقق من البريد الإلكتروني مطلوب'),
('auto_approve_instructors', 'false', 'boolean', 'الموافقة التلقائية على المدربين');

-- إدراج الصلاحيات الأساسية
INSERT INTO permissions (name, description, module) VALUES
('manage_users', 'إدارة المستخدمين', 'users'),
('manage_courses', 'إدارة الكورسات', 'courses'),
('manage_payments', 'إدارة المدفوعات', 'payments'),
('manage_settings', 'إدارة إعدادات النظام', 'settings'),
('view_reports', 'عرض التقارير', 'reports'),
('create_course', 'إنشاء كورس جديد', 'courses'),
('edit_own_course', 'تعديل الكورسات الخاصة', 'courses'),
('grade_assignments', 'تقييم الواجبات', 'assignments'),
('manage_sessions', 'إدارة الجلسات المباشرة', 'sessions'),
('enroll_courses', 'التسجيل في الكورسات', 'enrollments');

-- إدراج صلاحيات الأدوار
INSERT INTO role_permissions (role, permission_id) VALUES
-- صلاحيات الأدمن (جميع الصلاحيات)
('admin', 1), ('admin', 2), ('admin', 3), ('admin', 4), ('admin', 5),
-- صلاحيات المدرب
('instructor', 6), ('instructor', 7), ('instructor', 8), ('instructor', 9), ('instructor', 5),
-- صلاحيات الطالب
('student', 10);

-- إدراج مستخدم أدمن أساسي أولاً
INSERT INTO users (id, name, email, password, role, status, created_at) VALUES
(1, 'مدير النظام', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin', 'active', NOW());

-- إدراج إعدادات العمولة الأساسية
INSERT INTO commission_settings (commission_rate, min_rate, max_rate, effective_from, created_by) VALUES
(30.00, 15.00, 50.00, CURDATE(), 1);

-- ===== نقل البيانات من قواعد البيانات الموجودة =====

-- نقل المستخدمين من zoom_platform
INSERT IGNORE INTO users (id, name, email, password, phone, role, status, last_login, created_at, updated_at)
SELECT id, name, email, password, phone, role, status, last_login, created_at, updated_at
FROM zoom_platform.users;

-- نقل الكورسات من zoom_platform
INSERT IGNORE INTO courses (id, title, description, instructor_id, price, status, created_at, updated_at)
SELECT id, title, description, instructor_id,
       COALESCE(price, 0.00) as price,
       COALESCE(status, 'published') as status,
       created_at, updated_at
FROM zoom_platform.courses;

-- نقل تسجيلات الكورسات من zoom_platform
INSERT IGNORE INTO course_enrollments (student_id, course_id, enrollment_date, status)
SELECT student_id, course_id, enrollment_date,
       COALESCE(status, 'active') as status
FROM zoom_platform.course_enrollments;

-- نقل الجلسات من zoom_platform
INSERT IGNORE INTO sessions (id, course_id, instructor_id, title, description, session_date, start_time, end_time, status, created_at, updated_at)
SELECT id, course_id, instructor_id, title, description,
       DATE(session_datetime) as session_date,
       TIME(session_datetime) as start_time,
       ADDTIME(TIME(session_datetime), '01:00:00') as end_time,
       COALESCE(status, 'scheduled') as status,
       created_at, updated_at
FROM zoom_platform.sessions;

-- نقل الواجبات من zoom_platform
INSERT IGNORE INTO assignments (id, course_id, title, description, instructions, due_date, max_grade, status, created_by, created_at, updated_at)
SELECT id, course_id, title, description, instructions, due_date,
       COALESCE(max_grade, 100.00) as max_grade,
       COALESCE(status, 'active') as status,
       COALESCE(created_by, 1) as created_by,
       created_at, updated_at
FROM zoom_platform.assignments;

-- نقل تسليمات الواجبات من zoom_platform
INSERT IGNORE INTO assignment_submissions (id, assignment_id, student_id, submission_text, file_path, original_filename, file_size, submission_date, status, grade, feedback, graded_by, graded_at, late_submission)
SELECT id, assignment_id, student_id, submission_text, file_path, original_filename, file_size, submission_date,
       COALESCE(status, 'submitted') as status,
       grade, feedback, graded_by, graded_at,
       COALESCE(late_submission, FALSE) as late_submission
FROM zoom_platform.assignment_submissions;

-- نقل درجات الطلاب من zoom_platform
INSERT IGNORE INTO student_grades (id, student_id, course_id, assignment_id, grade, max_grade, feedback, graded_by, graded_at)
SELECT id, student_id, course_id, assignment_id, grade, max_grade, feedback, graded_by, graded_at
FROM zoom_platform.student_grades;

-- نقل البيانات الإضافية من zoom_learning_system إذا كانت موجودة
-- (سيتم تشغيلها فقط إذا كانت الجداول موجودة)

-- نقل التصنيفات من zoom_learning_system
INSERT IGNORE INTO categories (id, name, description, icon, color, parent_id, sort_order, status, created_at, updated_at)
SELECT id, name, description, icon, color, parent_id, sort_order, status, created_at, updated_at
FROM zoom_learning_system.categories
WHERE NOT EXISTS (SELECT 1 FROM categories WHERE categories.id = zoom_learning_system.categories.id);

-- نقل المدفوعات من zoom_learning_system
INSERT IGNORE INTO payments (id, user_id, course_id, amount, currency, payment_method, transaction_id, status, paid_at, created_at, updated_at)
SELECT id, user_id, course_id, amount, currency, payment_method, transaction_id, status, paid_at, created_at, updated_at
FROM zoom_learning_system.payments
WHERE EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'zoom_learning_system' AND table_name = 'payments');
