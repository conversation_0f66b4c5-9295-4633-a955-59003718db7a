<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = trim($_POST['username']);
    $password = $_POST['password'];
    $selected_role = $_POST['role'];

    // تسجيل محاولة تسجيل الدخول
    error_log("Login attempt - Username: {$username}, Role: {$selected_role}");

    if (empty($username) || empty($password) || empty($selected_role)) {
        $error = 'جميع الحقول مطلوبة';
        error_log("Login error: Empty fields");
    } else {
        try {
            // التحقق من وجود المستخدم أولاً
            $stmt = $conn->prepare("SELECT * FROM users WHERE (username = ? OR email = ?)");
            $stmt->execute([$username, $username]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            
            error_log("User found: " . ($user ? 'Yes' : 'No'));
            if ($user) {
                error_log("User role: {$user['role']}, Status: {$user['status']}");
            }
            
            if (!$user) {
                $error = 'اسم المستخدم أو البريد الإلكتروني غير موجود';
                error_log("Login error: User not found");
            } elseif ($user['role'] !== $selected_role) {
                $error = 'نوع المستخدم غير صحيح';
                error_log("Login error: Invalid role - User role: {$user['role']}, Selected role: {$selected_role}");
            } elseif ($user['status'] === 'pending') {
                $error = 'حسابك قيد المراجعة. يرجى الانتظار حتى يتم تفعيل حسابك';
                error_log("Login error: Account pending");
            } elseif ($user['status'] === 'inactive') {
                $error = 'تم تعطيل حسابك. يرجى التواصل مع الإدارة';
                error_log("Login error: Account inactive");
            } elseif (!password_verify($password, $user['password'])) {
                $error = 'كلمة المرور غير صحيحة';
                error_log("Login error: Invalid password");
                logUserActivity($user['id'], 'محاولة تسجيل دخول فاشلة', 'كلمة المرور غير صحيحة');
            } else {
                // تسجيل الدخول بنجاح
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['username'] = $user['username'];
                $_SESSION['role'] = $user['role'];
                $_SESSION['name'] = $user['name'];

                logUserActivity($user['id'], 'تسجيل دخول', 'تم تسجيل الدخول بنجاح');

                // تحديث آخر تسجيل دخول
                $stmt = $conn->prepare("UPDATE users SET last_login = NOW() WHERE id = ?");
                $stmt->execute([$user['id']]);

                // توجيه المستخدم حسب دوره
                switch($user['role']) {
                    case 'admin':
                        header('Location: admin/dashboard.php');
                        break;
                    case 'instructor':
                        header('Location: instructor/dashboard.php');
                        break;
                    case 'student':
                        header('Location: dashboard.php');
                        break;
                    default:
                        $error = 'نوع المستخدم غير صالح';
                        break;
                }
                exit();
            }
        } catch (PDOException $e) {
            error_log("Login error: " . $e->getMessage());
            $error = 'حدث خطأ في النظام';
        }
    }
}

?>
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - نظام التعلم عن بعد</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .login-container {
            max-width: 400px;
            margin: 100px auto;
        }
        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        .card-header {
            background-color: #007bff;
            color: white;
            text-align: center;
            padding: 20px;
            border-radius: 10px 10px 0 0;
        }
        .btn-primary {
            background-color: #007bff;
            border-color: #007bff;
            padding: 10px;
        }
        .btn-primary:hover {
            background-color: #0056b3;
            border-color: #0056b3;
        }
        .form-control {
            padding: 12px;
        }
        .role-selector {
            margin-bottom: 20px;
        }
        .role-selector .btn {
            margin: 0 5px;
        }
        .role-selector .btn.active {
            background-color: #0056b3;
            border-color: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="login-container">
            <div class="card">
                <div class="card-header">
                    <h3 class="mb-0">تسجيل الدخول</h3>
                </div>
                <div class="card-body">
                    <?php if (!empty($error)): ?>
                        <div class="alert alert-danger" role="alert">
                            <?php echo htmlspecialchars($error); ?>
                        </div>
                    <?php endif; ?>

                    <form method="POST" action="">
                        <div class="role-selector btn-group btn-group-toggle w-100" data-toggle="buttons">
                            <label class="btn btn-outline-primary active">
                                <input type="radio" name="role" value="student" checked> طالب
                            </label>
                            <label class="btn btn-outline-primary">
                                <input type="radio" name="role" value="instructor"> مدرس
                            </label>
                            <label class="btn btn-outline-primary">
                                <input type="radio" name="role" value="admin"> مدير
                            </label>
                        </div>

                        <div class="form-group">
                            <label for="username">البريد الإلكتروني أو اسم المستخدم</label>
                            <input type="text" class="form-control" id="username" name="username" required
                                value="<?php echo isset($_POST['username']) ? htmlspecialchars($_POST['username']) : ''; ?>">
                        </div>

                        <div class="form-group">
                            <label for="password">كلمة المرور</label>
                            <input type="password" class="form-control" id="password" name="password" required>
                        </div>

                        <button type="submit" class="btn btn-primary btn-block">دخول</button>
                    </form>

                    <div class="text-center mt-3">
                        <a href="reset-password.php">نسيت كلمة المرور؟</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.5.4/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
</body>
</html>