<?php
session_start();

try {
    $conn = new PDO("mysql:host=localhost", "root", "");
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // حذف قاعدة البيانات إذا كانت موجودة
    $conn->exec("DROP DATABASE IF EXISTS zoom_db");
    
    // إنشاء قاعدة البيانات
    $conn->exec("CREATE DATABASE zoom_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    $conn->exec("USE zoom_db");
    
    // إنشاء جدول المستخدمين
    $conn->exec("CREATE TABLE users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        email VARCHAR(255) NOT NULL UNIQUE,
        username VARCHAR(50) NOT NULL UNIQUE,
        password VARCHAR(255) NOT NULL,
        phone VARCHAR(20),
        role ENUM('admin', 'instructor', 'student') NOT NULL,
        status ENUM('active', 'pending', 'inactive') DEFAULT 'active',
        failed_attempts INT DEFAULT 0,
        lock_expires DATETIME DEFAULT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        last_login TIMESTAMP NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");

    // إنشاء جدول التصنيفات
    $conn->exec("CREATE TABLE categories (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        slug VARCHAR(255) NOT NULL UNIQUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");

    // إضافة التصنيفات الأساسية
    $categories = [
        ['name' => 'أمن سيبراني', 'slug' => 'cybersecurity'],
        ['name' => 'شبكات', 'slug' => 'networks'],
        ['name' => 'برمجة', 'slug' => 'programming'],
        ['name' => 'قواعد بيانات', 'slug' => 'databases'],
        ['name' => 'ذكاء اصطناعي', 'slug' => 'ai']
    ];

    $stmt = $conn->prepare("INSERT IGNORE INTO categories (name, slug) VALUES (?, ?)");
    foreach ($categories as $category) {
        $stmt->execute([$category['name'], $category['slug']]);
    }

    // إنشاء جدول الدورات
    $conn->exec("CREATE TABLE courses (
        id INT AUTO_INCREMENT PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        description TEXT,
        instructor_id INT,
        start_date DATE NOT NULL,
        end_date DATE NOT NULL,
        max_students INT NOT NULL DEFAULT 30,
        image_path VARCHAR(255),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        status ENUM('active', 'inactive', 'completed') DEFAULT 'active',
        FOREIGN KEY (instructor_id) REFERENCES users(id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");

    // إنشاء جدول العلاقة بين الدورات والتصنيفات
    $conn->exec("CREATE TABLE course_categories (
        course_id INT,
        category_id INT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (course_id, category_id),
        FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
        FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");

    // إنشاء حساب المدير
    $adminPassword = password_hash('admin123', PASSWORD_DEFAULT);
    $conn->exec("INSERT INTO users (name, email, username, password, role, status) 
                VALUES ('Administrator', '<EMAIL>', 'admin', '$adminPassword', 'admin', 'active')");

    // إنشاء حساب مدرب للاختبار
    $instructorPassword = password_hash('instructor123', PASSWORD_DEFAULT);
    $conn->exec("INSERT INTO users (name, email, username, password, role, status) 
                VALUES ('مدرب تجريبي', '<EMAIL>', 'instructor', '$instructorPassword', 'instructor', 'active')");

    // إنشاء حساب طالب للاختبار
    $studentPassword = password_hash('student123', PASSWORD_DEFAULT);
    $conn->exec("INSERT INTO users (name, email, username, password, role, status) 
                VALUES ('طالب تجريبي', '<EMAIL>', 'student', '$studentPassword', 'student', 'active')");

    echo "<div class='alert alert-success'>";
    echo "تم إنشاء قاعدة البيانات وحسابات المستخدمين بنجاح!<br><br>";
    echo "<strong>حساب المدير:</strong><br>";
    echo "البريد الإلكتروني: <EMAIL><br>";
    echo "كلمة المرور: admin123<br><br>";
    echo "<strong>حساب المدرب:</strong><br>";
    echo "البريد الإلكتروني: <EMAIL><br>";
    echo "كلمة المرور: instructor123<br><br>";
    echo "<strong>حساب الطالب:</strong><br>";
    echo "البريد الإلكتروني: <EMAIL><br>";
    echo "كلمة المرور: student123<br>";
    echo "</div>";

} catch (PDOException $e) {
    echo "خطأ في قاعدة البيانات: " . $e->getMessage();
}
?>
