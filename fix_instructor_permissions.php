<?php
/**
 * إصلاح صلاحيات المدرب وإنشاء حساب مدرب تجريبي
 * Fix instructor permissions and create test instructor account
 */

require_once 'config/database.php';

echo "<!DOCTYPE html>";
echo "<html dir='rtl' lang='ar'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>إصلاح صلاحيات المدرب</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "</head>";
echo "<body>";
echo "<div class='container mt-4'>";
echo "<h2>🔧 إصلاح صلاحيات المدرب</h2>";

try {
    // 1. فحص المستخدمين الحاليين
    echo "<h4>👥 فحص المستخدمين الحاليين</h4>";
    
    $stmt = $conn->query("SELECT id, name, email, role, status FROM users ORDER BY role, name");
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<div class='table-responsive'>";
    echo "<table class='table table-striped'>";
    echo "<thead class='table-dark'>";
    echo "<tr><th>ID</th><th>الاسم</th><th>البريد</th><th>الدور</th><th>الحالة</th><th>الإجراءات</th></tr>";
    echo "</thead><tbody>";
    
    $instructors_count = 0;
    $active_instructors = 0;
    
    foreach ($users as $user) {
        $role_class = '';
        $role_text = '';
        switch($user['role']) {
            case 'admin':
                $role_class = 'danger';
                $role_text = 'مدير';
                break;
            case 'instructor':
                $role_class = 'primary';
                $role_text = 'مدرب';
                $instructors_count++;
                if ($user['status'] === 'active') $active_instructors++;
                break;
            case 'student':
                $role_class = 'success';
                $role_text = 'طالب';
                break;
        }
        
        $status_class = $user['status'] === 'active' ? 'success' : 'warning';
        
        echo "<tr>";
        echo "<td>{$user['id']}</td>";
        echo "<td>" . htmlspecialchars($user['name']) . "</td>";
        echo "<td>" . htmlspecialchars($user['email']) . "</td>";
        echo "<td><span class='badge bg-$role_class'>$role_text</span></td>";
        echo "<td><span class='badge bg-$status_class'>{$user['status']}</span></td>";
        echo "<td>";
        if ($user['role'] === 'instructor' && $user['status'] !== 'active') {
            echo "<form method='POST' style='display:inline;'>";
            echo "<input type='hidden' name='action' value='activate_instructor'>";
            echo "<input type='hidden' name='user_id' value='{$user['id']}'>";
            echo "<button type='submit' class='btn btn-success btn-sm'>تفعيل</button>";
            echo "</form>";
        }
        echo "</td>";
        echo "</tr>";
    }
    
    echo "</tbody></table>";
    echo "</div>";
    
    echo "<div class='alert alert-info'>";
    echo "<strong>الإحصائيات:</strong><br>";
    echo "إجمالي المدربين: $instructors_count<br>";
    echo "المدربين النشطين: $active_instructors";
    echo "</div>";

    // 2. معالجة طلبات التفعيل
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
        if ($_POST['action'] === 'activate_instructor') {
            $user_id = (int)$_POST['user_id'];
            $stmt = $conn->prepare("UPDATE users SET status = 'active' WHERE id = ? AND role = 'instructor'");
            $stmt->execute([$user_id]);
            echo "<div class='alert alert-success'>✅ تم تفعيل المدرب بنجاح</div>";
            echo "<script>setTimeout(function(){ location.reload(); }, 2000);</script>";
        }
    }

    // 3. إنشاء مدرب تجريبي إذا لم يوجد
    echo "<h4>👨‍🏫 إنشاء مدرب تجريبي</h4>";
    
    if ($active_instructors === 0) {
        // التحقق من وجود مدرب تجريبي
        $stmt = $conn->prepare("SELECT id FROM users WHERE email = '<EMAIL>'");
        $stmt->execute();
        $existing_instructor = $stmt->fetch();
        
        if (!$existing_instructor) {
            // إنشاء مدرب جديد
            $password = password_hash('instructor123', PASSWORD_DEFAULT);
            $stmt = $conn->prepare("INSERT INTO users (name, email, password, role, phone, status, created_at) VALUES (?, ?, ?, 'instructor', ?, 'active', NOW())");
            $stmt->execute(['محمد أحمد - مدرب', '<EMAIL>', $password, '0501234567']);
            
            echo "<div class='alert alert-success'>✅ تم إنشاء مدرب تجريبي جديد</div>";
        } else {
            // تفعيل المدرب الموجود
            $stmt = $conn->prepare("UPDATE users SET status = 'active' WHERE email = '<EMAIL>'");
            $stmt->execute();
            echo "<div class='alert alert-success'>✅ تم تفعيل المدرب التجريبي</div>";
        }
    } else {
        echo "<div class='alert alert-info'>ℹ️ يوجد $active_instructors مدرب نشط</div>";
    }

    // 4. إنشاء التصنيفات إذا لم تكن موجودة
    echo "<h4>📂 فحص التصنيفات</h4>";
    
    try {
        $stmt = $conn->query("SELECT COUNT(*) FROM categories");
        $categories_count = $stmt->fetchColumn();
        
        if ($categories_count === 0) {
            $categories = [
                'البرمجة وتطوير المواقع',
                'التصميم الجرافيكي', 
                'التسويق الرقمي',
                'إدارة الأعمال',
                'اللغات',
                'العلوم والرياضيات',
                'الفنون والإبداع'
            ];
            
            $stmt = $conn->prepare("INSERT INTO categories (name, status, created_at) VALUES (?, 'active', NOW())");
            foreach ($categories as $category) {
                $stmt->execute([$category]);
            }
            
            echo "<div class='alert alert-success'>✅ تم إنشاء " . count($categories) . " تصنيف</div>";
        } else {
            echo "<div class='alert alert-info'>ℹ️ يوجد $categories_count تصنيف</div>";
        }
    } catch (Exception $e) {
        // إنشاء جدول التصنيفات
        $conn->exec("
            CREATE TABLE IF NOT EXISTS categories (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                description TEXT,
                icon VARCHAR(100) DEFAULT NULL,
                color VARCHAR(7) DEFAULT '#007bff',
                status ENUM('active', 'inactive') DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                
                INDEX idx_name (name),
                INDEX idx_status (status)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        
        $categories = [
            'البرمجة وتطوير المواقع',
            'التصميم الجرافيكي', 
            'التسويق الرقمي',
            'إدارة الأعمال',
            'اللغات',
            'العلوم والرياضيات',
            'الفنون والإبداع'
        ];
        
        $stmt = $conn->prepare("INSERT INTO categories (name, status, created_at) VALUES (?, 'active', NOW())");
        foreach ($categories as $category) {
            $stmt->execute([$category]);
        }
        
        echo "<div class='alert alert-success'>✅ تم إنشاء جدول التصنيفات و " . count($categories) . " تصنيف</div>";
    }

    // 5. فحص أعمدة الدفع في جدول الكورسات
    echo "<h4>💰 فحص نظام الدفع</h4>";
    
    $stmt = $conn->query("SHOW COLUMNS FROM courses LIKE 'course_type'");
    if ($stmt->rowCount() === 0) {
        $conn->exec("ALTER TABLE courses ADD COLUMN course_type ENUM('free', 'paid') DEFAULT 'free' AFTER description");
        $conn->exec("ALTER TABLE courses ADD COLUMN price DECIMAL(10,2) DEFAULT 0.00 AFTER course_type");
        $conn->exec("ALTER TABLE courses ADD COLUMN currency VARCHAR(3) DEFAULT 'SAR' AFTER price");
        echo "<div class='alert alert-success'>✅ تم إضافة أعمدة نظام الدفع</div>";
    } else {
        echo "<div class='alert alert-info'>ℹ️ أعمدة نظام الدفع موجودة</div>";
    }

    // 6. إنشاء مجلد الصور
    echo "<h4>📁 فحص مجلد الصور</h4>";
    
    $upload_dir = 'uploads/courses';
    if (!file_exists($upload_dir)) {
        mkdir($upload_dir, 0777, true);
        echo "<div class='alert alert-success'>✅ تم إنشاء مجلد الصور: $upload_dir</div>";
    } else {
        echo "<div class='alert alert-info'>ℹ️ مجلد الصور موجود: $upload_dir</div>";
    }

    // 7. عرض بيانات تسجيل الدخول للمدرب
    echo "<h4>🔑 بيانات تسجيل الدخول للمدرب</h4>";
    
    $stmt = $conn->query("SELECT * FROM users WHERE role = 'instructor' AND status = 'active' LIMIT 1");
    $instructor = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($instructor) {
        echo "<div class='card'>";
        echo "<div class='card-header bg-primary text-white'>";
        echo "<h6 class='mb-0'>👨‍🏫 بيانات المدرب</h6>";
        echo "</div>";
        echo "<div class='card-body'>";
        echo "<div class='row'>";
        echo "<div class='col-md-6'>";
        echo "<p><strong>الاسم:</strong> " . htmlspecialchars($instructor['name']) . "</p>";
        echo "<p><strong>البريد الإلكتروني:</strong></p>";
        echo "<input type='text' class='form-control mb-2' value='" . htmlspecialchars($instructor['email']) . "' readonly onclick='this.select()'>";
        echo "</div>";
        echo "<div class='col-md-6'>";
        echo "<p><strong>كلمة المرور:</strong></p>";
        echo "<input type='text' class='form-control mb-2' value='instructor123' readonly onclick='this.select()'>";
        echo "<p><strong>الحالة:</strong> <span class='badge bg-success'>نشط</span></p>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
    }

    echo "<div class='alert alert-success mt-4'>";
    echo "<h5>🎉 تم إصلاح جميع المشاكل!</h5>";
    echo "<ul class='mb-0'>";
    echo "<li>✅ تم التحقق من المدربين وتفعيلهم</li>";
    echo "<li>✅ تم إنشاء التصنيفات</li>";
    echo "<li>✅ تم إضافة نظام الدفع</li>";
    echo "<li>✅ تم إنشاء مجلد الصور</li>";
    echo "<li>✅ المدرب جاهز لإضافة الكورسات</li>";
    echo "</ul>";
    echo "</div>";

} catch (Exception $e) {
    echo "<div class='alert alert-danger'>❌ خطأ: " . htmlspecialchars($e->getMessage()) . "</div>";
}

echo "<div class='text-center mt-4'>";
echo "<a href='login.php' class='btn btn-primary btn-lg me-2'>🔐 تسجيل الدخول كمدرب</a>";
echo "<a href='instructor/add-course.php' class='btn btn-success btn-lg me-2'>➕ إضافة كورس</a>";
echo "<a href='instructor/dashboard.php' class='btn btn-info btn-lg'>🏠 لوحة المدرب</a>";
echo "</div>";

echo "<div class='mt-4'>";
echo "<div class='alert alert-info'>";
echo "<h6>💡 خطوات تسجيل الدخول كمدرب:</h6>";
echo "<ol>";
echo "<li>اذهب لصفحة تسجيل الدخول</li>";
echo "<li>أدخل البريد: <code><EMAIL></code></li>";
echo "<li>أدخل كلمة المرور: <code>instructor123</code></li>";
echo "<li>اختر 'مدرب' كنوع المستخدم</li>";
echo "<li>اضغط تسجيل الدخول</li>";
echo "</ol>";
echo "</div>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>
