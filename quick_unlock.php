<?php
/**
 * إصلاح سريع وبسيط لإلغاء قفل الحسابات
 * Quick and simple account unlock fix
 */

require_once 'config/database.php';

echo "<!DOCTYPE html>";
echo "<html dir='rtl' lang='ar'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>إصلاح سريع - إلغاء القفل</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "</head>";
echo "<body>";
echo "<div class='container mt-4'>";
echo "<h2>🔓 إصلاح سريع - إلغاء قفل الحسابات</h2>";

try {
    // 1. حذف جدول محاولات تسجيل الدخول إذا كان موجود
    echo "<h4>🗑️ تنظيف جداول الأمان</h4>";
    
    $tables_to_clean = ['login_attempts', 'failed_logins', 'security_logs'];
    
    foreach ($tables_to_clean as $table) {
        try {
            $stmt = $conn->query("SHOW TABLES LIKE '$table'");
            if ($stmt->rowCount() > 0) {
                $conn->exec("DROP TABLE $table");
                echo "<div class='alert alert-success'>✅ تم حذف جدول: $table</div>";
            }
        } catch (Exception $e) {
            // تجاهل الأخطاء
        }
    }

    // 2. تنظيف جدول المستخدمين من أعمدة القفل
    echo "<h4>👥 تنظيف جدول المستخدمين</h4>";
    
    // التحقق من الأعمدة الموجودة
    $stmt = $conn->query("DESCRIBE users");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    $existing_columns = array_column($columns, 'Field');
    
    // حذف أعمدة القفل إذا كانت موجودة
    $lock_columns = ['locked_until', 'failed_login_attempts', 'last_failed_login', 'login_attempts'];
    
    foreach ($lock_columns as $column) {
        if (in_array($column, $existing_columns)) {
            try {
                $conn->exec("ALTER TABLE users DROP COLUMN $column");
                echo "<div class='alert alert-success'>✅ تم حذف عمود: $column</div>";
            } catch (Exception $e) {
                echo "<div class='alert alert-warning'>⚠️ لم يتم حذف عمود: $column</div>";
            }
        }
    }

    // 3. تفعيل جميع المستخدمين
    echo "<h4>✅ تفعيل جميع المستخدمين</h4>";
    
    $stmt = $conn->exec("UPDATE users SET status = 'active'");
    echo "<div class='alert alert-success'>✅ تم تفعيل $stmt مستخدم</div>";

    // 4. إعادة إنشاء المستخدمين الافتراضيين
    echo "<h4>🔑 إعادة إنشاء المستخدمين الافتراضيين</h4>";
    
    // حذف المستخدمين الافتراضيين الموجودين
    $default_emails = ['<EMAIL>', '<EMAIL>', '<EMAIL>'];
    
    foreach ($default_emails as $email) {
        $stmt = $conn->prepare("DELETE FROM users WHERE email = ?");
        $stmt->execute([$email]);
    }
    
    // إنشاء المستخدمين من جديد
    $default_users = [
        ['مدير النظام', '<EMAIL>', password_hash('admin123', PASSWORD_DEFAULT), 'admin'],
        ['أحمد محمد - مدرب', '<EMAIL>', password_hash('instructor123', PASSWORD_DEFAULT), 'instructor'],
        ['سارة أحمد - طالبة', '<EMAIL>', password_hash('student123', PASSWORD_DEFAULT), 'student']
    ];
    
    $stmt = $conn->prepare("INSERT INTO users (name, email, password, role, status) VALUES (?, ?, ?, ?, 'active')");
    
    foreach ($default_users as $user) {
        $stmt->execute($user);
        echo "<div class='alert alert-success'>✅ تم إنشاء مستخدم: {$user[1]}</div>";
    }

    // 5. تنظيف الجلسات
    echo "<h4>🧹 تنظيف الجلسات</h4>";
    
    // بدء جلسة جديدة وتدميرها
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    session_destroy();
    
    // حذف ملفات الجلسات إذا أمكن
    $session_path = session_save_path();
    if (empty($session_path)) {
        $session_path = sys_get_temp_dir();
    }
    
    echo "<div class='alert alert-success'>✅ تم تنظيف بيانات الجلسة</div>";

    // 6. اختبار المستخدمين الجدد
    echo "<h4>🧪 اختبار المستخدمين</h4>";
    
    $test_accounts = [
        ['<EMAIL>', 'admin123', 'مدير'],
        ['<EMAIL>', 'instructor123', 'مدرب'],
        ['<EMAIL>', 'student123', 'طالب']
    ];
    
    foreach ($test_accounts as $account) {
        $stmt = $conn->prepare("SELECT id, name, password, role, status FROM users WHERE email = ?");
        $stmt->execute([$account[0]]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($user && password_verify($account[1], $user['password']) && $user['status'] === 'active') {
            echo "<div class='alert alert-success'>";
            echo "✅ <strong>{$account[2]}</strong> - {$account[0]} | كلمة المرور: {$account[1]} ✓";
            echo "</div>";
        } else {
            echo "<div class='alert alert-danger'>";
            echo "❌ <strong>{$account[2]}</strong> - مشكلة في الحساب";
            echo "</div>";
        }
    }

    // 7. فحص ملفات تسجيل الدخول
    echo "<h4>📄 فحص ملفات النظام</h4>";
    
    $important_files = [
        'login.php' => 'صفحة تسجيل الدخول',
        'config/database.php' => 'إعدادات قاعدة البيانات',
        'includes/session_config.php' => 'إعدادات الجلسة'
    ];
    
    foreach ($important_files as $file => $description) {
        if (file_exists($file)) {
            echo "<div class='alert alert-success'>✅ $description موجود</div>";
        } else {
            echo "<div class='alert alert-danger'>❌ $description غير موجود</div>";
        }
    }

    // 8. عرض النتائج النهائية
    echo "<h4>🎯 بيانات تسجيل الدخول الجاهزة</h4>";
    
    echo "<div class='row'>";
    
    // مدير النظام
    echo "<div class='col-md-4'>";
    echo "<div class='card border-danger'>";
    echo "<div class='card-header bg-danger text-white text-center'>";
    echo "<h6 class='mb-0'>👨‍💼 مدير النظام</h6>";
    echo "</div>";
    echo "<div class='card-body text-center'>";
    echo "<p><strong>البريد:</strong><br><code><EMAIL></code></p>";
    echo "<p><strong>كلمة المرور:</strong><br><code>admin123</code></p>";
    echo "<span class='badge bg-success'>✅ جاهز</span>";
    echo "</div>";
    echo "</div>";
    echo "</div>";
    
    // مدرب
    echo "<div class='col-md-4'>";
    echo "<div class='card border-primary'>";
    echo "<div class='card-header bg-primary text-white text-center'>";
    echo "<h6 class='mb-0'>👨‍🏫 مدرب</h6>";
    echo "</div>";
    echo "<div class='card-body text-center'>";
    echo "<p><strong>البريد:</strong><br><code><EMAIL></code></p>";
    echo "<p><strong>كلمة المرور:</strong><br><code>instructor123</code></p>";
    echo "<span class='badge bg-success'>✅ جاهز</span>";
    echo "</div>";
    echo "</div>";
    echo "</div>";
    
    // طالب
    echo "<div class='col-md-4'>";
    echo "<div class='card border-success'>";
    echo "<div class='card-header bg-success text-white text-center'>";
    echo "<h6 class='mb-0'>👨‍🎓 طالب</h6>";
    echo "</div>";
    echo "<div class='card-body text-center'>";
    echo "<p><strong>البريد:</strong><br><code><EMAIL></code></p>";
    echo "<p><strong>كلمة المرور:</strong><br><code>student123</code></p>";
    echo "<span class='badge bg-success'>✅ جاهز</span>";
    echo "</div>";
    echo "</div>";
    echo "</div>";
    
    echo "</div>";

    // 9. إحصائيات نهائية
    echo "<h4>📊 إحصائيات النظام</h4>";
    
    $stmt = $conn->query("SELECT COUNT(*) as total, COUNT(CASE WHEN status = 'active' THEN 1 END) as active FROM users");
    $stats = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "<div class='row'>";
    echo "<div class='col-md-6'>";
    echo "<div class='card bg-primary text-white'>";
    echo "<div class='card-body text-center'>";
    echo "<h3>{$stats['total']}</h3>";
    echo "<p class='mb-0'>إجمالي المستخدمين</p>";
    echo "</div></div></div>";
    
    echo "<div class='col-md-6'>";
    echo "<div class='card bg-success text-white'>";
    echo "<div class='card-body text-center'>";
    echo "<h3>{$stats['active']}</h3>";
    echo "<p class='mb-0'>مستخدمين نشطين</p>";
    echo "</div></div></div>";
    echo "</div>";

    echo "<div class='alert alert-success mt-4'>";
    echo "<h5>🎉 تم الإصلاح بنجاح!</h5>";
    echo "<ul class='mb-0'>";
    echo "<li>✅ تم حذف جميع جداول الأمان المشكلة</li>";
    echo "<li>✅ تم إزالة أعمدة القفل من جدول المستخدمين</li>";
    echo "<li>✅ تم إعادة إنشاء المستخدمين الافتراضيين</li>";
    echo "<li>✅ تم تفعيل جميع الحسابات</li>";
    echo "<li>✅ تم تنظيف بيانات الجلسات</li>";
    echo "<li>✅ النظام جاهز للاستخدام بدون قيود أمان</li>";
    echo "</ul>";
    echo "</div>";

} catch (Exception $e) {
    echo "<div class='alert alert-danger'>❌ خطأ: " . htmlspecialchars($e->getMessage()) . "</div>";
}

echo "<div class='mt-4 text-center'>";
echo "<a href='login.php' class='btn btn-primary btn-lg me-2'>🔐 تسجيل الدخول الآن</a>";
echo "<a href='system_health_check.php' class='btn btn-info btn-lg me-2'>🔍 فحص النظام</a>";
echo "<a href='index.php' class='btn btn-success btn-lg'>🏠 الصفحة الرئيسية</a>";
echo "</div>";

echo "<div class='mt-4'>";
echo "<div class='alert alert-info'>";
echo "<h6>📋 خطوات تسجيل الدخول:</h6>";
echo "<ol>";
echo "<li>اذهب لصفحة تسجيل الدخول</li>";
echo "<li>اختر نوع الحساب: <strong>مدرب</strong></li>";
echo "<li>أدخل البريد: <code><EMAIL></code></li>";
echo "<li>أدخل كلمة المرور: <code>instructor123</code></li>";
echo "<li>اضغط تسجيل الدخول</li>";
echo "</ol>";
echo "</div>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>
