<?php
/**
 * إصلاح تضمين ملف activity_logger.php في جميع الملفات
 * Fix activity_logger.php includes in all files
 */

echo "<!DOCTYPE html>";
echo "<html dir='rtl' lang='ar'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>إصلاح تضمين activity_logger</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "</head>";
echo "<body>";
echo "<div class='container mt-4'>";
echo "<h2>🔧 إصلاح تضمين activity_logger.php</h2>";

// قائمة الملفات التي تحتاج إلى activity_logger.php
$files_to_fix = [
    'admin/manage_join_requests.php',
    'admin/activity-logs.php',
    'login.php',
    'logout.php'
];

$fixed_files = [];
$errors = [];

foreach ($files_to_fix as $file) {
    if (file_exists($file)) {
        $content = file_get_contents($file);
        
        // التحقق من وجود activity_logger.php
        if (strpos($content, "require_once '../includes/activity_logger.php'") === false && 
            strpos($content, "require_once 'includes/activity_logger.php'") === false) {
            
            // إضافة require_once للملف
            if (strpos($file, 'admin/') === 0) {
                // ملفات المدير
                $pattern = "/require_once '\.\.\/includes\/security\.php';/";
                $replacement = "require_once '../includes/security.php';\nrequire_once '../includes/activity_logger.php';";
            } else {
                // ملفات الجذر
                $pattern = "/require_once 'includes\/security\.php';/";
                $replacement = "require_once 'includes/security.php';\nrequire_once 'includes/activity_logger.php';";
            }
            
            $new_content = preg_replace($pattern, $replacement, $content);
            
            if ($new_content !== $content) {
                if (file_put_contents($file, $new_content)) {
                    $fixed_files[] = $file;
                    echo "<div class='alert alert-success'>✅ تم إصلاح: $file</div>";
                } else {
                    $errors[] = "فشل في كتابة: $file";
                    echo "<div class='alert alert-danger'>❌ فشل في إصلاح: $file</div>";
                }
            } else {
                echo "<div class='alert alert-warning'>⚠️ لم يتم العثور على النمط في: $file</div>";
            }
        } else {
            echo "<div class='alert alert-info'>ℹ️ الملف محدث مسبقاً: $file</div>";
        }
    } else {
        $errors[] = "الملف غير موجود: $file";
        echo "<div class='alert alert-warning'>⚠️ الملف غير موجود: $file</div>";
    }
}

// إنشاء ملف تجريبي لاختبار الدوال
echo "<h4>🧪 اختبار دوال activity_logger</h4>";

try {
    require_once 'config/database.php';
    require_once 'includes/activity_logger.php';
    
    // اختبار دالة logActivity
    $test_result = logActivity('اختبار النظام', 'اختبار دوال تسجيل الأنشطة', null, ['test' => true]);
    
    if ($test_result) {
        echo "<div class='alert alert-success'>✅ دالة logActivity تعمل بشكل صحيح</div>";
    } else {
        echo "<div class='alert alert-danger'>❌ دالة logActivity لا تعمل</div>";
    }
    
    // اختبار دالة logUserActivity
    $test_result2 = logUserActivity('اختبار المستخدم', 'اختبار دالة المستخدم');
    
    if ($test_result2) {
        echo "<div class='alert alert-success'>✅ دالة logUserActivity تعمل بشكل صحيح</div>";
    } else {
        echo "<div class='alert alert-danger'>❌ دالة logUserActivity لا تعمل</div>";
    }
    
    // عرض آخر 5 أنشطة
    $recent_activities = getRecentActivities(5);
    
    if (!empty($recent_activities)) {
        echo "<h5>📋 آخر الأنشطة:</h5>";
        echo "<div class='table-responsive'>";
        echo "<table class='table table-sm'>";
        echo "<thead><tr><th>النشاط</th><th>الوصف</th><th>التاريخ</th></tr></thead>";
        echo "<tbody>";
        foreach ($recent_activities as $activity) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($activity['action']) . "</td>";
            echo "<td>" . htmlspecialchars($activity['description']) . "</td>";
            echo "<td>" . date('Y-m-d H:i', strtotime($activity['created_at'])) . "</td>";
            echo "</tr>";
        }
        echo "</tbody></table>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>❌ خطأ في الاختبار: " . htmlspecialchars($e->getMessage()) . "</div>";
}

// ملخص النتائج
echo "<h4>📊 ملخص النتائج:</h4>";
echo "<div class='row'>";
echo "<div class='col-md-6'>";
echo "<div class='card bg-success text-white'>";
echo "<div class='card-body text-center'>";
echo "<h3>" . count($fixed_files) . "</h3>";
echo "<p>ملفات تم إصلاحها</p>";
echo "</div>";
echo "</div>";
echo "</div>";

echo "<div class='col-md-6'>";
echo "<div class='card bg-danger text-white'>";
echo "<div class='card-body text-center'>";
echo "<h3>" . count($errors) . "</h3>";
echo "<p>أخطاء</p>";
echo "</div>";
echo "</div>";
echo "</div>";
echo "</div>";

if (!empty($errors)) {
    echo "<h5>❌ الأخطاء:</h5>";
    echo "<ul class='list-group'>";
    foreach ($errors as $error) {
        echo "<li class='list-group-item list-group-item-danger'>$error</li>";
    }
    echo "</ul>";
}

echo "<div class='mt-4'>";
echo "<a href='instructor/add-course.php' class='btn btn-primary btn-lg me-2'>➕ إضافة كورس</a>";
echo "<a href='admin/manage-sessions.php' class='btn btn-success btn-lg me-2'>🎥 إدارة الجلسات</a>";
echo "<a href='admin/activity-logs.php' class='btn btn-info btn-lg me-2'>📋 سجل الأنشطة</a>";
echo "<a href='fix_database_structure.php' class='btn btn-warning btn-lg'>🔧 إصلاح قاعدة البيانات</a>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>
