<?php
/**
 * إصلاح شامل لصفحة تفاصيل الكورس
 * Complete fix for course details page
 */

require_once 'config/database.php';

echo "<!DOCTYPE html>";
echo "<html dir='rtl' lang='ar'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>إصلاح شامل لتفاصيل الكورس</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "</head>";
echo "<body>";
echo "<div class='container mt-4'>";
echo "<h2>🔧 إصلاح شامل لصفحة تفاصيل الكورس</h2>";

try {
    // 1. إنشاء جدول المراجعات إذا لم يكن موجود
    echo "<h4>⭐ إنشاء جدول المراجعات</h4>";
    
    $stmt = $conn->query("SHOW TABLES LIKE 'course_reviews'");
    if ($stmt->rowCount() == 0) {
        $conn->exec("
            CREATE TABLE course_reviews (
                id INT AUTO_INCREMENT PRIMARY KEY,
                course_id INT NOT NULL,
                student_id INT NOT NULL,
                rating INT NOT NULL CHECK (rating >= 1 AND rating <= 5),
                comment TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                
                INDEX idx_course_id (course_id),
                INDEX idx_student_id (student_id),
                INDEX idx_rating (rating),
                
                FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
                FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
                
                UNIQUE KEY unique_course_student (course_id, student_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        echo "<div class='alert alert-success'>✅ تم إنشاء جدول المراجعات</div>";
    } else {
        echo "<div class='alert alert-info'>ℹ️ جدول المراجعات موجود</div>";
    }

    // 2. إنشاء جدول المواد التعليمية إذا لم يكن موجود
    echo "<h4>📚 إنشاء جدول المواد التعليمية</h4>";
    
    $stmt = $conn->query("SHOW TABLES LIKE 'course_materials'");
    if ($stmt->rowCount() == 0) {
        $conn->exec("
            CREATE TABLE course_materials (
                id INT AUTO_INCREMENT PRIMARY KEY,
                course_id INT NOT NULL,
                title VARCHAR(255) NOT NULL,
                description TEXT,
                file_path VARCHAR(500) NOT NULL,
                file_type VARCHAR(50),
                file_size INT DEFAULT 0,
                status ENUM('active', 'inactive') DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                
                INDEX idx_course_id (course_id),
                INDEX idx_status (status),
                
                FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        echo "<div class='alert alert-success'>✅ تم إنشاء جدول المواد التعليمية</div>";
    } else {
        echo "<div class='alert alert-info'>ℹ️ جدول المواد التعليمية موجود</div>";
    }

    // 3. إضافة جلسات للكورس رقم 15
    echo "<h4>🎥 إضافة جلسات للكورس رقم 15</h4>";
    
    $course_id = 15;
    $stmt = $conn->prepare("SELECT COUNT(*) FROM sessions WHERE course_id = ?");
    $stmt->execute([$course_id]);
    $session_count = $stmt->fetchColumn();
    
    if ($session_count == 0) {
        $sample_sessions = [
            [
                'title' => 'الجلسة الأولى - مقدمة في البرمجة المتقدمة',
                'description' => 'نظرة عامة على مفاهيم البرمجة المتقدمة والأدوات المطلوبة',
                'date' => date('Y-m-d', strtotime('+1 week')),
                'start_time' => '19:00:00',
                'end_time' => '21:00:00'
            ],
            [
                'title' => 'الجلسة الثانية - هياكل البيانات المتقدمة',
                'description' => 'تعلم هياكل البيانات المعقدة وتطبيقاتها العملية',
                'date' => date('Y-m-d', strtotime('+2 weeks')),
                'start_time' => '19:00:00',
                'end_time' => '21:00:00'
            ],
            [
                'title' => 'الجلسة الثالثة - خوارزميات التحسين',
                'description' => 'تحسين الأداء وكتابة كود فعال ومحسن',
                'date' => date('Y-m-d', strtotime('+3 weeks')),
                'start_time' => '19:00:00',
                'end_time' => '21:00:00'
            ],
            [
                'title' => 'الجلسة الرابعة - مشروع تطبيقي',
                'description' => 'تطبيق عملي شامل لجميع المفاهيم المتعلمة',
                'date' => date('Y-m-d', strtotime('+4 weeks')),
                'start_time' => '19:00:00',
                'end_time' => '21:00:00'
            ]
        ];
        
        foreach ($sample_sessions as $session) {
            $stmt = $conn->prepare("
                INSERT INTO sessions (course_id, title, description, session_date, start_time, end_time, status, created_at)
                VALUES (?, ?, ?, ?, ?, ?, 'scheduled', NOW())
            ");
            $stmt->execute([
                $course_id,
                $session['title'],
                $session['description'],
                $session['date'],
                $session['start_time'],
                $session['end_time']
            ]);
        }
        
        echo "<div class='alert alert-success'>✅ تم إضافة " . count($sample_sessions) . " جلسات للكورس</div>";
    } else {
        echo "<div class='alert alert-info'>ℹ️ الكورس يحتوي على $session_count جلسة</div>";
    }

    // 4. إضافة مواد تعليمية للكورس
    echo "<h4>📄 إضافة مواد تعليمية للكورس</h4>";
    
    $stmt = $conn->prepare("SELECT COUNT(*) FROM course_materials WHERE course_id = ?");
    $stmt->execute([$course_id]);
    $materials_count = $stmt->fetchColumn();
    
    if ($materials_count == 0) {
        $sample_materials = [
            [
                'title' => 'كتاب البرمجة المتقدمة - الجزء الأول',
                'description' => 'مرجع شامل للمفاهيم الأساسية في البرمجة المتقدمة',
                'file_path' => 'uploads/materials/advanced_programming_part1.pdf'
            ],
            [
                'title' => 'أمثلة الكود المصدري',
                'description' => 'مجموعة من الأمثلة العملية والتطبيقات',
                'file_path' => 'uploads/materials/code_examples.zip'
            ],
            [
                'title' => 'شرائح العرض التقديمي',
                'description' => 'شرائح جميع الجلسات بصيغة PDF',
                'file_path' => 'uploads/materials/presentation_slides.pdf'
            ]
        ];
        
        foreach ($sample_materials as $material) {
            $stmt = $conn->prepare("
                INSERT INTO course_materials (course_id, title, description, file_path, status, created_at)
                VALUES (?, ?, ?, ?, 'active', NOW())
            ");
            $stmt->execute([
                $course_id,
                $material['title'],
                $material['description'],
                $material['file_path']
            ]);
        }
        
        echo "<div class='alert alert-success'>✅ تم إضافة " . count($sample_materials) . " مادة تعليمية</div>";
    } else {
        echo "<div class='alert alert-info'>ℹ️ الكورس يحتوي على $materials_count مادة تعليمية</div>";
    }

    // 5. إضافة مراجعات للكورس
    echo "<h4>💬 إضافة مراجعات للكورس</h4>";
    
    $stmt = $conn->prepare("SELECT COUNT(*) FROM course_reviews WHERE course_id = ?");
    $stmt->execute([$course_id]);
    $reviews_count = $stmt->fetchColumn();
    
    if ($reviews_count == 0) {
        // جلب طلاب للمراجعات
        $stmt = $conn->query("SELECT id, name FROM users WHERE role = 'student' LIMIT 5");
        $students = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($students)) {
            $sample_reviews = [
                ['rating' => 5, 'comment' => 'كورس ممتاز ومفيد جداً، المدرب محترف ويشرح بطريقة واضحة ومفهومة'],
                ['rating' => 4, 'comment' => 'محتوى جيد ومنظم، استفدت كثيراً من الأمثلة العملية'],
                ['rating' => 5, 'comment' => 'أفضل كورس أخذته في البرمجة، أنصح به بشدة'],
                ['rating' => 4, 'comment' => 'شرح ممتاز ومواد تعليمية مفيدة، لكن أتمنى المزيد من التمارين'],
                ['rating' => 5, 'comment' => 'كورس شامل ومتكامل، سأسجل في المزيد من كورسات هذا المدرب']
            ];
            
            foreach ($sample_reviews as $index => $review) {
                if (isset($students[$index])) {
                    $stmt = $conn->prepare("
                        INSERT INTO course_reviews (course_id, student_id, rating, comment, created_at)
                        VALUES (?, ?, ?, ?, NOW())
                        ON DUPLICATE KEY UPDATE rating = VALUES(rating), comment = VALUES(comment)
                    ");
                    $stmt->execute([
                        $course_id,
                        $students[$index]['id'],
                        $review['rating'],
                        $review['comment']
                    ]);
                }
            }
            
            echo "<div class='alert alert-success'>✅ تم إضافة " . count($sample_reviews) . " مراجعة للكورس</div>";
        } else {
            echo "<div class='alert alert-warning'>⚠️ لا يوجد طلاب لإضافة مراجعات</div>";
        }
    } else {
        echo "<div class='alert alert-info'>ℹ️ الكورس يحتوي على $reviews_count مراجعة</div>";
    }

    // 6. إضافة تسجيلات طلاب للكورس
    echo "<h4>👥 إضافة تسجيلات طلاب للكورس</h4>";
    
    $stmt = $conn->prepare("SELECT COUNT(*) FROM course_enrollments WHERE course_id = ? AND status = 'active'");
    $stmt->execute([$course_id]);
    $enrollments_count = $stmt->fetchColumn();
    
    if ($enrollments_count == 0) {
        $stmt = $conn->query("SELECT id FROM users WHERE role = 'student' LIMIT 8");
        $students = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        if (!empty($students)) {
            foreach ($students as $student_id) {
                $stmt = $conn->prepare("
                    INSERT INTO course_enrollments (course_id, student_id, enrollment_date, status)
                    VALUES (?, ?, NOW(), 'active')
                    ON DUPLICATE KEY UPDATE status = 'active'
                ");
                $stmt->execute([$course_id, $student_id]);
            }
            
            echo "<div class='alert alert-success'>✅ تم تسجيل " . count($students) . " طالب في الكورس</div>";
        }
    } else {
        echo "<div class='alert alert-info'>ℹ️ الكورس يحتوي على $enrollments_count طالب مسجل</div>";
    }

    // 7. اختبار الاستعلام الكامل
    echo "<h4>🧪 اختبار صفحة تفاصيل الكورس</h4>";
    
    try {
        $stmt = $conn->prepare("
            SELECT c.*, u.name as instructor_name, u.email as instructor_email, u.phone as instructor_phone,
                   (SELECT COUNT(*) FROM course_enrollments WHERE course_id = c.id AND status = 'active') as enrolled_students,
                   (SELECT COUNT(*) FROM sessions WHERE course_id = c.id) as total_sessions,
                   (SELECT AVG(rating) FROM course_reviews WHERE course_id = c.id) as avg_rating,
                   (SELECT COUNT(*) FROM course_reviews WHERE course_id = c.id) as total_reviews
            FROM courses c
            JOIN users u ON c.instructor_id = u.id
            WHERE c.id = ? AND c.status = 'active'
        ");
        $stmt->execute([$course_id]);
        $test_course = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($test_course) {
            echo "<div class='alert alert-success'>✅ استعلام صفحة التفاصيل يعمل بنجاح</div>";
            
            echo "<div class='card'>";
            echo "<div class='card-header'><h5>ملخص الكورس رقم $course_id</h5></div>";
            echo "<div class='card-body'>";
            echo "<div class='row'>";
            echo "<div class='col-md-6'>";
            echo "<p><strong>العنوان:</strong> " . htmlspecialchars($test_course['title']) . "</p>";
            echo "<p><strong>المدرب:</strong> " . htmlspecialchars($test_course['instructor_name']) . "</p>";
            echo "<p><strong>النوع:</strong> " . ($test_course['course_type'] === 'paid' ? 'مدفوع' : 'مجاني') . "</p>";
            echo "<p><strong>السعر:</strong> " . number_format($test_course['price'], 0) . " " . $test_course['currency'] . "</p>";
            echo "</div>";
            echo "<div class='col-md-6'>";
            echo "<p><strong>الطلاب:</strong> " . $test_course['enrolled_students'] . "</p>";
            echo "<p><strong>الجلسات:</strong> " . $test_course['total_sessions'] . "</p>";
            echo "<p><strong>التقييم:</strong> " . ($test_course['avg_rating'] ? number_format($test_course['avg_rating'], 1) . "/5" : 'لا يوجد') . "</p>";
            echo "<p><strong>المراجعات:</strong> " . $test_course['total_reviews'] . "</p>";
            echo "</div>";
            echo "</div>";
            echo "</div></div>";
            
        } else {
            echo "<div class='alert alert-danger'>❌ فشل في جلب بيانات الكورس</div>";
        }
        
    } catch (Exception $e) {
        echo "<div class='alert alert-danger'>❌ خطأ في الاستعلام: " . htmlspecialchars($e->getMessage()) . "</div>";
    }

    echo "<div class='alert alert-success mt-4'>";
    echo "<h5>🎉 تم إصلاح صفحة تفاصيل الكورس بنجاح!</h5>";
    echo "<ul class='mb-0'>";
    echo "<li>✅ تم إنشاء جداول المراجعات والمواد التعليمية</li>";
    echo "<li>✅ تم إضافة جلسات تفصيلية للكورس</li>";
    echo "<li>✅ تم إضافة مواد تعليمية متنوعة</li>";
    echo "<li>✅ تم إضافة مراجعات من الطلاب</li>";
    echo "<li>✅ تم تسجيل طلاب في الكورس</li>";
    echo "<li>✅ صفحة التفاصيل جاهزة للعرض</li>";
    echo "</ul>";
    echo "</div>";

} catch (Exception $e) {
    echo "<div class='alert alert-danger'>❌ خطأ: " . htmlspecialchars($e->getMessage()) . "</div>";
}

echo "<div class='text-center mt-4'>";
echo "<a href='course-details.php?id=15' class='btn btn-primary btn-lg me-2'>📚 عرض تفاصيل الكورس</a>";
echo "<a href='courses.php' class='btn btn-success btn-lg me-2'>📋 جميع الكورسات</a>";
echo "<a href='index.php' class='btn btn-info btn-lg'>🏠 الصفحة الرئيسية</a>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>
