<?php
/**
 * وصول سريع لحساب المدرس
 * Quick instructor access
 */

require_once 'config/database.php';

echo "<!DOCTYPE html>";
echo "<html dir='rtl' lang='ar'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>وصول سريع - حساب المدرس</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "</head>";
echo "<body>";
echo "<div class='container mt-4'>";
echo "<h2>👨‍🏫 وصول سريع لحساب المدرس</h2>";

try {
    // 1. حذف جداول الأمان المشكلة
    echo "<h4>🔧 إصلاح سريع</h4>";
    
    $problematic_tables = ['login_attempts', 'failed_logins'];
    foreach ($problematic_tables as $table) {
        try {
            $conn->exec("DROP TABLE IF EXISTS $table");
            echo "<div class='alert alert-success'>✅ تم حذف جدول: $table</div>";
        } catch (Exception $e) {
            // تجاهل
        }
    }

    // 2. إزالة أعمدة القفل من جدول المستخدمين
    $lock_columns = ['locked_until', 'failed_login_attempts'];
    foreach ($lock_columns as $column) {
        try {
            $conn->exec("ALTER TABLE users DROP COLUMN IF EXISTS $column");
        } catch (Exception $e) {
            // تجاهل
        }
    }

    // 3. إنشاء/تحديث حساب المدرس
    echo "<h4>👨‍🏫 إعداد حساب المدرس</h4>";
    
    $instructor_password = password_hash('instructor123', PASSWORD_DEFAULT);
    
    // حذف المدرس الموجود
    $stmt = $conn->prepare("DELETE FROM users WHERE email = '<EMAIL>'");
    $stmt->execute();
    
    // إنشاء المدرس من جديد
    $stmt = $conn->prepare("INSERT INTO users (name, email, password, role, status) VALUES (?, ?, ?, ?, ?)");
    $stmt->execute(['أحمد محمد - مدرب', '<EMAIL>', $instructor_password, 'instructor', 'active']);
    
    echo "<div class='alert alert-success'>✅ تم إنشاء حساب المدرس بنجاح</div>";

    // 4. اختبار الحساب
    echo "<h4>🧪 اختبار الحساب</h4>";
    
    $stmt = $conn->prepare("SELECT * FROM users WHERE email = '<EMAIL>'");
    $stmt->execute();
    $instructor = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($instructor && password_verify('instructor123', $instructor['password'])) {
        echo "<div class='alert alert-success'>✅ حساب المدرس جاهز ويعمل بشكل صحيح</div>";
    } else {
        echo "<div class='alert alert-danger'>❌ مشكلة في حساب المدرس</div>";
    }

    // 5. عرض بيانات تسجيل الدخول
    echo "<h4>🔑 بيانات تسجيل الدخول</h4>";
    
    echo "<div class='card border-primary'>";
    echo "<div class='card-header bg-primary text-white text-center'>";
    echo "<h5 class='mb-0'>👨‍🏫 حساب المدرس</h5>";
    echo "</div>";
    echo "<div class='card-body text-center'>";
    echo "<div class='row'>";
    echo "<div class='col-md-6'>";
    echo "<p><strong>البريد الإلكتروني:</strong></p>";
    echo "<h5><code><EMAIL></code></h5>";
    echo "</div>";
    echo "<div class='col-md-6'>";
    echo "<p><strong>كلمة المرور:</strong></p>";
    echo "<h5><code>instructor123</code></h5>";
    echo "</div>";
    echo "</div>";
    echo "<hr>";
    echo "<p class='text-success'><strong>✅ الحساب جاهز للاستخدام</strong></p>";
    echo "</div>";
    echo "</div>";

    // 6. خطوات تسجيل الدخول
    echo "<h4>📋 خطوات تسجيل الدخول</h4>";
    
    echo "<div class='alert alert-info'>";
    echo "<ol class='mb-0'>";
    echo "<li>اضغط على زر <strong>\"تسجيل الدخول الآن\"</strong> أدناه</li>";
    echo "<li>في صفحة تسجيل الدخول، اختر <strong>\"مدرب\"</strong></li>";
    echo "<li>أدخل البريد: <code><EMAIL></code></li>";
    echo "<li>أدخل كلمة المرور: <code>instructor123</code></li>";
    echo "<li>اضغط <strong>\"تسجيل الدخول\"</strong></li>";
    echo "</ol>";
    echo "</div>";

    echo "<div class='alert alert-success'>";
    echo "<h5>🎉 تم الإصلاح بنجاح!</h5>";
    echo "<p class='mb-0'>حساب المدرس جاهز ولن تواجه مشكلة القفل مرة أخرى.</p>";
    echo "</div>";

} catch (Exception $e) {
    echo "<div class='alert alert-danger'>❌ خطأ: " . htmlspecialchars($e->getMessage()) . "</div>";
}

echo "<div class='text-center mt-4'>";
echo "<a href='login.php' class='btn btn-primary btn-lg me-2'>🔐 تسجيل الدخول الآن</a>";
echo "<a href='instructor/dashboard.php' class='btn btn-success btn-lg'>📊 لوحة تحكم المدرس</a>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>
