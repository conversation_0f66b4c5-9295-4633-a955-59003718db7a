<?php
require_once 'config/database.php';

try {
    // Update the users table structure
    $conn->exec("ALTER TABLE users MODIFY COLUMN role ENUM('admin', 'instructor', 'student') DEFAULT 'student'");
    $conn->exec("ALTER TABLE users MODIFY COLUMN status ENUM('pending', 'approved', 'active') DEFAULT 'pending'");
    
    // Update existing admin users to have 'active' status
    $conn->exec("UPDATE users SET status = 'active' WHERE role = 'admin'");
    
    // Add username column if it doesn't exist
    $conn->exec("ALTER TABLE users ADD COLUMN IF NOT EXISTS username VARCHAR(50) UNIQUE AFTER email");
    
    // Add failed_attempts and lock_expires columns if they don't exist
    $conn->exec("ALTER TABLE users ADD COLUMN IF NOT EXISTS failed_attempts INT DEFAULT 0 AFTER status");
    $conn->exec("ALTER TABLE users ADD COLUMN IF NOT EXISTS lock_expires DATETIME NULL AFTER failed_attempts");
    
    echo "✅ تم تحديث هيكل قاعدة البيانات بنجاح\n";
    echo "✅ تم تحديث حالة المستخدمين\n";
    
} catch(PDOException $e) {
    die("خطأ: " . $e->getMessage());
}
?>