<?php
require_once '../config/database.php';
require_once '../includes/functions.php';
$pageTitle = 'لوحة التحكم';
require_once '../includes/header.php';
if (!isLoggedIn() || !isAdmin()) {
    redirect('../login.php');
}

if (isset($_GET['id'])) {
    $studentId = (int)$_GET['id'];
    
    try {
        // Get student information
        $stmt = $conn->prepare("SELECT name, email, phone FROM users WHERE id = ? AND role = 'student' AND status = 'pending'");
        $stmt->execute([$studentId]);
        $student = $stmt->fetch();
        
        if ($student) {
            // Update student status
            $stmt = $conn->prepare("UPDATE users SET status = 'approved' WHERE id = ?");
            $stmt->execute([$studentId]);
            
            // Create Zoom meeting (this is a placeholder - you'll need to implement the actual Zoom API integration)
            $meetingLink = "https://zoom.us/j/placeholder";
            
            // Create session
            $stmt = $conn->prepare("INSERT INTO sessions (topic, start_time, zoom_link, created_by) VALUES (?, ?, ?, ?)");
            $stmt->execute([
                'جلسة تعريفية',
                date('Y-m-d H:i:s', strtotime('+1 day')),
                $meetingLink,
                $_SESSION['user_id']
            ]);
            
            if ($stmt->execute()) {
                // Log approval instead of sending email
                error_log("Student {$student['name']} has been approved");
                
                // Redirect using JavaScript
                echo '<script>window.location.href = "students.php";</script>';
                exit;
            }
            
            // Send email notification
            $emailMessage = "
                <h3>مرحباً {$student['name']}</h3>
                <p>تم قبول طلب التسجيل الخاص بك في نظام التعلم عن بعد.</p>
                <p>رابط الجلسة التعريفية: <a href='$meetingLink'>اضغط هنا</a></p>
            ";
            sendEmail($student['email'], 'تم قبول طلب التسجيل', $emailMessage);
            
            // Generate WhatsApp message
            $whatsappMessage = "مرحباً {$student['name']}, تم قبول طلب التسجيل الخاص بك. رابط الجلسة: $meetingLink";
            $whatsappLink = generateWhatsAppLink($student['phone'], $whatsappMessage);
            
            // Save notification
            $stmt = $conn->prepare("INSERT INTO notifications (user_id, type, content) VALUES (?, 'email', ?)");
            $stmt->execute([$studentId, $emailMessage]);
            
            $_SESSION['success'] = 'تم قبول الطالب بنجاح وإرسال الإشعارات';
        }
    } catch(PDOException $e) {
        $_SESSION['error'] = 'حدث خطأ أثناء معالجة الطلب';
    }
}

redirect('dashboard.php');
?>
