<?php
require_once 'config/database.php';

try {
    echo "<h2>إصلاح جدول طلبات الانضمام</h2>";
    
    // التحقق من وجود الجدول
    $stmt = $conn->query("SHOW TABLES LIKE 'course_join_requests'");
    if ($stmt->rowCount() == 0) {
        echo "<p>إنشاء جدول course_join_requests...</p>";
        
        $conn->exec("
            CREATE TABLE course_join_requests (
                id INT AUTO_INCREMENT PRIMARY KEY,
                course_id INT NOT NULL,
                student_id INT NOT NULL,
                message TEXT DEFAULT NULL,
                status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
                requested_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                processed_at TIMESTAMP NULL,
                processed_by INT DEFAULT NULL,
                notes TEXT DEFAULT NULL,
                INDEX idx_course_id (course_id),
                INDEX idx_student_id (student_id),
                INDEX idx_status (status),
                UNIQUE KEY unique_request (course_id, student_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        
        echo "<div class='alert alert-success'>✅ تم إنشاء جدول course_join_requests بنجاح</div>";
    } else {
        echo "<div class='alert alert-info'>ℹ️ جدول course_join_requests موجود بالفعل</div>";
    }
    
    // إضافة بيانات تجريبية
    echo "<h3>إضافة بيانات تجريبية</h3>";
    
    // جلب معرف مدرب وطالب وكورس
    $stmt = $conn->query("SELECT id FROM users WHERE role = 'instructor' LIMIT 1");
    $instructor = $stmt->fetch();
    
    $stmt = $conn->query("SELECT id FROM users WHERE role = 'student' LIMIT 1");
    $student = $stmt->fetch();
    
    if ($instructor && $student) {
        $stmt = $conn->prepare("SELECT id FROM courses WHERE instructor_id = ? LIMIT 1");
        $stmt->execute([$instructor['id']]);
        $course = $stmt->fetch();
        
        if ($course) {
            // إضافة طلب انضمام تجريبي
            $stmt = $conn->prepare("
                INSERT IGNORE INTO course_join_requests (course_id, student_id, message, status)
                VALUES (?, ?, ?, 'pending')
            ");
            $stmt->execute([
                $course['id'], 
                $student['id'], 
                'أرغب في الانضمام لهذا الكورس لتطوير مهاراتي'
            ]);
            
            if ($stmt->rowCount() > 0) {
                echo "<div class='alert alert-success'>✅ تم إضافة طلب انضمام تجريبي</div>";
            } else {
                echo "<div class='alert alert-info'>ℹ️ طلب الانضمام موجود بالفعل</div>";
            }
        } else {
            echo "<div class='alert alert-warning'>⚠️ لا توجد كورسات للمدرب</div>";
        }
    } else {
        echo "<div class='alert alert-warning'>⚠️ لا توجد بيانات مدربين أو طلاب</div>";
    }
    
    // عرض البيانات الحالية
    echo "<h3>البيانات الحالية</h3>";
    $stmt = $conn->query("
        SELECT jr.*, u.name as student_name, c.title as course_title
        FROM course_join_requests jr
        LEFT JOIN users u ON jr.student_id = u.id
        LEFT JOIN courses c ON jr.course_id = c.id
        ORDER BY jr.requested_at DESC
        LIMIT 10
    ");
    $requests = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($requests)) {
        echo "<p>لا توجد طلبات انضمام</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>الطالب</th><th>الكورس</th><th>الرسالة</th><th>الحالة</th><th>التاريخ</th></tr>";
        
        foreach ($requests as $request) {
            echo "<tr>";
            echo "<td>" . $request['id'] . "</td>";
            echo "<td>" . htmlspecialchars($request['student_name']) . "</td>";
            echo "<td>" . htmlspecialchars($request['course_title']) . "</td>";
            echo "<td>" . htmlspecialchars(substr($request['message'] ?? '', 0, 50)) . "</td>";
            echo "<td>" . $request['status'] . "</td>";
            echo "<td>" . $request['requested_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
} catch (PDOException $e) {
    echo "<div class='alert alert-danger'>❌ خطأ: " . $e->getMessage() . "</div>";
}
?>

<style>
.alert {
    padding: 10px;
    margin: 10px 0;
    border-radius: 5px;
}
.alert-success { background-color: #d4edda; color: #155724; }
.alert-info { background-color: #d1ecf1; color: #0c5460; }
.alert-warning { background-color: #fff3cd; color: #856404; }
.alert-danger { background-color: #f8d7da; color: #721c24; }
</style>
