<?php
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/mail.php';

if (!isLoggedIn() || !isAdmin()) {
    redirect('../login.php');
}

if (isset($_GET['id'])) {
    $studentId = (int)$_GET['id'];
    
    try {
        // Get student information
        $stmt = $conn->prepare("SELECT name, email FROM users WHERE id = ? AND role = 'student' AND status = 'pending'");
        $stmt->execute([$studentId]);
        $student = $stmt->fetch();
        
        if ($student) {
            // Delete the student
            $stmt = $conn->prepare("DELETE FROM users WHERE id = ?");
            $stmt->execute([$studentId]);
            
            // Send rejection email
            $emailBody = "
                <h3>مرحباً {$student['name']}</h3>
                <p>نأسف لإبلاغك أنه تم رفض طلب التسجيل الخاص بك في نظام التعلم عن بعد.</p>
                <p>يمكنك التواصل مع الإدارة لمزيد من المعلومات.</p>
            ";
            sendEmailWithPHPMailer($student['email'], 'تم رفض طلب التسجيل', $emailBody);
            
            $_SESSION['success'] = 'تم رفض الطالب وإرسال إشعار البريد الإلكتروني';
        }
    } catch(PDOException $e) {
        $_SESSION['error'] = 'حدث خطأ أثناء معالجة الطلب';
    }
}
$pageTitle = 'لوحة التحكم';
require_once '../includes/header.php';
redirect('students.php');

?>
