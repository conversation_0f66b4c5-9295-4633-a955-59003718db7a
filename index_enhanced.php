<?php
session_start();
require_once 'config/database.php';
require_once 'config/config.php';
require_once 'includes/functions.php';
require_once 'includes/search_system.php';
require_once 'includes/security.php';

// تطبيق إعدادات الأمان
applySecurityHeaders();

// إحصائيات عامة محسنة
try {
    $stats = [
        'total_courses' => getTotalCourses(),
        'total_students' => getTotalStudents(),
        'total_instructors' => getTotalInstructors(),
        'total_hours' => getTotalHours(),
        'total_videos' => getTotalVideos(),
        'total_certificates' => getTotalCertificates()
    ];

    // أحدث الكورسات مع معلومات إضافية
    $stmt = $conn->prepare("
        SELECT c.*, u.name as instructor_name, u.email as instructor_email,
               cat.name as category_name,
               (SELECT COUNT(*) FROM course_enrollments WHERE course_id = c.id AND status = 'active') as enrolled_students,
               (SELECT COUNT(*) FROM course_videos WHERE course_id = c.id) as total_videos,
               (SELECT AVG(rating) FROM course_reviews WHERE course_id = c.id) as avg_rating,
               (SELECT COUNT(*) FROM course_reviews WHERE course_id = c.id) as total_reviews
        FROM courses c
        LEFT JOIN users u ON c.instructor_id = u.id
        LEFT JOIN categories cat ON c.category_id = cat.id
        WHERE c.status = 'active'
        ORDER BY c.created_at DESC
        LIMIT 8
    ");
    $stmt->execute();
    $latest_courses = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // الكورسات المميزة
    $stmt = $conn->prepare("
        SELECT c.*, u.name as instructor_name,
               cat.name as category_name,
               (SELECT COUNT(*) FROM course_enrollments WHERE course_id = c.id AND status = 'active') as enrolled_students,
               (SELECT AVG(rating) FROM course_reviews WHERE course_id = c.id) as avg_rating
        FROM courses c
        LEFT JOIN users u ON c.instructor_id = u.id
        LEFT JOIN categories cat ON c.category_id = cat.id
        WHERE c.status = 'active' AND c.featured = 1
        ORDER BY c.rating DESC, c.total_students DESC
        LIMIT 6
    ");
    $stmt->execute();
    $featured_courses = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // أفضل المدربين
    $stmt = $conn->prepare("
        SELECT u.*, 
               (SELECT COUNT(*) FROM courses WHERE instructor_id = u.id AND status = 'active') as total_courses,
               (SELECT COUNT(*) FROM course_enrollments ce 
                JOIN courses c ON ce.course_id = c.id 
                WHERE c.instructor_id = u.id AND ce.status = 'active') as total_students,
               (SELECT AVG(cr.rating) FROM course_reviews cr 
                JOIN courses c ON cr.course_id = c.id 
                WHERE c.instructor_id = u.id) as avg_rating
        FROM users u
        WHERE u.role = 'instructor' AND u.status = 'active'
        HAVING total_courses > 0
        ORDER BY avg_rating DESC, total_students DESC
        LIMIT 6
    ");
    $stmt->execute();
    $top_instructors = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // التصنيفات الرئيسية
    $stmt = $conn->prepare("
        SELECT cat.*, 
               (SELECT COUNT(*) FROM courses WHERE category_id = cat.id AND status = 'active') as courses_count
        FROM categories cat
        WHERE cat.is_active = 1
        HAVING courses_count > 0
        ORDER BY courses_count DESC
        LIMIT 8
    ");
    $stmt->execute();
    $main_categories = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // شهادات الطلاب (تقييمات)
    $stmt = $conn->prepare("
        SELECT cr.*, u.name as student_name, c.title as course_title
        FROM course_reviews cr
        JOIN users u ON cr.user_id = u.id
        JOIN courses c ON cr.course_id = c.id
        WHERE cr.is_approved = 1 AND cr.rating >= 4
        ORDER BY cr.created_at DESC
        LIMIT 6
    ");
    $stmt->execute();
    $student_testimonials = $stmt->fetchAll(PDO::FETCH_ASSOC);

} catch (PDOException $e) {
    error_log("Database error in index: " . $e->getMessage());
    $stats = ['total_courses' => 0, 'total_students' => 0, 'total_instructors' => 0, 'total_hours' => 0, 'total_videos' => 0, 'total_certificates' => 0];
    $latest_courses = [];
    $featured_courses = [];
    $top_instructors = [];
    $main_categories = [];
    $student_testimonials = [];
}

// معلومات SEO
$page_title = "منصة التعلم الإلكتروني - " . SITE_NAME;
$page_description = "منصة تعليمية متقدمة تقدم كورسات عالية الجودة في مختلف المجالات مع مدربين خبراء ونظام تعلم تفاعلي";
$page_keywords = "تعلم إلكتروني, كورسات أونلاين, تدريب, شهادات معتمدة, مدربين خبراء";

// دوال مساعدة
function getTotalCourses() {
    global $conn;
    try {
        $stmt = $conn->query("SELECT COUNT(*) FROM courses WHERE status = 'active'");
        return $stmt->fetchColumn();
    } catch (PDOException $e) {
        return 0;
    }
}

function getTotalStudents() {
    global $conn;
    try {
        $stmt = $conn->query("SELECT COUNT(*) FROM users WHERE role = 'student' AND status = 'active'");
        return $stmt->fetchColumn();
    } catch (PDOException $e) {
        return 0;
    }
}

function getTotalInstructors() {
    global $conn;
    try {
        $stmt = $conn->query("SELECT COUNT(*) FROM users WHERE role = 'instructor' AND status = 'active'");
        return $stmt->fetchColumn();
    } catch (PDOException $e) {
        return 0;
    }
}

function getTotalHours() {
    global $conn;
    try {
        $stmt = $conn->query("SELECT SUM(duration_hours) FROM courses WHERE status = 'active'");
        return $stmt->fetchColumn() ?: 0;
    } catch (PDOException $e) {
        return 0;
    }
}

function getTotalVideos() {
    global $conn;
    try {
        $stmt = $conn->query("SELECT COUNT(*) FROM course_videos cv JOIN courses c ON cv.course_id = c.id WHERE c.status = 'active'");
        return $stmt->fetchColumn();
    } catch (PDOException $e) {
        return 0;
    }
}

function getTotalCertificates() {
    global $conn;
    try {
        $stmt = $conn->query("SELECT COUNT(*) FROM certificates WHERE is_verified = 1");
        return $stmt->fetchColumn();
    } catch (PDOException $e) {
        return 0;
    }
}

function formatPrice($price, $currency = 'USD') {
    if ($price == 0) {
        return 'مجاني';
    }
    
    $symbols = [
        'USD' => '$',
        'EUR' => '€',
        'SAR' => 'ر.س',
        'AED' => 'د.إ'
    ];
    
    $symbol = $symbols[$currency] ?? '$';
    return $symbol . number_format($price, 2);
}

function getStarRating($rating) {
    $rating = (float)$rating;
    $fullStars = floor($rating);
    $halfStar = ($rating - $fullStars) >= 0.5 ? 1 : 0;
    $emptyStars = 5 - $fullStars - $halfStar;
    
    $html = '';
    
    // نجوم ممتلئة
    for ($i = 0; $i < $fullStars; $i++) {
        $html .= '<i class="fas fa-star text-warning"></i>';
    }
    
    // نجمة نصف ممتلئة
    if ($halfStar) {
        $html .= '<i class="fas fa-star-half-alt text-warning"></i>';
    }
    
    // نجوم فارغة
    for ($i = 0; $i < $emptyStars; $i++) {
        $html .= '<i class="far fa-star text-warning"></i>';
    }
    
    return $html;
}

function timeAgo($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) return 'الآن';
    if ($time < 3600) return floor($time/60) . ' دقيقة';
    if ($time < 86400) return floor($time/3600) . ' ساعة';
    if ($time < 2592000) return floor($time/86400) . ' يوم';
    if ($time < 31536000) return floor($time/2592000) . ' شهر';
    
    return floor($time/31536000) . ' سنة';
}
?>

<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="<?php echo $page_description; ?>">
    <meta name="keywords" content="<?php echo $page_keywords; ?>">
    <meta name="author" content="<?php echo SITE_AUTHOR; ?>">
    <meta name="robots" content="index, follow">
    <meta name="language" content="Arabic">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="<?php echo $page_title; ?>">
    <meta property="og:description" content="<?php echo $page_description; ?>">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?php echo SITE_URL; ?>">
    <meta property="og:image" content="<?php echo SITE_URL; ?>/assets/images/og-image.jpg">
    <meta property="og:site_name" content="<?php echo SITE_NAME; ?>">
    <meta property="og:locale" content="ar_SA">
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="<?php echo $page_title; ?>">
    <meta name="twitter:description" content="<?php echo $page_description; ?>">
    <meta name="twitter:image" content="<?php echo SITE_URL; ?>/assets/images/twitter-card.jpg">
    
    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="<?php echo PWA_THEME_COLOR; ?>">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="<?php echo PWA_SHORT_NAME; ?>">
    
    <title><?php echo $page_title; ?></title>
    
    <!-- Preload Critical Resources -->
    <link rel="preload" href="assets/css/main.css" as="style">
    <link rel="preload" href="assets/css/responsive.css" as="style">
    <link rel="preload" href="assets/js/main.js" as="script">
    <link rel="preload" href="assets/js/responsive.js" as="script">
    
    <!-- DNS Prefetch -->
    <link rel="dns-prefetch" href="//fonts.googleapis.com">
    <link rel="dns-prefetch" href="//fonts.gstatic.com">
    <link rel="dns-prefetch" href="//cdn.jsdelivr.net">
    <link rel="dns-prefetch" href="//cdnjs.cloudflare.com">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- CSS Libraries -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-T3c6CoIi6uLrA9TneNEoa7RxnatzjcDSCmG1MXxSR1GAsXEV/Dwwykc2MPK8M2HN" crossorigin="anonymous">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous">
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="assets/css/main.css" rel="stylesheet">
    <link href="assets/css/responsive.css" rel="stylesheet">
    
    <!-- Favicon and Icons -->
    <link rel="icon" type="image/x-icon" href="<?php echo SITE_FAVICON; ?>">
    <link rel="apple-touch-icon" sizes="180x180" href="assets/images/icons/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="assets/images/icons/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="assets/images/icons/favicon-16x16.png">
    <link rel="manifest" href="manifest.json">
    
    <!-- Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "EducationalOrganization",
        "name": "<?php echo SITE_NAME; ?>",
        "description": "<?php echo $page_description; ?>",
        "url": "<?php echo SITE_URL; ?>",
        "logo": "<?php echo SITE_URL . SITE_LOGO; ?>",
        "sameAs": [
            "https://facebook.com/zoomlearning",
            "https://twitter.com/zoomlearning",
            "https://linkedin.com/company/zoomlearning"
        ],
        "address": {
            "@type": "PostalAddress",
            "addressCountry": "SA"
        },
        "contactPoint": {
            "@type": "ContactPoint",
            "telephone": "+966-50-000-0000",
            "contactType": "customer service",
            "availableLanguage": ["Arabic", "English"]
        }
    }
    </script>
</head>
<body class="homepage">
    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-content">
            <div class="loading-logo">
                <i class="fas fa-graduation-cap"></i>
            </div>
            <div class="loading-text">جاري التحميل...</div>
            <div class="loading-progress">
                <div class="loading-bar"></div>
            </div>
        </div>
    </div>

    <!-- Skip to Content (Accessibility) -->
    <a href="#main-content" class="skip-to-content">انتقل إلى المحتوى الرئيسي</a>

    <!-- Header -->
    <header class="header-responsive" id="main-header">
        <div class="container-fluid">
            <nav class="navbar navbar-expand-lg navbar-dark">
                <a class="logo-responsive" href="index.php" aria-label="الصفحة الرئيسية">
                    <img src="<?php echo SITE_LOGO; ?>" alt="<?php echo SITE_NAME; ?>" loading="lazy">
                    <span><?php echo SITE_NAME; ?></span>
                </a>

                <button class="navbar-toggler sidebar-toggle" type="button" aria-label="فتح القائمة" aria-expanded="false">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <div class="navbar-nav ms-auto d-none d-lg-flex">
                    <a href="#home" class="nav-link">الرئيسية</a>
                    <a href="#courses" class="nav-link">الكورسات</a>
                    <a href="#instructors" class="nav-link">المدربين</a>
                    <a href="#about" class="nav-link">من نحن</a>
                    <a href="#contact" class="nav-link">اتصل بنا</a>
                </div>

                <div class="header-actions d-none d-lg-flex">
                    <a href="login.php" class="btn-responsive btn-secondary-responsive">
                        <i class="fas fa-sign-in-alt"></i>
                        <span>تسجيل الدخول</span>
                    </a>
                    <a href="register.php" class="btn-responsive btn-primary-responsive">
                        <i class="fas fa-user-plus"></i>
                        <span>إنشاء حساب</span>
                    </a>
                </div>
            </nav>
        </div>
    </header>

    <!-- Sidebar for Mobile -->
    <div class="sidebar-overlay"></div>
    <aside class="sidebar-responsive" id="mobile-sidebar">
        <div class="sidebar-header">
            <h5>القائمة</h5>
            <button class="btn-close" aria-label="إغلاق القائمة"></button>
        </div>
        <nav class="sidebar-nav">
            <a href="#home" class="sidebar-link">
                <i class="fas fa-home"></i>
                <span>الرئيسية</span>
            </a>
            <a href="#courses" class="sidebar-link">
                <i class="fas fa-book"></i>
                <span>الكورسات</span>
            </a>
            <a href="#instructors" class="sidebar-link">
                <i class="fas fa-chalkboard-teacher"></i>
                <span>المدربين</span>
            </a>
            <a href="#about" class="sidebar-link">
                <i class="fas fa-info-circle"></i>
                <span>من نحن</span>
            </a>
            <a href="#contact" class="sidebar-link">
                <i class="fas fa-envelope"></i>
                <span>اتصل بنا</span>
            </a>
            <hr>
            <a href="login.php" class="sidebar-link">
                <i class="fas fa-sign-in-alt"></i>
                <span>تسجيل الدخول</span>
            </a>
            <a href="register.php" class="sidebar-link">
                <i class="fas fa-user-plus"></i>
                <span>إنشاء حساب</span>
            </a>
        </nav>
    </aside>

    <!-- Main Content -->
    <main id="main-content" class="main-content-responsive">
        <!-- Hero Section -->
        <section id="home" class="hero-section-enhanced">
            <div class="container">
                <div class="row align-items-center min-vh-100">
                    <div class="col-lg-6" data-aos="fade-right" data-aos-duration="1000">
                        <div class="hero-content">
                            <h1 class="hero-title">
                                🚀 تعلم مهارات المستقبل مع أفضل المدربين
                            </h1>
                            <p class="hero-subtitle">
                                انضم إلى آلاف الطلاب واكتسب مهارات جديدة في مختلف المجالات مع مدربين محترفين ومحتوى تعليمي متميز. ابدأ رحلتك التعليمية اليوم وحقق أهدافك المهنية.
                            </p>
                            <div class="hero-buttons">
                                <a href="#courses" class="btn-responsive btn-primary-responsive btn-lg">
                                    <i class="fas fa-book"></i>
                                    <span>تصفح الكورسات</span>
                                </a>
                                <a href="register.php" class="btn-responsive btn-secondary-responsive btn-lg">
                                    <i class="fas fa-rocket"></i>
                                    <span>ابدأ التعلم الآن</span>
                                </a>
                            </div>

                            <!-- Quick Stats -->
                            <div class="hero-stats">
                                <div class="stat-item">
                                    <span class="stat-number"><?php echo number_format($stats['total_courses']); ?>+</span>
                                    <span class="stat-label">كورس</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-number"><?php echo number_format($stats['total_instructors']); ?>+</span>
                                    <span class="stat-label">مدرب</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-number"><?php echo number_format($stats['total_students']); ?>+</span>
                                    <span class="stat-label">طالب</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-6" data-aos="fade-left" data-aos-duration="1000" data-aos-delay="200">
                        <div class="hero-image">
                            <div class="hero-illustration">
                                <i class="fas fa-laptop-code hero-main-icon"></i>
                                <div class="floating-elements">
                                    <div class="floating-element" style="--delay: 0s;">
                                        <i class="fas fa-graduation-cap"></i>
                                    </div>
                                    <div class="floating-element" style="--delay: 1s;">
                                        <i class="fas fa-book"></i>
                                    </div>
                                    <div class="floating-element" style="--delay: 2s;">
                                        <i class="fas fa-lightbulb"></i>
                                    </div>
                                    <div class="floating-element" style="--delay: 3s;">
                                        <i class="fas fa-certificate"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Statistics Section -->
        <section class="stats-section-enhanced">
            <div class="container">
                <div class="stats-grid">
                    <div class="stat-card-responsive" data-aos="fade-up" data-aos-delay="100">
                        <div class="stat-icon-responsive bg-primary">
                            <i class="fas fa-book-open"></i>
                        </div>
                        <div class="stat-number-responsive counter" data-target="<?php echo $stats['total_courses']; ?>">0</div>
                        <div class="stat-label-responsive">كورس متاح</div>
                    </div>
                    <div class="stat-card-responsive" data-aos="fade-up" data-aos-delay="200">
                        <div class="stat-icon-responsive bg-success">
                            <i class="fas fa-chalkboard-teacher"></i>
                        </div>
                        <div class="stat-number-responsive counter" data-target="<?php echo $stats['total_instructors']; ?>">0</div>
                        <div class="stat-label-responsive">مدرب محترف</div>
                    </div>
                    <div class="stat-card-responsive" data-aos="fade-up" data-aos-delay="300">
                        <div class="stat-icon-responsive bg-warning">
                            <i class="fas fa-user-graduate"></i>
                        </div>
                        <div class="stat-number-responsive counter" data-target="<?php echo $stats['total_students']; ?>">0</div>
                        <div class="stat-label-responsive">طالب مسجل</div>
                    </div>
                    <div class="stat-card-responsive" data-aos="fade-up" data-aos-delay="400">
                        <div class="stat-icon-responsive bg-info">
                            <i class="fas fa-play-circle"></i>
                        </div>
                        <div class="stat-number-responsive counter" data-target="<?php echo $stats['total_videos']; ?>">0</div>
                        <div class="stat-label-responsive">فيديو تعليمي</div>
                    </div>
                    <div class="stat-card-responsive" data-aos="fade-up" data-aos-delay="500">
                        <div class="stat-icon-responsive bg-danger">
                            <i class="fas fa-certificate"></i>
                        </div>
                        <div class="stat-number-responsive counter" data-target="<?php echo $stats['total_certificates']; ?>">0</div>
                        <div class="stat-label-responsive">شهادة معتمدة</div>
                    </div>
                    <div class="stat-card-responsive" data-aos="fade-up" data-aos-delay="600">
                        <div class="stat-icon-responsive bg-secondary">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-number-responsive counter" data-target="<?php echo $stats['total_hours']; ?>">0</div>
                        <div class="stat-label-responsive">ساعة تدريب</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Featured Courses Section -->
        <?php if (!empty($featured_courses)): ?>
        <section id="courses" class="section-enhanced">
            <div class="container">
                <div class="section-header" data-aos="fade-up">
                    <h2 class="section-title">الكورسات المميزة</h2>
                    <p class="section-subtitle">اكتشف أفضل الكورسات المختارة بعناية من قبل خبرائنا</p>
                </div>

                <div class="row g-4">
                    <?php foreach ($featured_courses as $course): ?>
                    <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="<?php echo 100 + (array_search($course, $featured_courses) * 100); ?>">
                        <div class="card-responsive course-card hover-lift">
                            <div class="course-image">
                                <?php if ($course['thumbnail']): ?>
                                    <img src="<?php echo $course['thumbnail']; ?>" alt="<?php echo htmlspecialchars($course['title']); ?>" loading="lazy">
                                <?php else: ?>
                                    <div class="course-placeholder">
                                        <i class="fas fa-book"></i>
                                    </div>
                                <?php endif; ?>

                                <div class="course-overlay">
                                    <a href="course-details.php?id=<?php echo $course['id']; ?>" class="btn-responsive btn-primary-responsive">
                                        <i class="fas fa-eye"></i>
                                        <span>عرض التفاصيل</span>
                                    </a>
                                </div>

                                <?php if ($course['price'] > 0): ?>
                                    <div class="price-badge paid">
                                        <?php echo formatPrice($course['price'], $course['currency'] ?? 'USD'); ?>
                                    </div>
                                <?php else: ?>
                                    <div class="price-badge free">مجاني</div>
                                <?php endif; ?>
                            </div>

                            <div class="card-body-responsive">
                                <div class="course-meta">
                                    <span class="course-category">
                                        <i class="fas fa-tag"></i>
                                        <?php echo htmlspecialchars($course['category_name'] ?? 'عام'); ?>
                                    </span>
                                    <span class="course-level">
                                        <i class="fas fa-signal"></i>
                                        <?php echo htmlspecialchars($course['level'] ?? 'مبتدئ'); ?>
                                    </span>
                                </div>

                                <h5 class="course-title">
                                    <a href="course-details.php?id=<?php echo $course['id']; ?>">
                                        <?php echo htmlspecialchars($course['title']); ?>
                                    </a>
                                </h5>

                                <p class="course-description">
                                    <?php echo htmlspecialchars(substr($course['short_description'] ?? $course['description'], 0, 100)) . '...'; ?>
                                </p>

                                <div class="course-instructor">
                                    <i class="fas fa-user"></i>
                                    <span><?php echo htmlspecialchars($course['instructor_name']); ?></span>
                                </div>

                                <div class="course-stats">
                                    <div class="course-rating">
                                        <?php if ($course['avg_rating']): ?>
                                            <?php echo getStarRating($course['avg_rating']); ?>
                                            <span class="rating-text">(<?php echo number_format($course['avg_rating'], 1); ?>)</span>
                                        <?php else: ?>
                                            <span class="text-muted">لا توجد تقييمات</span>
                                        <?php endif; ?>
                                    </div>
                                    <div class="course-students">
                                        <i class="fas fa-users"></i>
                                        <span><?php echo number_format($course['enrolled_students']); ?> طالب</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>

                <div class="text-center mt-5" data-aos="fade-up">
                    <a href="courses.php" class="btn-responsive btn-primary-responsive btn-lg">
                        <i class="fas fa-th-large"></i>
                        <span>عرض جميع الكورسات</span>
                    </a>
                </div>
            </div>
        </section>
        <?php endif; ?>

        <!-- Categories Section -->
        <?php if (!empty($main_categories)): ?>
        <section class="section-enhanced bg-light">
            <div class="container">
                <div class="section-header" data-aos="fade-up">
                    <h2 class="section-title">التصنيفات الرئيسية</h2>
                    <p class="section-subtitle">اختر المجال الذي يناسب اهتماماتك وابدأ رحلة التعلم</p>
                </div>

                <div class="row g-4">
                    <?php foreach ($main_categories as $category): ?>
                    <div class="col-lg-3 col-md-6" data-aos="fade-up" data-aos-delay="<?php echo 100 + (array_search($category, $main_categories) * 100); ?>">
                        <div class="card-responsive category-card hover-lift">
                            <div class="category-icon">
                                <i class="<?php echo $category['icon'] ?? 'fas fa-folder'; ?>" style="color: <?php echo $category['color'] ?? '#667eea'; ?>"></i>
                            </div>
                            <div class="card-body-responsive text-center">
                                <h5 class="category-title"><?php echo htmlspecialchars($category['name']); ?></h5>
                                <p class="category-description"><?php echo htmlspecialchars($category['description'] ?? ''); ?></p>
                                <div class="category-stats">
                                    <span class="courses-count">
                                        <i class="fas fa-book"></i>
                                        <?php echo number_format($category['courses_count']); ?> كورس
                                    </span>
                                </div>
                                <a href="courses.php?category=<?php echo $category['id']; ?>" class="btn-responsive btn-outline-primary">
                                    <span>استكشف الكورسات</span>
                                    <i class="fas fa-arrow-left"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </section>
        <?php endif; ?>

        <!-- Top Instructors Section -->
        <?php if (!empty($top_instructors)): ?>
        <section id="instructors" class="section-enhanced">
            <div class="container">
                <div class="section-header" data-aos="fade-up">
                    <h2 class="section-title">أفضل المدربين</h2>
                    <p class="section-subtitle">تعلم من خبراء المجال والمدربين المحترفين</p>
                </div>

                <div class="row g-4">
                    <?php foreach ($top_instructors as $instructor): ?>
                    <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="<?php echo 100 + (array_search($instructor, $top_instructors) * 100); ?>">
                        <div class="card-responsive instructor-card hover-lift">
                            <div class="instructor-avatar">
                                <?php if ($instructor['profile_picture']): ?>
                                    <img src="<?php echo $instructor['profile_picture']; ?>" alt="<?php echo htmlspecialchars($instructor['name']); ?>" loading="lazy">
                                <?php else: ?>
                                    <div class="avatar-placeholder">
                                        <i class="fas fa-user"></i>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <div class="card-body-responsive text-center">
                                <h5 class="instructor-name"><?php echo htmlspecialchars($instructor['name']); ?></h5>
                                <p class="instructor-title"><?php echo htmlspecialchars($instructor['specialization'] ?? 'مدرب محترف'); ?></p>

                                <div class="instructor-rating">
                                    <?php if ($instructor['avg_rating']): ?>
                                        <?php echo getStarRating($instructor['avg_rating']); ?>
                                        <span class="rating-text">(<?php echo number_format($instructor['avg_rating'], 1); ?>)</span>
                                    <?php else: ?>
                                        <span class="text-muted">جديد</span>
                                    <?php endif; ?>
                                </div>

                                <div class="instructor-stats">
                                    <div class="stat-item">
                                        <span class="stat-number"><?php echo number_format($instructor['total_courses']); ?></span>
                                        <span class="stat-label">كورس</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-number"><?php echo number_format($instructor['total_students']); ?></span>
                                        <span class="stat-label">طالب</span>
                                    </div>
                                </div>

                                <a href="instructor-profile.php?id=<?php echo $instructor['id']; ?>" class="btn-responsive btn-outline-primary">
                                    <span>عرض الملف الشخصي</span>
                                    <i class="fas fa-arrow-left"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </section>
        <?php endif; ?>

        <!-- Student Testimonials Section -->
        <?php if (!empty($student_testimonials)): ?>
        <section class="section-enhanced bg-light">
            <div class="container">
                <div class="section-header" data-aos="fade-up">
                    <h2 class="section-title">آراء الطلاب</h2>
                    <p class="section-subtitle">اكتشف تجارب طلابنا وقصص نجاحهم</p>
                </div>

                <div class="row g-4">
                    <?php foreach (array_slice($student_testimonials, 0, 3) as $testimonial): ?>
                    <div class="col-lg-4" data-aos="fade-up" data-aos-delay="<?php echo 100 + (array_search($testimonial, $student_testimonials) * 100); ?>">
                        <div class="card-responsive testimonial-card">
                            <div class="card-body-responsive">
                                <div class="testimonial-rating">
                                    <?php echo getStarRating($testimonial['rating']); ?>
                                </div>

                                <blockquote class="testimonial-text">
                                    "<?php echo htmlspecialchars($testimonial['review_text']); ?>"
                                </blockquote>

                                <div class="testimonial-author">
                                    <div class="author-info">
                                        <h6 class="author-name"><?php echo htmlspecialchars($testimonial['student_name']); ?></h6>
                                        <p class="author-course">كورس: <?php echo htmlspecialchars($testimonial['course_title']); ?></p>
                                    </div>
                                </div>

                                <div class="testimonial-date">
                                    <small class="text-muted"><?php echo timeAgo($testimonial['created_at']); ?></small>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </section>
        <?php endif; ?>

        <!-- Features Section -->
        <section class="section-enhanced">
            <div class="container">
                <div class="section-header" data-aos="fade-up">
                    <h2 class="section-title">لماذا تختار منصتنا؟</h2>
                    <p class="section-subtitle">نقدم تجربة تعليمية متكاملة ومتطورة</p>
                </div>

                <div class="row g-4">
                    <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="100">
                        <div class="card-responsive feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-video"></i>
                            </div>
                            <div class="card-body-responsive">
                                <h5 class="feature-title">جلسات مباشرة تفاعلية</h5>
                                <p class="feature-description">تفاعل مباشر مع المدربين من خلال جلسات فيديو عالية الجودة مع إمكانية المشاركة والنقاش</p>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="200">
                        <div class="card-responsive feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-certificate"></i>
                            </div>
                            <div class="card-body-responsive">
                                <h5 class="feature-title">شهادات معتمدة</h5>
                                <p class="feature-description">احصل على شهادات معتمدة ومعترف بها دولياً عند إتمام الكورسات بنجاح</p>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="300">
                        <div class="card-responsive feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-mobile-alt"></i>
                            </div>
                            <div class="card-body-responsive">
                                <h5 class="feature-title">تعلم في أي مكان</h5>
                                <p class="feature-description">منصة متجاوبة تعمل على جميع الأجهزة والهواتف الذكية مع تطبيق جوال</p>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="400">
                        <div class="card-responsive feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="card-body-responsive">
                                <h5 class="feature-title">مجتمع تعليمي نشط</h5>
                                <p class="feature-description">انضم لمجتمع من المتعلمين وتبادل الخبرات والمعرفة مع زملائك</p>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="500">
                        <div class="card-responsive feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="card-body-responsive">
                                <h5 class="feature-title">مرونة في التوقيت</h5>
                                <p class="feature-description">تعلم في الوقت الذي يناسبك مع محتوى مسجل ومتاح على مدار الساعة</p>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="600">
                        <div class="card-responsive feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-headset"></i>
                            </div>
                            <div class="card-body-responsive">
                                <h5 class="feature-title">دعم فني متميز</h5>
                                <p class="feature-description">فريق دعم فني متخصص متاح لمساعدتك في أي وقت لضمان أفضل تجربة تعليمية</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Call to Action Section -->
        <section class="cta-section">
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-lg-8 text-center" data-aos="fade-up">
                        <h2 class="cta-title">ابدأ رحلتك التعليمية اليوم</h2>
                        <p class="cta-subtitle">انضم إلى آلاف الطلاب الذين حققوا أهدافهم المهنية من خلال منصتنا</p>
                        <div class="cta-buttons">
                            <a href="register.php" class="btn-responsive btn-primary-responsive btn-lg">
                                <i class="fas fa-rocket"></i>
                                <span>إنشاء حساب مجاني</span>
                            </a>
                            <a href="courses.php" class="btn-responsive btn-secondary-responsive btn-lg">
                                <i class="fas fa-search"></i>
                                <span>استكشف الكورسات</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="footer-responsive" id="contact">
        <div class="container">
            <div class="footer-content">
                <div class="row g-4">
                    <div class="col-lg-4 col-md-6">
                        <div class="footer-section">
                            <div class="footer-logo">
                                <img src="<?php echo SITE_LOGO; ?>" alt="<?php echo SITE_NAME; ?>" loading="lazy">
                                <span><?php echo SITE_NAME; ?></span>
                            </div>
                            <p class="footer-description">
                                منصة تعليمية متقدمة تهدف إلى تقديم أفضل تجربة تعلم إلكتروني مع مدربين محترفين ومحتوى عالي الجودة.
                            </p>
                            <div class="social-links">
                                <a href="#" class="social-link" aria-label="فيسبوك">
                                    <i class="fab fa-facebook-f"></i>
                                </a>
                                <a href="#" class="social-link" aria-label="تويتر">
                                    <i class="fab fa-twitter"></i>
                                </a>
                                <a href="#" class="social-link" aria-label="لينكد إن">
                                    <i class="fab fa-linkedin-in"></i>
                                </a>
                                <a href="#" class="social-link" aria-label="يوتيوب">
                                    <i class="fab fa-youtube"></i>
                                </a>
                                <a href="#" class="social-link" aria-label="إنستغرام">
                                    <i class="fab fa-instagram"></i>
                                </a>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-2 col-md-6">
                        <div class="footer-section">
                            <h5 class="footer-title">روابط سريعة</h5>
                            <ul class="footer-links">
                                <li><a href="#home">الرئيسية</a></li>
                                <li><a href="#courses">الكورسات</a></li>
                                <li><a href="#instructors">المدربين</a></li>
                                <li><a href="about.php">من نحن</a></li>
                                <li><a href="contact.php">اتصل بنا</a></li>
                            </ul>
                        </div>
                    </div>

                    <div class="col-lg-2 col-md-6">
                        <div class="footer-section">
                            <h5 class="footer-title">الخدمات</h5>
                            <ul class="footer-links">
                                <li><a href="courses.php">جميع الكورسات</a></li>
                                <li><a href="instructors.php">المدربين</a></li>
                                <li><a href="certificates.php">الشهادات</a></li>
                                <li><a href="support.php">الدعم الفني</a></li>
                                <li><a href="faq.php">الأسئلة الشائعة</a></li>
                            </ul>
                        </div>
                    </div>

                    <div class="col-lg-2 col-md-6">
                        <div class="footer-section">
                            <h5 class="footer-title">الحساب</h5>
                            <ul class="footer-links">
                                <li><a href="login.php">تسجيل الدخول</a></li>
                                <li><a href="register.php">إنشاء حساب</a></li>
                                <li><a href="join-request.php">طلب انضمام</a></li>
                                <li><a href="forgot-password.php">نسيت كلمة المرور</a></li>
                            </ul>
                        </div>
                    </div>

                    <div class="col-lg-2 col-md-6">
                        <div class="footer-section">
                            <h5 class="footer-title">القانونية</h5>
                            <ul class="footer-links">
                                <li><a href="privacy-policy.php">سياسة الخصوصية</a></li>
                                <li><a href="terms-of-service.php">شروط الخدمة</a></li>
                                <li><a href="refund-policy.php">سياسة الاسترداد</a></li>
                                <li><a href="cookie-policy.php">سياسة الكوكيز</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <div class="footer-bottom">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <p class="copyright">
                            &copy; <?php echo date('Y'); ?> <?php echo SITE_NAME; ?>. جميع الحقوق محفوظة.
                        </p>
                    </div>
                    <div class="col-md-6">
                        <div class="footer-links-inline">
                            <a href="privacy-policy.php">الخصوصية</a>
                            <a href="terms-of-service.php">الشروط</a>
                            <a href="sitemap.php">خريطة الموقع</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Back to Top Button -->
    <button class="back-to-top" id="backToTop" aria-label="العودة إلى الأعلى">
        <i class="fas fa-chevron-up"></i>
    </button>

    <!-- Search Modal -->
    <div class="modal fade" id="searchModal" tabindex="-1" aria-labelledby="searchModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="searchModalLabel">البحث في الكورسات</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
                </div>
                <div class="modal-body">
                    <form class="search-form" action="search.php" method="GET">
                        <div class="input-group">
                            <input type="text" class="form-control" name="q" placeholder="ابحث عن كورس، مدرب، أو موضوع..." autocomplete="off">
                            <button class="btn btn-primary" type="submit">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </form>
                    <div class="search-suggestions mt-3">
                        <h6>اقتراحات البحث:</h6>
                        <div class="suggestion-tags">
                            <span class="suggestion-tag">برمجة</span>
                            <span class="suggestion-tag">تصميم</span>
                            <span class="suggestion-tag">تسويق</span>
                            <span class="suggestion-tag">إدارة</span>
                            <span class="suggestion-tag">لغات</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript Libraries -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js" integrity="sha384-C6RzsynM9kWDrMNeT87bh95OGNyZPhcTNXj1NW7RuBCsyN/o0jlpcV8Qyq46cDfL" crossorigin="anonymous"></script>
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>

    <!-- Custom JavaScript -->
    <script src="assets/js/main.js"></script>
    <script src="assets/js/responsive.js"></script>

    <!-- PWA Service Worker -->
    <script>
        // Register Service Worker
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', function() {
                navigator.serviceWorker.register('/sw.js')
                    .then(function(registration) {
                        console.log('ServiceWorker registration successful');
                    })
                    .catch(function(err) {
                        console.log('ServiceWorker registration failed: ', err);
                    });
            });
        }

        // Initialize AOS
        AOS.init({
            duration: 1000,
            once: true,
            offset: 100
        });

        // Counter Animation
        function animateCounters() {
            const counters = document.querySelectorAll('.counter');

            counters.forEach(counter => {
                const target = parseInt(counter.getAttribute('data-target'));
                const increment = target / 100;
                let current = 0;

                const updateCounter = () => {
                    if (current < target) {
                        current += increment;
                        counter.textContent = Math.floor(current);
                        requestAnimationFrame(updateCounter);
                    } else {
                        counter.textContent = target;
                    }
                };

                updateCounter();
            });
        }

        // Intersection Observer for counters
        const observerOptions = {
            threshold: 0.5,
            rootMargin: '0px 0px -100px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    animateCounters();
                    observer.unobserve(entry.target);
                }
            });
        }, observerOptions);

        // Observe stats section
        const statsSection = document.querySelector('.stats-section-enhanced');
        if (statsSection) {
            observer.observe(statsSection);
        }

        // Loading Screen
        window.addEventListener('load', function() {
            const loadingScreen = document.getElementById('loading-screen');
            if (loadingScreen) {
                loadingScreen.style.opacity = '0';
                setTimeout(() => {
                    loadingScreen.style.display = 'none';
                }, 500);
            }
        });

        // Back to Top Button
        const backToTopBtn = document.getElementById('backToTop');

        window.addEventListener('scroll', function() {
            if (window.pageYOffset > 300) {
                backToTopBtn.classList.add('show');
            } else {
                backToTopBtn.classList.remove('show');
            }
        });

        backToTopBtn.addEventListener('click', function() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Search functionality
        const searchInput = document.querySelector('.search-form input');
        if (searchInput) {
            searchInput.addEventListener('input', function() {
                const query = this.value.trim();
                if (query.length > 2) {
                    // Implement live search suggestions here
                    console.log('Searching for:', query);
                }
            });
        }

        // Lazy loading for images
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        imageObserver.unobserve(img);
                    }
                });
            });

            document.querySelectorAll('img[data-src]').forEach(img => {
                imageObserver.observe(img);
            });
        }

        // Performance monitoring
        if ('performance' in window) {
            window.addEventListener('load', function() {
                setTimeout(function() {
                    const perfData = performance.getEntriesByType('navigation')[0];
                    console.log('Page load time:', perfData.loadEventEnd - perfData.loadEventStart, 'ms');
                }, 0);
            });
        }
    </script>

    <!-- Google Analytics (if configured) -->
    <?php if (defined('GOOGLE_ANALYTICS_ID') && GOOGLE_ANALYTICS_ID): ?>
    <script async src="https://www.googletagmanager.com/gtag/js?id=<?php echo GOOGLE_ANALYTICS_ID; ?>"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', '<?php echo GOOGLE_ANALYTICS_ID; ?>');
    </script>
    <?php endif; ?>
</body>
</html>
