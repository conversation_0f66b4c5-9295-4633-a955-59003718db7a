<?php
/**
 * إضافة سجل الأنشطة التجريبي
 * Add Sample Activity Logs
 */

require_once 'config/database.php';

echo "<!DOCTYPE html>";
echo "<html dir='rtl' lang='ar'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>إضافة سجل الأنشطة</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "</head>";
echo "<body>";
echo "<div class='container mt-4'>";
echo "<h2>📋 إضافة سجل الأنشطة التجريبي</h2>";

try {
    // إنشاء جدول activity_logs إذا لم يكن موجوداً
    $createActivityLogs = "
    CREATE TABLE IF NOT EXISTS activity_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT DEFAULT NULL,
        action VARCHAR(255) NOT NULL,
        description TEXT,
        ip_address VARCHAR(45),
        user_agent TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
        INDEX idx_user_id (user_id),
        INDEX idx_action (action),
        INDEX idx_created_at (created_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $conn->exec($createActivityLogs);
    echo "<div class='alert alert-success'>✅ تم إنشاء جدول activity_logs</div>";
    
    // الحصول على معرفات المستخدمين
    $stmt = $conn->query("SELECT id, name, role FROM users");
    $users = $stmt->fetchAll();
    
    if (!empty($users)) {
        $activities = [
            ['action' => 'login', 'description' => 'تسجيل دخول ناجح'],
            ['action' => 'logout', 'description' => 'تسجيل خروج'],
            ['action' => 'course_created', 'description' => 'إنشاء كورس جديد'],
            ['action' => 'session_created', 'description' => 'إنشاء جلسة جديدة'],
            ['action' => 'student_approved', 'description' => 'الموافقة على طالب جديد'],
            ['action' => 'profile_updated', 'description' => 'تحديث الملف الشخصي'],
            ['action' => 'password_changed', 'description' => 'تغيير كلمة المرور'],
            ['action' => 'course_enrolled', 'description' => 'التسجيل في كورس'],
            ['action' => 'session_joined', 'description' => 'الانضمام لجلسة'],
            ['action' => 'report_generated', 'description' => 'إنشاء تقرير']
        ];
        
        $insertActivity = $conn->prepare("
            INSERT INTO activity_logs (user_id, action, description, ip_address, user_agent, created_at) 
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        
        $ipAddresses = ['*************', '*************', '*************', '*********'];
        $userAgents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36'
        ];
        
        // إضافة 50 نشاط تجريبي
        for ($i = 0; $i < 50; $i++) {
            $user = $users[array_rand($users)];
            $activity = $activities[array_rand($activities)];
            $ip = $ipAddresses[array_rand($ipAddresses)];
            $userAgent = $userAgents[array_rand($userAgents)];
            $createdAt = date('Y-m-d H:i:s', strtotime('-' . rand(1, 30) . ' days -' . rand(0, 23) . ' hours'));
            
            $description = $activity['description'] . ' - ' . $user['name'];
            
            $insertActivity->execute([
                $user['id'],
                $activity['action'],
                $description,
                $ip,
                $userAgent,
                $createdAt
            ]);
        }
        
        echo "<div class='alert alert-success'>✅ تم إضافة 50 نشاط تجريبي</div>";
        
        // إحصائيات الأنشطة
        $stmt = $conn->query("
            SELECT action, COUNT(*) as count 
            FROM activity_logs 
            GROUP BY action 
            ORDER BY count DESC
        ");
        $actionStats = $stmt->fetchAll();
        
        echo "<h4>📊 إحصائيات الأنشطة:</h4>";
        echo "<div class='row'>";
        foreach ($actionStats as $stat) {
            echo "<div class='col-md-4 mb-2'>";
            echo "<div class='card'>";
            echo "<div class='card-body text-center'>";
            echo "<h5>{$stat['count']}</h5>";
            echo "<p>{$stat['action']}</p>";
            echo "</div>";
            echo "</div>";
            echo "</div>";
        }
        echo "</div>";
        
        // آخر الأنشطة
        $stmt = $conn->query("
            SELECT al.*, u.name as user_name 
            FROM activity_logs al 
            LEFT JOIN users u ON al.user_id = u.id 
            ORDER BY al.created_at DESC 
            LIMIT 10
        ");
        $recentActivities = $stmt->fetchAll();
        
        echo "<h4>🕒 آخر الأنشطة:</h4>";
        echo "<div class='table-responsive'>";
        echo "<table class='table table-striped'>";
        echo "<thead>";
        echo "<tr><th>المستخدم</th><th>النشاط</th><th>الوصف</th><th>التاريخ</th></tr>";
        echo "</thead>";
        echo "<tbody>";
        
        foreach ($recentActivities as $activity) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($activity['user_name'] ?? 'غير محدد') . "</td>";
            echo "<td>" . htmlspecialchars($activity['action']) . "</td>";
            echo "<td>" . htmlspecialchars($activity['description']) . "</td>";
            echo "<td>" . date('Y-m-d H:i', strtotime($activity['created_at'])) . "</td>";
            echo "</tr>";
        }
        
        echo "</tbody>";
        echo "</table>";
        echo "</div>";
    }
    
    echo "<div class='mt-4'>";
    echo "<a href='admin/activity-logs.php' class='btn btn-primary'>عرض سجل الأنشطة</a> ";
    echo "<a href='admin/dashboard.php' class='btn btn-success'>لوحة التحكم</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>";
    echo "<h5>❌ حدث خطأ</h5>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "</div>";
echo "</body>";
echo "</html>";
?>
