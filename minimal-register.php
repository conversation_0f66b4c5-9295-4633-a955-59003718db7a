<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'config/database.php';

$message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = trim($_POST['name']);
    $email = trim($_POST['email']);
    $phone = trim($_POST['phone']);
    $password = $_POST['password'];
    
    echo "<h3>البيانات المستلمة:</h3>";
    echo "<p>الاسم: " . htmlspecialchars($name) . "</p>";
    echo "<p>البريد: " . htmlspecialchars($email) . "</p>";
    echo "<p>الهاتف: " . htmlspecialchars($phone) . "</p>";
    echo "<p>كلمة المرور: " . (strlen($password) > 0 ? "تم إدخالها" : "فارغة") . "</p>";
    
    if (empty($name) || empty($email) || empty($phone) || empty($password)) {
        $message = '<div style="color: red;">جميع الحقول مطلوبة</div>';
    } else {
        try {
            // فحص البريد الإلكتروني
            $stmt = $conn->prepare("SELECT id FROM users WHERE email = ?");
            $stmt->execute([$email]);
            
            if ($stmt->rowCount() > 0) {
                $message = '<div style="color: red;">البريد الإلكتروني مسجل مسبقاً</div>';
            } else {
                // إنشاء المستخدم
                $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
                $username = explode('@', $email)[0];
                
                $stmt = $conn->prepare("INSERT INTO users (name, email, phone, username, password, role, status) VALUES (?, ?, ?, ?, ?, 'student', 'pending')");
                $result = $stmt->execute([$name, $email, $phone, $username, $hashedPassword]);
                
                if ($result) {
                    $user_id = $conn->lastInsertId();
                    $message = '<div style="color: green;">✅ تم التسجيل بنجاح! معرف المستخدم: ' . $user_id . '</div>';
                    
                    // إنشاء طلب انضمام
                    try {
                        $stmt = $conn->prepare("INSERT INTO join_requests (name, email, phone, status) VALUES (?, ?, ?, 'pending')");
                        $stmt->execute([$name, $email, $phone]);
                        $message .= '<div style="color: green;">✅ تم إنشاء طلب الانضمام</div>';
                    } catch (Exception $e) {
                        $message .= '<div style="color: orange;">⚠️ فشل في إنشاء طلب الانضمام: ' . $e->getMessage() . '</div>';
                    }
                } else {
                    $message = '<div style="color: red;">❌ فشل في إنشاء المستخدم</div>';
                }
            }
        } catch (Exception $e) {
            $message = '<div style="color: red;">❌ خطأ: ' . $e->getMessage() . '</div>';
        }
    }
}
?>
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل مبسط</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .form-container { max-width: 500px; margin: 0 auto; padding: 20px; border: 1px solid #ccc; border-radius: 10px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input[type="text"], input[type="email"], input[type="password"] { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; box-sizing: border-box; }
        .btn { background: #007bff; color: white; padding: 12px 30px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px; }
        .btn:hover { background: #0056b3; }
    </style>
</head>
<body>
    <div class="form-container">
        <h2>تسجيل حساب جديد - نسخة مبسطة</h2>
        
        <?php if ($message): ?>
            <?php echo $message; ?>
        <?php endif; ?>
        
        <form method="POST" action="">
            <div class="form-group">
                <label for="name">الاسم الكامل:</label>
                <input type="text" id="name" name="name" value="<?php echo isset($_POST['name']) ? htmlspecialchars($_POST['name']) : ''; ?>" required>
            </div>
            
            <div class="form-group">
                <label for="email">البريد الإلكتروني:</label>
                <input type="email" id="email" name="email" value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>" required>
            </div>
            
            <div class="form-group">
                <label for="phone">رقم الهاتف:</label>
                <input type="text" id="phone" name="phone" value="<?php echo isset($_POST['phone']) ? htmlspecialchars($_POST['phone']) : ''; ?>" required>
            </div>
            
            <div class="form-group">
                <label for="password">كلمة المرور:</label>
                <input type="password" id="password" name="password" required>
            </div>
            
            <div class="form-group">
                <button type="submit" class="btn">تسجيل</button>
            </div>
        </form>
        
        <p><a href="index.php">العودة للرئيسية</a></p>
    </div>
</body>
</html>
